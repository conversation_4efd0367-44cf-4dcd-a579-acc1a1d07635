<?php
/**
 * Simple Income Diagnostic
 * Simplified diagnostic script to avoid SQL compatibility issues
 */

require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../config/config.php';

// Initialize classes
$db = Database::getInstance();
$pvSystem = new PVSystem();
$config = Config::getInstance();

echo "<h2>Simple Income Generation Diagnostic</h2>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// Test dates
$weekStart = date('Y-m-d', strtotime('monday last week'));
$weekEnd = date('Y-m-d', strtotime('sunday last week'));

echo "<h3>System Configuration:</h3>";
echo "<ul>";
echo "<li>Week Start: {$weekStart}</li>";
echo "<li>Week End: {$weekEnd}</li>";
echo "<li>PV Rate: ₹" . $config->getPVRate() . "</li>";
echo "<li>Weekly Capping: ₹" . number_format($config->get('weekly_capping', 130000)) . "</li>";
echo "</ul>";

// Check basic table existence
echo "<h3>1. Database Tables Check:</h3>";
$requiredTables = [
    'users' => 'User accounts',
    'pv_transactions' => 'PV transactions',
    'pv_usage_tracking' => 'PV usage tracking',
    'weekly_income_logs' => 'Income logs',
    'weekly_income_reports' => 'Income reports',
    'wallet' => 'User wallets',
    'wallet_transactions' => 'Wallet transactions'
];

foreach ($requiredTables as $table => $description) {
    try {
        $stmt = $db->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->fetch()) {
            echo "<p class='success'>✓ {$description} table exists</p>";
        } else {
            echo "<p class='error'>✗ {$description} table missing</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error checking {$description} table: " . $e->getMessage() . "</p>";
    }
}

// Check active users
echo "<h3>2. Active Users:</h3>";
try {
    $userStmt = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $userCount = $userStmt->fetch()['count'];
    echo "<p>Active users: <strong>{$userCount}</strong></p>";
    
    if ($userCount == 0) {
        echo "<p class='warning'>⚠ No active users found!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking users: " . $e->getMessage() . "</p>";
}

// Check PV transactions
echo "<h3>3. PV Transactions:</h3>";
try {
    $pvStmt = $db->query("SELECT COUNT(*) as count, SUM(pv_amount) as total FROM pv_transactions");
    $pvData = $pvStmt->fetch();
    echo "<p>Total PV transactions: <strong>{$pvData['count']}</strong></p>";
    echo "<p>Total PV amount: <strong>" . number_format($pvData['total'] ?? 0, 2) . "</strong></p>";
    
    if ($pvData['count'] == 0) {
        echo "<p class='warning'>⚠ No PV transactions found!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking PV transactions: " . $e->getMessage() . "</p>";
}

// Check PV usage tracking
echo "<h3>4. PV Usage Tracking:</h3>";
try {
    $trackingStmt = $db->query("SELECT COUNT(*) as count FROM pv_usage_tracking");
    $trackingCount = $trackingStmt->fetch()['count'];
    echo "<p>PV tracking records: <strong>{$trackingCount}</strong></p>";
    
    if ($trackingCount == 0) {
        echo "<p class='warning'>⚠ No PV usage tracking records found! This is likely the main issue.</p>";
        echo "<p><strong>Solution:</strong> <a href='fix-pv-tracking.php' target='_blank'>Run PV Tracking Fix</a></p>";
    } else {
        // Check available PV
        $availableStmt = $db->query("SELECT COUNT(*) as count, SUM(remaining_amount) as total FROM pv_usage_tracking WHERE remaining_amount > 0");
        $availableData = $availableStmt->fetch();
        echo "<p>Available PV records: <strong>{$availableData['count']}</strong></p>";
        echo "<p>Total available PV: <strong>" . number_format($availableData['total'] ?? 0, 2) . "</strong></p>";
        
        if ($availableData['total'] == 0) {
            echo "<p class='warning'>⚠ No available PV found for processing!</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking PV tracking: " . $e->getMessage() . "</p>";
}

// Check wallets
echo "<h3>5. Wallet System:</h3>";
try {
    $walletStmt = $db->query("SELECT COUNT(*) as count, SUM(balance) as total FROM wallet");
    $walletData = $walletStmt->fetch();
    echo "<p>Wallet records: <strong>{$walletData['count']}</strong></p>";
    echo "<p>Total wallet balance: <strong>₹" . number_format($walletData['total'] ?? 0, 2) . "</strong></p>";
    
    // Check for users without wallets
    $missingWalletStmt = $db->query("
        SELECT COUNT(*) as count 
        FROM users u 
        LEFT JOIN wallet w ON u.user_id = w.user_id 
        WHERE u.status = 'active' AND w.user_id IS NULL
    ");
    $missingWallets = $missingWalletStmt->fetch()['count'];
    
    if ($missingWallets > 0) {
        echo "<p class='warning'>⚠ {$missingWallets} active users without wallet records!</p>";
    } else {
        echo "<p class='success'>✓ All active users have wallet records</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking wallets: " . $e->getMessage() . "</p>";
}

// Check recent income logs
echo "<h3>6. Recent Income Processing:</h3>";
try {
    $recentStmt = $db->query("
        SELECT COUNT(*) as count, SUM(income_amount) as total 
        FROM weekly_income_logs 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $recentData = $recentStmt->fetch();
    echo "<p>Income logs (last 30 days): <strong>{$recentData['count']}</strong></p>";
    echo "<p>Total income distributed: <strong>₹" . number_format($recentData['total'] ?? 0, 2) . "</strong></p>";
    
    if ($recentData['count'] == 0) {
        echo "<p class='warning'>⚠ No recent income processing found!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking income logs: " . $e->getMessage() . "</p>";
}

// Test a simple user lookup
echo "<h3>7. Sample User Test:</h3>";
try {
    $sampleStmt = $db->query("
        SELECT u.user_id, u.full_name 
        FROM users u 
        WHERE u.status = 'active' 
        LIMIT 1
    ");
    $sampleUser = $sampleStmt->fetch();
    
    if ($sampleUser) {
        echo "<p>Testing with user: <strong>{$sampleUser['full_name']} ({$sampleUser['user_id']})</strong></p>";
        
        // Check if user has PV transactions
        $userPVStmt = $db->prepare("SELECT COUNT(*) as count FROM pv_transactions WHERE user_id = ?");
        $userPVStmt->execute([$sampleUser['user_id']]);
        $userPVCount = $userPVStmt->fetch()['count'];
        
        echo "<p>User's PV transactions: <strong>{$userPVCount}</strong></p>";
        
        if ($userPVCount > 0) {
            // Test available PV calculation
            try {
                $availablePV = $pvSystem->getAvailablePVForWeek($sampleUser['user_id'], $weekStart);
                echo "<p>Available Left PV: <strong>" . number_format($availablePV['left_pv'], 2) . "</strong></p>";
                echo "<p>Available Right PV: <strong>" . number_format($availablePV['right_pv'], 2) . "</strong></p>";
                
                $matchedPV = min($availablePV['left_pv'], $availablePV['right_pv']);
                echo "<p>Potential matched PV: <strong>" . number_format($matchedPV, 2) . "</strong></p>";
                
                if ($matchedPV > 0) {
                    $potentialIncome = $matchedPV * $config->getPVRate() * 0.85; // After 15% deductions
                    echo "<p class='success'>✓ Potential net income: <strong>₹" . number_format($potentialIncome, 2) . "</strong></p>";
                } else {
                    echo "<p class='warning'>⚠ No matched PV available for income generation</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>✗ Error calculating available PV: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='warning'>⚠ User has no PV transactions</p>";
        }
        
    } else {
        echo "<p class='warning'>⚠ No active users found for testing</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error in sample user test: " . $e->getMessage() . "</p>";
}

// Summary and recommendations
echo "<h3>8. Summary & Next Steps:</h3>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
echo "<h4>Quick Fix Actions:</h4>";
echo "<ol>";
echo "<li><strong><a href='fix-pv-tracking.php' target='_blank'>Fix PV Tracking</a></strong> - Resolves most common issues</li>";
echo "<li><strong><a href='test-income-generation.php' target='_blank'>Test Income Generation</a></strong> - Comprehensive system test</li>";
echo "<li><strong>Generate Report</strong> - Go back to Weekly Income Reports and generate a report</li>";
echo "</ol>";

echo "<h4>Common Issues & Solutions:</h4>";
echo "<ul>";
echo "<li><strong>No PV tracking records:</strong> Run the PV tracking fix</li>";
echo "<li><strong>No available PV:</strong> Users need PV on both left and right sides</li>";
echo "<li><strong>Missing wallets:</strong> Fix script will create them automatically</li>";
echo "<li><strong>No income generated:</strong> Check if users have sufficient matching PV</li>";
echo "</ul>";
echo "</div>";

echo "<p class='info'><strong>Diagnostic Complete!</strong> Use the links above to fix any identified issues.</p>";
?>
