<?php
/**
 * Simple Report Generator
 * Simplified version for testing report generation
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/WeeklyDateHelper.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$pvSystem = new PVSystem();
$config = Config::getInstance();
$db = Database::getInstance();

// Handle form submission
$message = '';
$messageType = '';
$result = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'generate_report') {
    $weekStart = $_POST['week_start'] ?? '';
    $weekEnd = $_POST['week_end'] ?? '';
    
    if ($weekStart && $weekEnd) {
        try {
            echo "<h3>🔄 Processing Report Generation...</h3>";
            echo "<p>Week: {$weekStart} to {$weekEnd}</p>";
            
            // Validate dates
            $startDate = new DateTime($weekStart);
            $endDate = new DateTime($weekEnd);
            
            if ($startDate > $endDate) {
                throw new Exception("Start date cannot be after end date.");
            }
            
            echo "<p>✅ Date validation passed</p>";
            
            // Check for active users
            $userCountStmt = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
            $userCount = $userCountStmt->fetch()['count'];
            echo "<p>Active users found: {$userCount}</p>";
            
            if ($userCount == 0) {
                throw new Exception("No active users found for processing.");
            }
            
            // Generate unique report ID
            require_once '../includes/functions.php';
            $reportId = 'SIMPLE_' . date('YmdHis') . '_' . uniqid();
            echo "<p>Report ID: {$reportId}</p>";
            
            // Call runWeeklyMatching
            echo "<p>🔄 Calling runWeeklyMatching...</p>";
            $result = $pvSystem->runWeeklyMatching($weekStart, $weekEnd, false, $reportId);
            
            if ($result !== false && is_array($result)) {
                echo "<h3 style='color: green;'>✅ Report Generation Successful!</h3>";
                echo "<div style='background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4>📊 Results Summary:</h4>";
                echo "<ul>";
                echo "<li><strong>Users Processed:</strong> " . ($result['processed'] ?? 0) . "</li>";
                echo "<li><strong>Users with Income:</strong> " . ($result['users_with_income'] ?? 0) . "</li>";
                echo "<li><strong>Users Skipped:</strong> " . ($result['skipped_users'] ?? 0) . "</li>";
                echo "<li><strong>Total Gross Income:</strong> ₹" . number_format($result['total_gross_income'] ?? 0, 2) . "</li>";
                echo "<li><strong>Total Service Charge:</strong> ₹" . number_format($result['total_service_charge'] ?? 0, 2) . "</li>";
                echo "<li><strong>Total TDS:</strong> ₹" . number_format($result['total_tds_amount'] ?? 0, 2) . "</li>";
                echo "<li><strong>Total Net Income:</strong> ₹" . number_format($result['total_income'] ?? 0, 2) . "</li>";
                echo "<li><strong>Total Capping Applied:</strong> ₹" . number_format($result['total_capping'] ?? 0, 2) . "</li>";
                if (isset($result['error_count'])) {
                    echo "<li><strong>Errors:</strong> {$result['error_count']}</li>";
                }
                echo "</ul>";
                echo "</div>";
                
                // Check if report was saved
                $reportCheckStmt = $db->prepare("SELECT * FROM weekly_income_reports WHERE week_start_date = ? ORDER BY id DESC LIMIT 1");
                $reportCheckStmt->execute([$weekStart]);
                $savedReport = $reportCheckStmt->fetch();
                
                if ($savedReport) {
                    echo "<p style='color: green;'>✅ Report saved to database successfully!</p>";
                    echo "<p>Report Status: " . ucfirst($savedReport['report_status']) . "</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Report not found in database</p>";
                }
                
                $message = "Report generated successfully!";
                $messageType = 'success';
            } else {
                echo "<h3 style='color: red;'>❌ Report Generation Failed!</h3>";
                echo "<p>runWeeklyMatching returned: " . print_r($result, true) . "</p>";
                $message = "Report generation failed. Check the output above for details.";
                $messageType = 'danger';
            }
            
        } catch (Exception $e) {
            echo "<h3 style='color: red;'>❌ Error During Report Generation</h3>";
            echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
            $message = "Error: " . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = "Please provide both start and end dates.";
        $messageType = 'warning';
    }
}

// Get default dates
$previousWeek = WeeklyDateHelper::getPreviousWeek();
$defaultStart = $previousWeek['start'];
$defaultEnd = $previousWeek['end'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Report Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-chart-line me-2"></i>Simple Report Generator</h4>
                        <p class="mb-0 text-muted">Simplified interface for testing weekly income report generation</p>
                    </div>
                    <div class="card-body">
                        
                        <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                            <input type="hidden" name="action" value="generate_report">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="week_start" class="form-label">Week Start Date</label>
                                    <input type="date" class="form-control" id="week_start" name="week_start" 
                                           value="<?php echo $defaultStart; ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="week_end" class="form-label">Week End Date</label>
                                    <input type="date" class="form-control" id="week_end" name="week_end" 
                                           value="<?php echo $defaultEnd; ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    This will generate a weekly income report for the specified period using Saturday-Friday weeks.
                                </small>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-play me-1"></i>Generate Report
                                </button>
                                <a href="weekly-income-reports.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to Main Reports
                                </a>
                            </div>
                        </form>
                        
                    </div>
                </div>
                
                <!-- Debug Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-bug me-2"></i>Debug Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>System Status</h6>
                                <ul class="list-unstyled">
                                    <li>✅ PHP Version: <?php echo PHP_VERSION; ?></li>
                                    <li>✅ Current User: <?php echo $currentUser['full_name']; ?></li>
                                    <li>✅ Database Connected: <?php echo $db ? 'Yes' : 'No'; ?></li>
                                    <li>✅ PVSystem Loaded: <?php echo class_exists('PVSystem') ? 'Yes' : 'No'; ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Week Information</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Current Week:</strong> <?php echo WeeklyDateHelper::getCurrentWeek()['start']; ?> to <?php echo WeeklyDateHelper::getCurrentWeek()['end']; ?></li>
                                    <li><strong>Previous Week:</strong> <?php echo $defaultStart; ?> to <?php echo $defaultEnd; ?></li>
                                    <li><strong>Processing Day:</strong> <?php echo WeeklyDateHelper::getProcessingDay(); ?> (Friday)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-calculate week end date when start date changes
        document.getElementById('week_start').addEventListener('change', function() {
            if (this.value) {
                const startDate = new Date(this.value);
                const endDate = new Date(startDate);
                endDate.setDate(startDate.getDate() + 6);
                
                const year = endDate.getFullYear();
                const month = String(endDate.getMonth() + 1).padStart(2, '0');
                const day = String(endDate.getDate()).padStart(2, '0');
                
                document.getElementById('week_end').value = `${year}-${month}-${day}`;
            }
        });
    </script>
</body>
</html>
