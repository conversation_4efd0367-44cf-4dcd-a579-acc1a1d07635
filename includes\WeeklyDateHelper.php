<?php
/**
 * Weekly Date Helper
 * Provides centralized functions for calculating Saturday-Friday weeks
 */

class WeeklyDateHelper {
    
    /**
     * Get the start date of the week (Saturday) for a given date
     * 
     * @param string|null $date Date in Y-m-d format, or null for current date
     * @return string Week start date (Saturday) in Y-m-d format
     */
    public static function getWeekStart($date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }
        
        $timestamp = strtotime($date);
        $dayOfWeek = date('w', $timestamp); // 0 = Sunday, 6 = Saturday
        
        // Calculate days to subtract to get to Saturday
        if ($dayOfWeek == 6) {
            // Already Saturday
            $daysToSubtract = 0;
        } else {
            // Sunday = 1 day back, Monday = 2 days back, ..., Friday = 7 days back
            $daysToSubtract = ($dayOfWeek + 1) % 7;
            if ($daysToSubtract == 0) $daysToSubtract = 7;
        }
        
        return date('Y-m-d', strtotime("-{$daysToSubtract} days", $timestamp));
    }
    
    /**
     * Get the end date of the week (Friday) for a given date
     * 
     * @param string|null $date Date in Y-m-d format, or null for current date
     * @return string Week end date (Friday) in Y-m-d format
     */
    public static function getWeekEnd($date = null) {
        $weekStart = self::getWeekStart($date);
        return date('Y-m-d', strtotime('+6 days', strtotime($weekStart)));
    }
    
    /**
     * Get the current week start and end dates
     * 
     * @return array ['start' => 'Y-m-d', 'end' => 'Y-m-d']
     */
    public static function getCurrentWeek() {
        return [
            'start' => self::getWeekStart(),
            'end' => self::getWeekEnd()
        ];
    }
    
    /**
     * Get the previous week start and end dates
     * 
     * @return array ['start' => 'Y-m-d', 'end' => 'Y-m-d']
     */
    public static function getPreviousWeek() {
        $lastWeekDate = date('Y-m-d', strtotime('-7 days'));
        return [
            'start' => self::getWeekStart($lastWeekDate),
            'end' => self::getWeekEnd($lastWeekDate)
        ];
    }
    
    /**
     * Get the next week start and end dates
     * 
     * @return array ['start' => 'Y-m-d', 'end' => 'Y-m-d']
     */
    public static function getNextWeek() {
        $nextWeekDate = date('Y-m-d', strtotime('+7 days'));
        return [
            'start' => self::getWeekStart($nextWeekDate),
            'end' => self::getWeekEnd($nextWeekDate)
        ];
    }
    
    /**
     * Get week start and end for a specific week offset
     * 
     * @param int $weekOffset Weeks to offset (negative for past, positive for future)
     * @return array ['start' => 'Y-m-d', 'end' => 'Y-m-d']
     */
    public static function getWeekByOffset($weekOffset = 0) {
        $targetDate = date('Y-m-d', strtotime("{$weekOffset} weeks"));
        return [
            'start' => self::getWeekStart($targetDate),
            'end' => self::getWeekEnd($targetDate)
        ];
    }
    
    /**
     * Check if a given date falls within a specific week
     * 
     * @param string $date Date to check (Y-m-d format)
     * @param string $weekStart Week start date (Y-m-d format)
     * @param string $weekEnd Week end date (Y-m-d format)
     * @return bool True if date is within the week
     */
    public static function isDateInWeek($date, $weekStart, $weekEnd) {
        return $date >= $weekStart && $date <= $weekEnd;
    }
    
    /**
     * Get a formatted week range string
     * 
     * @param string $weekStart Week start date (Y-m-d format)
     * @param string $weekEnd Week end date (Y-m-d format)
     * @param string $format Date format (default: 'M d, Y')
     * @return string Formatted week range
     */
    public static function formatWeekRange($weekStart, $weekEnd, $format = 'M d, Y') {
        $startFormatted = date($format, strtotime($weekStart));
        $endFormatted = date($format, strtotime($weekEnd));
        return "{$startFormatted} - {$endFormatted}";
    }
    
    /**
     * Get the week number for a given date (Saturday-Friday weeks)
     * 
     * @param string|null $date Date in Y-m-d format, or null for current date
     * @return int Week number (1-53)
     */
    public static function getWeekNumber($date = null) {
        $weekStart = self::getWeekStart($date);
        return (int) date('W', strtotime($weekStart));
    }
    
    /**
     * Convert old Monday-Sunday week to new Saturday-Friday week
     * This is useful for migration purposes
     * 
     * @param string $mondayDate Monday date of old week
     * @return array ['start' => 'Y-m-d', 'end' => 'Y-m-d'] New Saturday-Friday week
     */
    public static function convertMondayWeekToSaturdayWeek($mondayDate) {
        // The Saturday before this Monday is the new week start
        $newWeekStart = date('Y-m-d', strtotime('-2 days', strtotime($mondayDate)));
        $newWeekEnd = date('Y-m-d', strtotime('+6 days', strtotime($newWeekStart)));
        
        return [
            'start' => $newWeekStart,
            'end' => $newWeekEnd
        ];
    }
    
    /**
     * Get all weeks between two dates
     * 
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @return array Array of week arrays with 'start' and 'end' keys
     */
    public static function getWeeksBetween($startDate, $endDate) {
        $weeks = [];
        $currentWeekStart = self::getWeekStart($startDate);
        $endWeekStart = self::getWeekStart($endDate);
        
        while ($currentWeekStart <= $endWeekStart) {
            $weeks[] = [
                'start' => $currentWeekStart,
                'end' => self::getWeekEnd($currentWeekStart)
            ];
            $currentWeekStart = date('Y-m-d', strtotime('+7 days', strtotime($currentWeekStart)));
        }
        
        return $weeks;
    }
    
    /**
     * Get processing day configuration (Friday = 5)
     * 
     * @return int Day of week for processing (5 = Friday)
     */
    public static function getProcessingDay() {
        return 5; // Friday
    }
    
    /**
     * Check if today is the processing day
     * 
     * @return bool True if today is Friday
     */
    public static function isProcessingDay() {
        return (int) date('w') === self::getProcessingDay();
    }
    
    /**
     * Get the processing time (11:59 PM)
     * 
     * @return string Processing time in H:i format
     */
    public static function getProcessingTime() {
        return '23:59';
    }
    
    /**
     * Check if current time is past processing time
     * 
     * @return bool True if current time is past 11:59 PM
     */
    public static function isPastProcessingTime() {
        return date('H:i') >= self::getProcessingTime();
    }
}
?>
