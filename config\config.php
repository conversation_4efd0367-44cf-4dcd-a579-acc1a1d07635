<?php
/**
 * Main Configuration File
 * MLM Binary Plan System
 */

// Include database configuration
require_once 'Connection.php';

/**
 * Configuration Manager Class
 */
class Config {
    private static $instance = null;
    private $configs = [];
    private $db;
    
    private function __construct() {
        $this->db = Database::getInstance();
        $this->loadConfigs();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Load configurations from database
     */
    private function loadConfigs() {
        try {
            $stmt = $this->db->prepare("SELECT config_key, config_value FROM config");
            $stmt->execute();
            $results = $stmt->fetchAll();
            
            foreach ($results as $row) {
                $this->configs[$row['config_key']] = $row['config_value'];
            }
        } catch (Exception $e) {
            // If config table doesn't exist or error, use defaults
            $this->setDefaultConfigs();
        }
    }
    
    /**
     * Set default configurations
     */
    private function setDefaultConfigs() {
        $this->configs = [
            'pv_rate' => '0.20',
            'daily_capping' => '130000.00',
            'min_withdrawal' => '500.00',
            'razorpay_mode' => 'test',
            'company_name' => 'ShaktiPure MLM',
            'support_email' => '<EMAIL>',
            'support_phone' => '+91-9999999999'
        ];
    }
    
    /**
     * Get configuration value
     */
    public function get($key, $default = null) {
        return $this->configs[$key] ?? $default;
    }
    
    /**
     * Set configuration value
     */
    public function set($key, $value, $description = '') {
        try {
            $stmt = $this->db->prepare("INSERT INTO config (config_key, config_value, description) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE config_value = ?, description = ?");
            $stmt->execute([$key, $value, $description, $value, $description]);
            $this->configs[$key] = $value;
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get all configurations
     */
    public function getAll() {
        return $this->configs;
    }
    
    /**
     * Get PV rate
     */
    public function getPVRate() {
        return (float) $this->get('pv_rate', PV_RATE);
    }
    
    /**
     * Get daily capping amount
     */
    public function getDailyCapping() {
        return (float) $this->get('daily_capping', DAILY_CAPPING);
    }
    
    /**
     * Get minimum withdrawal amount
     */
    public function getMinWithdrawal() {
        return (float) $this->get('min_withdrawal', MIN_WITHDRAWAL);
    }
    
    /**
     * Get Razorpay mode
     */
    public function getRazorpayMode() {
        return $this->get('razorpay_mode', RAZORPAY_MODE);
    }
    
    /**
     * Get company name
     */
    public function getCompanyName() {
        return $this->get('company_name', SITE_NAME);
    }
    
    /**
     * Get support email
     */
    public function getSupportEmail() {
        return $this->get('support_email', ADMIN_EMAIL);
    }
    
    /**
     * Get support phone
     */
    public function getSupportPhone() {
        return $this->get('support_phone', '+91-9999999999');
    }
}

// Global configuration instance
$config = Config::getInstance();

// Define global constants from config
define('APP_PV_RATE', $config->getPVRate());
define('APP_DAILY_CAPPING', $config->getDailyCapping());
define('APP_MIN_WITHDRAWAL', $config->getMinWithdrawal());
define('APP_RAZORPAY_MODE', $config->getRazorpayMode());
define('APP_COMPANY_NAME', $config->getCompanyName());
define('APP_SUPPORT_EMAIL', $config->getSupportEmail());
define('APP_SUPPORT_PHONE', $config->getSupportPhone());
?>
