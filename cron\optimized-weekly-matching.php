<?php
/**
 * Optimized Weekly PV Matching Cron Job
 * MLM Binary Plan System
 * 
 * This script should be run weekly (e.g., every Sunday at midnight)
 * to process PV matching income for all users with optimized performance
 */

// Prevent direct browser access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

// Set time limit and memory limit for long-running process
set_time_limit(0);
ini_set('memory_limit', '512M');

// Include required files
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/config/Connection.php';
require_once dirname(__DIR__) . '/includes/PVSystem.php';
require_once dirname(__DIR__) . '/includes/BinaryTree.php';
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/includes/EmailService.php';
require_once dirname(__DIR__) . '/includes/Logger.php';
require_once dirname(__DIR__) . '/includes/WeeklyDateHelper.php';

// Initialize logger
$logger = Logger::getInstance();
$logger->logCron('weekly-matching', 'started', ['memory_usage' => memory_get_usage()]);

echo str_repeat('=', 60) . "\n";
echo "Optimized Weekly PV Matching Process\n";
echo "Start time: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat('=', 60) . "\n";

try {
    $db = Database::getInstance();
    $pvSystem = new PVSystem();
    $config = Config::getInstance();
    
    // Determine the week to process (Saturday-Friday weeks)
    $previousWeek = WeeklyDateHelper::getPreviousWeek();
    $weekStartDate = $previousWeek['start'];
    $weekEndDate = $previousWeek['end'];

    // Check if we should process - runs on Friday at 11:59 PM
    $processingDay = WeeklyDateHelper::getProcessingDay(); // 5 = Friday
    $currentDayOfWeek = (int) date('w'); // 0 = Sunday, 1 = Monday, etc.

    if ($currentDayOfWeek === $processingDay) {
        // If today is Friday, process the completed Saturday-Friday week
        echo "Processing completed Saturday-Friday week: {$weekStartDate} to {$weekEndDate}\n";
        $logger->info("Processing completed Saturday-Friday week: {$weekStartDate} to {$weekEndDate}", [], 'cron');
    } else {
        $message = "Not the designated processing day. Current day: {$currentDayOfWeek}, Processing day: {$processingDay} (Friday)";
        echo $message . "\n";
        echo "Weekly matching will run on Friday at 11:59 PM\n";
        $logger->info($message, ['current_day' => $currentDayOfWeek, 'processing_day' => $processingDay], 'cron');
        exit(0);
    }
    
    // Check if this week has already been processed
    $checkStmt = $db->prepare("SELECT id FROM weekly_income_reports WHERE week_start_date = ?");
    $checkStmt->execute([$weekStartDate]);
    
    if ($checkStmt->fetch()) {
        $message = "Week {$weekStartDate} to {$weekEndDate} has already been processed.";
        echo $message . "\n";
        echo "Skipping weekly matching.\n";
        $logger->info($message, [], 'cron');
        exit(0);
    }
    
    echo "Starting weekly PV matching process...\n\n";
    $logger->info("Starting weekly PV matching process", ['week_start' => $weekStartDate, 'week_end' => $weekEndDate], 'cron');
    
    // Get all active users
    $userStmt = $db->prepare("SELECT user_id FROM users WHERE status = 'active'");
    $userStmt->execute();
    $users = $userStmt->fetchAll(PDO::FETCH_COLUMN);
    
    $totalUsers = count($users);
    echo "Total users to process: {$totalUsers}\n";
    $logger->info("Total users to process: {$totalUsers}", [], 'cron');
    
    // Process users in batches to optimize memory usage
    $batchSize = 100;
    $batches = ceil($totalUsers / $batchSize);
    
    $processed = 0;
    $usersWithIncome = 0;
    $totalIncomeDistributed = 0;
    $totalCappingApplied = 0;
    
    // Create weekly report
    $db->beginTransaction();
    try {
        $reportStmt = $db->prepare("INSERT INTO weekly_income_reports (week_start_date, week_end_date, total_users_earned, total_income_distributed, total_capping_applied, report_status, report_generated_at) VALUES (?, ?, 0, 0, 0, 'processing', NOW())");
        $reportStmt->execute([$weekStartDate, $weekEndDate]);
        $reportId = $db->lastInsertId();
        $db->commit();
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
    // Process users in batches
    for ($i = 0; $i < $batches; $i++) {
        $start = $i * $batchSize;
        $batchUsers = array_slice($users, $start, $batchSize);
        
        echo "Processing batch " . ($i + 1) . " of {$batches} (" . count($batchUsers) . " users)...\n";
        $logger->info("Processing batch " . ($i + 1) . " of {$batches}", ['batch_size' => count($batchUsers), 'memory_usage' => memory_get_usage()], 'cron');
        
        $batchProcessed = 0;
        $batchIncome = 0;
        $batchCapping = 0;
        $batchUsersWithIncome = 0;
        
        // Begin transaction for this batch
        $db->beginTransaction();
        
        try {
            foreach ($batchUsers as $userId) {
                $result = $pvSystem->processWeeklyPVMatching($userId, $weekStartDate, $weekEndDate);
                
                if ($result) {
                    $batchProcessed++;
                    
                    if ($result['income_amount'] > 0) {
                        $batchUsersWithIncome++;
                        $batchIncome += $result['income_amount'];
                        $batchCapping += $result['capping_applied'];
                        
                        // Log payout
                        $logger->logPayout($userId, $result['income_amount'], 'weekly_matching', [
                            'week_start' => $weekStartDate,
                            'week_end' => $weekEndDate,
                            'matched_pv' => $result['matched_pv'],
                            'capping_applied' => $result['capping_applied']
                        ]);
                    }
                }
                
                // Free memory
                unset($result);
            }
            
            // Update totals
            $processed += $batchProcessed;
            $usersWithIncome += $batchUsersWithIncome;
            $totalIncomeDistributed += $batchIncome;
            $totalCappingApplied += $batchCapping;
            
            // Commit transaction for this batch
            $db->commit();
            
            echo "Batch " . ($i + 1) . " completed. Processed: {$batchProcessed}, Users with income: {$batchUsersWithIncome}, Income: ₹" . number_format($batchIncome, 2) . "\n";
            $logger->info("Batch " . ($i + 1) . " completed", [
                'processed' => $batchProcessed,
                'users_with_income' => $batchUsersWithIncome,
                'income' => $batchIncome,
                'capping' => $batchCapping,
                'memory_usage' => memory_get_usage()
            ], 'cron');
            
        } catch (Exception $e) {
            // Rollback transaction for this batch
            $db->rollback();
            
            $errorMessage = "Error processing batch " . ($i + 1) . ": " . $e->getMessage();
            echo $errorMessage . "\n";
            $logger->error($errorMessage, ['batch' => $i + 1, 'exception' => $e->getMessage()], 'cron');
            
            // Continue with next batch
            continue;
        }
        
        // Force garbage collection after each batch
        gc_collect_cycles();
    }
    
    // Update weekly report
    $db->beginTransaction();
    try {
        $updateReportStmt = $db->prepare("UPDATE weekly_income_reports SET total_users_earned = ?, total_income_distributed = ?, total_capping_applied = ?, report_status = 'generated', report_generated_at = NOW() WHERE id = ?");
        $updateReportStmt->execute([$usersWithIncome, $totalIncomeDistributed, $totalCappingApplied, $reportId]);
        $db->commit();
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
    echo "\n" . str_repeat('-', 50) . "\n";
    echo "Weekly PV matching completed successfully!\n";
    echo "Summary:\n";
    echo "- Total users processed: {$processed}\n";
    echo "- Users with income: {$usersWithIncome}\n";
    echo "- Total income distributed: ₹" . number_format($totalIncomeDistributed, 2) . "\n";
    echo "- Total capping applied: ₹" . number_format($totalCappingApplied, 2) . "\n";
    echo str_repeat('-', 50) . "\n";
    
    // Log the successful run
    $logger->info("Weekly PV matching completed successfully", [
        'processed' => $processed,
        'users_with_income' => $usersWithIncome,
        'total_income' => $totalIncomeDistributed,
        'total_capping' => $totalCappingApplied,
        'memory_usage' => memory_get_usage()
    ], 'cron');
    
    // Send admin notification
    try {
        $emailService = new EmailService();
        
        $result = [
            'processed' => $processed,
            'users_with_income' => $usersWithIncome,
            'total_income' => $totalIncomeDistributed,
            'total_capping' => $totalCappingApplied
        ];
        
        $emailResult = $emailService->sendWeeklyIncomeReport($weekStartDate, $weekEndDate, $result);
        
        if ($emailResult) {
            echo "\nAdmin notification sent successfully.\n";
            $logger->info("Admin notification sent successfully", [], 'cron');
            
            // Update report status to 'sent'
            $db->beginTransaction();
            try {
                $updateStmt = $db->prepare("UPDATE weekly_income_reports SET report_sent_at = NOW(), report_status = 'sent' WHERE id = ?");
                $updateStmt->execute([$reportId]);
                $db->commit();
            } catch (Exception $e) {
                $db->rollback();
                throw $e;
            }
        } else {
            throw new Exception("Failed to send email notification");
        }
    } catch (Exception $e) {
        echo "Failed to send admin notification: " . $e->getMessage() . "\n";
        $logger->error("Failed to send admin notification", ['exception' => $e->getMessage()], 'cron');
        
        // Update report status to 'failed_email'
        try {
            $db->beginTransaction();
            $updateStmt = $db->prepare("UPDATE weekly_income_reports SET report_status = 'failed_email' WHERE id = ?");
            $updateStmt->execute([$reportId]);
            $db->commit();
        } catch (Exception $updateError) {
            $db->rollback();
            $logger->error("Failed to update report status", ['exception' => $updateError->getMessage()], 'cron');
        }
    }
    
} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    
    $logger->critical("Weekly matching fatal error", [
        'exception' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'memory_usage' => memory_get_usage()
    ], 'cron');
}

// Log completion
$logger->logCron('weekly-matching', 'completed', ['memory_usage' => memory_get_usage()]);

echo str_repeat('=', 60) . "\n";
echo "Weekly PV matching process completed\n";
echo "End time: " . date('Y-m-d H:i:s') . "\n";
echo "Peak memory usage: " . (memory_get_peak_usage(true) / 1024 / 1024) . " MB\n";
echo str_repeat('=', 60) . "\n";
?>
