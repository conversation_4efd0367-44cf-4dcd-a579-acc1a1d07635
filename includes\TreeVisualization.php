        <?php
        /**
         * Tree Visualization Helper
         * MLM Binary Plan System
         */

        class TreeVisualization {
            
            /**
             * Generate HTML for binary tree visualization
             */
            public static function renderTree($treeData, $currentUserId = null) {
                if (empty($treeData)) {
                    return '<div class="text-center text-muted">No tree data available</div>';
                }

                $html = '<div class="tree-container">';
                $html .= self::renderNode($treeData, $currentUserId, 0);
                $html .= '</div>';

                return $html;
            }

            /**
             * Generate HTML for binary tree visualization with enhanced data
             */
            public static function renderEnhancedTree($treeData, $currentUserId = null) {
                if (empty($treeData)) {
                    return '<div class="text-center text-muted">No tree data available</div>';
                }

                $html = '<div class="tree-container">';
                $html .= self::renderEnhancedNode($treeData, $currentUserId, 0);
                $html .= '</div>';

                return $html;
            }
            
            /**
             * Render individual tree node
             */
            private static function renderNode($node, $currentUserId, $level) {
                if (empty($node)) {
                    return '';
                }
                
                $isCurrentUser = $node['user_id'] === $currentUserId;
                $statusClass = $node['status'] === 'active' ? 'active-user' : 'inactive-user';
                $currentClass = $isCurrentUser ? 'current-user' : '';
                
                $html = '<div class="tree-level level-' . $level . '">';
                
                // Node container
                $html .= '<div class="tree-node-container">';
                
                // Node
                $html .= '<div class="tree-node ' . $statusClass . ' ' . $currentClass . '" data-user-id="' . $node['user_id'] . '">';
                $html .= '<div class="node-icon">';
                $html .= '<i class="fas fa-users fa-2x"></i>';
                $html .= '</div>';
                $html .= '<div class="node-info">';
                $html .= '<div class="user-id">' . htmlspecialchars($node['user_id']) . '</div>';
                $html .= '<div class="user-name">' . htmlspecialchars($node['full_name']) . '</div>';
                $html .= '</div>';
                $html .= '</div>';
                
                // Always show children container with plus icons if no children
                $html .= '<div class="tree-line"></div>';
                $html .= '<div class="tree-children">';
                
                // Left child
                $html .= '<div class="tree-child left-child">';
                if (!empty($node['left'])) {
                    $html .= '<div class="child-label">Left</div>';
                    $html .= self::renderNode($node['left'], $currentUserId, $level + 1);
                } else {
                    $html .= '<div class="empty-slot" data-parent="' . $node['user_id'] . '" data-side="left">';
                    $html .= '<a href="javascript:void(0)" onclick="handleRegistration(\'' . $node['user_id'] . '\', \'left\')" class="empty-slot-link">';
                    $html .= '<i class="fas fa-plus-circle"></i><br>Available';
                    $html .= '</a>';
                    $html .= '</div>';
                }
                $html .= '</div>';
                
                // Right child
                $html .= '<div class="tree-child right-child">';
                if (!empty($node['right'])) {
                    $html .= '<div class="child-label">Right</div>';
                    $html .= self::renderNode($node['right'], $currentUserId, $level + 1);
                } else {
                    $html .= '<div class="empty-slot" data-parent="' . $node['user_id'] . '" data-side="right">';
                    $html .= '<a href="javascript:void(0)" onclick="handleRegistration(\'' . $node['user_id'] . '\', \'right\')" class="empty-slot-link">';
                    $html .= '<i class="fas fa-plus-circle"></i><br>Available';
                    $html .= '</a>';
                    $html .= '</div>';
                }
                $html .= '</div>';
                
                $html .= '</div>'; // tree-children
                $html .= '</div>'; // tree-node-container
                $html .= '</div>'; // tree-level
                
                return $html;
            }

            /**
             * Render enhanced individual tree node with PV data
             */
            private static function renderEnhancedNode($node, $currentUserId, $level) {
                if (empty($node)) {
                    return '';
                }

                $isCurrentUser = $node['user_id'] === $currentUserId;
                $statusClass = $node['status'] === 'active' ? 'active-user' : 'inactive-user';
                $currentClass = $isCurrentUser ? 'current-user' : '';

                // Get PV and downline data for this user
                $pvSystem = new PVSystem();
                $binaryTree = new BinaryTree();

                $ownPV = $pvSystem->getUserPVTotals($node['user_id']);
                $downlinePV = $pvSystem->getDownlinePVTotals($node['user_id']);
                $treeStats = $binaryTree->getTreeStats($node['user_id']);

                $totalLeftPV = $ownPV['left_pv'] + $downlinePV['left_pv'];
                $totalRightPV = $ownPV['right_pv'] + $downlinePV['right_pv'];

                $html = '<div class="tree-level level-' . $level . '">';

                // Node container
                $html .= '<div class="tree-node-container">';

                // Get additional user data for hover
                $db = Database::getInstance();
                $userStmt = $db->prepare("SELECT created_at FROM users WHERE user_id = ?");
                $userStmt->execute([$node['user_id']]);
                $userData = $userStmt->fetch();
                $joinDate = $userData ? date('d M Y', strtotime($userData['created_at'])) : 'N/A';

                // Get left and right member counts
                $leftMembers = $treeStats['left_leg_count'] ?? 0;
                $rightMembers = $treeStats['right_leg_count'] ?? 0;

                // Node with enhanced data attributes
                $html .= '<div class="tree-node enhanced-node ' . $statusClass . ' ' . $currentClass . '"
                            data-user-id="' . $node['user_id'] . '"
                            data-self-pv="' . floatval($node['self_pv'] ?? 0) . '"
                            data-left-pv="' . $totalLeftPV . '"
                            data-right-pv="' . $totalRightPV . '"
                            data-downline-count="' . $treeStats['total_downline'] . '"
                            data-user-name="' . htmlspecialchars($node['full_name']) . '"
                            data-join-date="' . $joinDate . '"
                            data-left-members="' . $leftMembers . '"
                            data-right-members="' . $rightMembers . '"
                            data-status="' . $node['status'] . '">';

                $html .= '<div class="node-icon">';
                $html .= '<i class="fas fa-users fa-2x"></i>';
                $html .= '</div>';
                $html .= '<div class="node-info">';
                $html .= '<div class="user-id">' . htmlspecialchars($node['user_id']) . '</div>';
                $html .= '<div class="user-name">' . htmlspecialchars($node['full_name']) . '</div>';
                $html .= '</div>';
                $html .= '</div>';

                // Always show children container with plus icons
                $html .= '<div class="tree-line"></div>';
                $html .= '<div class="tree-children">';

                // Left child
                $html .= '<div class="tree-child left-child">';
                if (!empty($node['left'])) {
                    $html .= '<div class="child-label">Left</div>';
                    $html .= self::renderEnhancedNode($node['left'], $currentUserId, $level + 1);
                } else {
                    $html .= '<div class="empty-slot" data-parent="' . $node['user_id'] . '" data-side="left">';
                    $html .= '<a href="javascript:void(0)" onclick="handleRegistration(\'' . $node['user_id'] . '\', \'left\')" class="empty-slot-link">';
                    $html .= '<i class="fas fa-plus-circle"></i><br>Available';
                    $html .= '</a>';
                    $html .= '</div>';
                }
                $html .= '</div>';

                // Right child
                $html .= '<div class="tree-child right-child">';
                if (!empty($node['right'])) {
                    $html .= '<div class="child-label">Right</div>';
                    $html .= self::renderEnhancedNode($node['right'], $currentUserId, $level + 1);
                } else {
                    $html .= '<div class="empty-slot" data-parent="' . $node['user_id'] . '" data-side="right">';
                    $html .= '<a href="javascript:void(0)" onclick="handleRegistration(\'' . $node['user_id'] . '\', \'right\')" class="empty-slot-link">';
                    $html .= '<i class="fas fa-plus-circle"></i><br>Available';
                    $html .= '</a>';
                    $html .= '</div>';
                }
                $html .= '</div>';

                $html .= '</div>'; // tree-children

                $html .= '</div>'; // tree-node-container
                $html .= '</div>'; // tree-level

                return $html;
            }

            /**
             * Generate CSS for tree visualization
             */
            public static function getTreeCSS() {
                return '
                <style>
                .tree-container {
                    width: 100%;
                    overflow-x: auto;
                    padding: 1rem;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    font-family: Arial, sans-serif;
                    -webkit-overflow-scrolling: touch;
                }
                
                .tree-level {
                    display: flex;
                    justify-content: center;
                    margin-bottom: 2rem;
                    min-width: max-content;
                    padding: 0 1rem;
                }
                
                .tree-node-container {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin: 0 0.5rem;
                }
                
                .tree-node {
                    text-align: center;
                    min-width: 70px;
                    max-width: 100px;
                    transition: all 0.3s ease;
                    cursor: pointer;
                    padding: 0.5rem;
                    margin: 0 0.25rem;
                }
                
                @media (max-width: 768px) {
                    .tree-node {
                        min-width: 60px;
                        padding: 0.35rem;
                        font-size: 90%;
                    }
                }
                
                .tree-node:hover {
                    transform: scale(1.1);
                }
                
                .tree-node.active-user .node-icon {
                    color: #28a745;
                }
                
                .tree-node.inactive-user .node-icon {
                    color: #dc3545;
                }
                
                .tree-node.current-user .node-icon {
                    color: #667eea;
                }

                .node-icon {
                    font-size: 1rem;
                    color: #667eea;
                }

                .node-icon i {
                    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
                }

                .tree-node:hover .node-icon i {
                    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
                }
                
                /* Node info styles */
                .node-info {
                    margin-top: 5px;
                    text-align: center;
                }

                .user-id {
                    font-size: 0.75rem;
                    font-weight: 600;
                    color: #495057;
                }

                .user-name {
                    font-size: 0.7rem;
                    color: #6c757d;
                    margin-top: 2px;
                }

                /* Status colors applied directly to icons */
                .tree-node.active-user .node-icon {
                    color: #28a745;
                }
                
                .tree-node.inactive-user .node-icon {
                    color: #dc3545;
                }
                
                .tree-line {
                    width: 2px;
                    height: 30px;
                    background-color: #dee2e6;
                    margin: 0.5rem 0;
                }
                
                .tree-children {
                    display: flex;
                    gap: 2rem;
                    position: relative;
                }
                
                .tree-children::before {
                    content: "";
                    position: absolute;
                    top: -15px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: calc(100% - 2rem);
                    height: 2px;
                    background-color: #dee2e6;
                }
                
                .tree-child {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    position: relative;
                }
                
                .tree-child::before {
                    content: "";
                    position: absolute;
                    top: -15px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 2px;
                    height: 15px;
                    background-color: #dee2e6;
                }
                
                .child-label {
                    font-size: 0.75rem;
                    color: #6c757d;
                    margin-bottom: 0.5rem;
                    font-weight: 500;
                }
                
                .empty-slot {
                    text-align: center;
                    min-width: 80px;
                    color: #6c757d;
                    font-size: 0.8rem;
                    transition: all 0.3s ease;
                    padding: 0.5rem;
                }

                .empty-slot-link {
                    text-decoration: none;
                    color: #6c757d;
                    display: block;
                    cursor: pointer;
                }
                
                .empty-slot:hover {
                    transform: scale(1.1);
                }

                .empty-slot:hover .empty-slot-link {
                    color: #667eea;
                }
                
                .empty-slot i {
                    font-size: 2rem;
                    margin-bottom: 0.25rem;
                    display: block;
                    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
                }

                .empty-slot:hover i {
                    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
                }
                
                @media (max-width: 768px) {
                    .tree-container {
                        padding: 1rem;
                    }
                    
                    .tree-node {
                        min-width: 120px;
                        padding: 0.75rem;
                        font-size: 0.8rem;
                    }
                    
                    .tree-children {
                        gap: 1rem;
                    }
                    
                    .empty-slot {
                        min-width: 120px;
                        padding: 0.75rem;
                    }
                }

                /* Tooltip Styles */
                .tree-tooltip {
                    position: absolute;
                    background: linear-gradient(135deg, #2c3e50, #34495e);
                    color: white;
                    padding: 15px;
                    border-radius: 12px;
                    font-size: 0.85rem;
                    z-index: 1000;
                    pointer-events: none;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                    min-width: 280px;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
                    border: 1px solid rgba(255,255,255,0.1);
                }

                .tree-tooltip.show {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(-5px);
                }

                .tree-tooltip::after {
                    content: "";
                    position: absolute;
                    top: 100%;
                    left: 50%;
                    margin-left: -5px;
                    border-width: 5px;
                    border-style: solid;
                    border-color: #34495e transparent transparent transparent;
                }

                .tooltip-header {
                    margin-bottom: 12px;
                }

                .tooltip-name {
                    font-weight: bold;
                    font-size: 1rem;
                    color: #3498db;
                    margin-bottom: 4px;
                }

                .tooltip-userid {
                    font-size: 0.8rem;
                    color: #bdc3c7;
                }

                .tooltip-divider {
                    height: 1px;
                    background: rgba(255,255,255,0.2);
                    margin: 8px 0;
                }

                .tooltip-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 6px;
                    align-items: center;
                }

                .tooltip-row:last-child {
                    margin-bottom: 0;
                }

                .tooltip-label {
                    font-weight: 500;
                    margin-right: 12px;
                    color: #ecf0f1;
                    flex: 1;
                }

                .tooltip-value {
                    font-weight: bold;
                    color: #2ecc71;
                    text-align: right;
                }

                .tooltip-label {
                    font-weight: bold;
                }

                .tooltip-section {
                    margin-top: 8px;
                }

                .tooltip-section-title {
                    font-size: 0.9rem;
                    margin-bottom: 6px;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                }

                .enhanced-node {
                    position: relative;
                }
                </style>';
            }
            
            /**
             * Generate JavaScript for tree interactions
             */
            public static function getTreeJS() {
                return '
                <script>
                async function handleRegistration(sponsorId, side) {
                    try {
                        // First, perform logout
                        const logoutResponse = await fetch("../logout.php");
                        if (logoutResponse.ok) {
                            // After successful logout, redirect to registration page with sponsor and side
                            window.location.href = "https://shaktipure.store/user/register.php?sponsor=" + sponsorId + "&side=" + side;
                        } else {
                            console.error("Logout failed");
                        }
                    } catch (error) {
                        console.error("Error during logout:", error);
                    }
                }

                document.addEventListener("DOMContentLoaded", function() {
                    let tooltip = null;

                    // Create tooltip element
                    function createTooltip() {
                        if (!tooltip) {
                            tooltip = document.createElement("div");
                            tooltip.className = "tree-tooltip";
                            document.body.appendChild(tooltip);
                        }
                        return tooltip;
                    }

                    // Enhanced tree node handlers
                    document.querySelectorAll(".enhanced-node").forEach(function(node) {
                        // Hover handlers for tooltip
                        node.addEventListener("mouseenter", function(e) {
                            const tooltip = createTooltip();
                            const userId = this.getAttribute("data-user-id") || "N/A";
                            const userName = this.getAttribute("data-user-name") || "Unknown";
                            const joinDate = this.getAttribute("data-join-date") || "N/A";
                            const selfPV = this.getAttribute("data-self-pv") || "0";
                            const leftPV = this.getAttribute("data-left-pv") || "0";
                            const rightPV = this.getAttribute("data-right-pv") || "0";
                            const leftMembers = this.getAttribute("data-left-members") || "0";
                            const rightMembers = this.getAttribute("data-right-members") || "0";
                            const status = this.getAttribute("data-status") || "unknown";

                            // Calculate activation progress for inactive users
                            const selfPVValue = parseFloat(selfPV);
                            const activationThreshold = 500;
                            const remainingPV = Math.max(0, activationThreshold - selfPVValue);
                            const activationProgress = Math.min(100, (selfPVValue / activationThreshold) * 100);

                            let activationInfo = "";
                            if (status === "inactive") {
                                activationInfo = "<div class=\\"tooltip-divider\\"></div>" +
                                    "<div class=\\"tooltip-section\\">" +
                                        "<div class=\\"tooltip-section-title\\" style=\\"color: #dc3545; font-weight: bold;\\">" +
                                            "<i class=\\"fas fa-exclamation-triangle\\"></i> Activation Status" +
                                        "</div>" +
                                        "<div class=\\"tooltip-row\\">" +
                                            "<span class=\\"tooltip-label\\">Progress:</span>" +
                                            "<span class=\\"tooltip-value\\">" + activationProgress.toFixed(1) + "%</span>" +
                                        "</div>" +
                                        "<div class=\\"tooltip-row\\">" +
                                            "<span class=\\"tooltip-label\\">Remaining PV:</span>" +
                                            "<span class=\\"tooltip-value\\" style=\\"color: #dc3545;\\">" + remainingPV.toFixed(0) + "</span>" +
                                        "</div>" +
                                    "</div>";
                            } else if (status === "active") {
                                activationInfo = "<div class=\\"tooltip-divider\\"></div>" +
                                    "<div class=\\"tooltip-section\\">" +
                                        "<div class=\\"tooltip-section-title\\" style=\\"color: #28a745; font-weight: bold;\\">" +
                                            "<i class=\\"fas fa-check-circle\\"></i> Activated" +
                                        "</div>" +
                                    "</div>";
                            }

                            tooltip.innerHTML = "<div class=\\"tooltip-header\\">" +
                                    "<div class=\\"tooltip-name\\">" + userName + "</div>" +
                                    "<div class=\\"tooltip-userid\\">ID: " + userId + "</div>" +
                                "</div>" +
                                "<div class=\\"tooltip-divider\\"></div>" +
                                "<div class=\\"tooltip-row\\">" +
                                    "<span class=\\"tooltip-label\\">Join Date:</span>" +
                                    "<span>" + joinDate + "</span>" +
                                "</div>" +
                                "<div class=\\"tooltip-row\\">" +
                                    "<span class=\\"tooltip-label\\">Self PV:</span>" +
                                    "<span class=\\"tooltip-value\\" style=\\"font-weight: bold; color: #667eea;\\">" + selfPVValue.toFixed(2) + "</span>" +
                                "</div>" +
                                "<div class=\\"tooltip-row\\">" +
                                    "<span class=\\"tooltip-label\\">Left PV:</span>" +
                                    "<span class=\\"tooltip-value\\">" + parseFloat(leftPV).toFixed(2) + "</span>" +
                                "</div>" +
                                "<div class=\\"tooltip-row\\">" +
                                    "<span class=\\"tooltip-label\\">Right PV:</span>" +
                                    "<span class=\\"tooltip-value\\">" + parseFloat(rightPV).toFixed(2) + "</span>" +
                                "</div>" +
                                "<div class=\\"tooltip-row\\">" +
                                    "<span class=\\"tooltip-label\\">Left Members:</span>" +
                                    "<span class=\\"tooltip-value\\">" + leftMembers + "</span>" +
                                "</div>" +
                                "<div class=\\"tooltip-row\\">" +
                                    "<span class=\\"tooltip-label\\">Right Members:</span>" +
                                    "<span class=\\"tooltip-value\\">" + rightMembers + "</span>" +
                                "</div>" +
                                activationInfo;

                            tooltip.classList.add("show");
                            positionTooltip(e, tooltip);
                        });

                        node.addEventListener("mousemove", function(e) {
                            if (tooltip) {
                                positionTooltip(e, tooltip);
                            }
                        });

                        node.addEventListener("mouseleave", function() {
                            if (tooltip) {
                                tooltip.classList.remove("show");
                            }
                        });

                        // Click handler for navigation
                        node.addEventListener("click", function() {
                            const userId = this.getAttribute("data-user-id");
                            if (userId) {
                                navigateToSponsorTree(userId);
                            }
                        });
                    });

                    // Regular tree node click handler (for non-enhanced nodes)
                    document.querySelectorAll(".tree-node:not(.enhanced-node)").forEach(function(node) {
                        node.addEventListener("click", function() {
                            const userId = this.getAttribute("data-user-id");
                            if (userId) {
                                showUserDetails(userId);
                            }
                        });
                    });

                    // Empty slots now use direct links to the registration page

                    function positionTooltip(e, tooltip) {
                        const rect = tooltip.getBoundingClientRect();
                        const x = e.pageX - rect.width / 2;
                        const y = e.pageY - rect.height - 10;

                        tooltip.style.left = Math.max(10, x) + "px";
                        tooltip.style.top = Math.max(10, y) + "px";
                    }
                });

                function navigateToSponsorTree(userId) {
                    // Navigate to the sponsor\'s tree view
                    const currentUrl = window.location.href;
                    const baseUrl = currentUrl.split("?")[0];
                    window.location.href = baseUrl + "?view_user=" + userId;
                }

                function showUserDetails(userId) {
                    // Show user details modal or redirect to user profile
                    console.log("Show details for user:", userId);
                    // You can implement modal or redirect logic here
                }

                function showPlacementForm(parentId, side) {
                    // Show placement form for new user
                    console.log("Show placement form for parent:", parentId, "side:", side);
                    // You can implement placement form logic here
                }
                </script>';
            }
            
            /**
             * Render complete tree with CSS and JS
             */
            public static function renderCompleteTree($treeData, $currentUserId = null) {
                $html = self::getTreeCSS();
                $html .= self::renderTree($treeData, $currentUserId);
                $html .= self::getTreeJS();
                return $html;
            }
        }
        ?>
