<?php
/**
 * Purchase Bills Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

// Get user's purchase orders
$db = Database::getInstance();

// Pagination
$page = (int) ($_GET['page'] ?? 1);
$limit = 10;
$offset = ($page - 1) * $limit;

// Get total count (only franchise-assigned and admin-approved products)
$countStmt = $db->prepare("
    SELECT COUNT(*) as total
    FROM purchase_orders po
    WHERE po.user_id = ?
    AND po.payment_method = 'manual'
    AND po.payment_status = 'completed'
    AND po.order_status = 'confirmed'
");
$countStmt->execute([$userId]);
$totalOrders = $countStmt->fetch()['total'];
$totalPages = ceil($totalOrders / $limit);

// Get orders with product details (only franchise-assigned and admin-approved products)
$ordersStmt = $db->prepare("
    SELECT po.*, p.name as product_name, p.description as product_description,
           u.franchise_id, f.full_name as franchise_name, f.franchise_code
    FROM purchase_orders po
    JOIN products p ON po.product_id = p.id
    JOIN users u ON po.user_id = u.user_id
    LEFT JOIN franchise f ON u.franchise_id = f.id
    WHERE po.user_id = ?
    AND po.payment_method = 'manual'
    AND po.payment_status = 'completed'
    AND po.order_status = 'confirmed'
    ORDER BY po.created_at DESC
    LIMIT ? OFFSET ?
");
$ordersStmt->execute([$userId, $limit, $offset]);
$orders = $ordersStmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Bills - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <img src="../assets/images/logo.png" alt="" style="height:50px; width:auto;">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-box me-1"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="purchase-bills.php">
                            <i class="fas fa-receipt me-1"></i>Purchase Bills
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3><i class="fas fa-receipt me-2"></i>Purchase Bills</h3>
                        <p class="text-muted mb-0">Products assigned by franchise and approved by admin</p>
                    </div>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Summary Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <h4 class="text-primary"><?php echo $totalOrders; ?></h4>
                                <small class="text-muted">Total Orders</small>
                            </div>
                            <div class="col-md-4">
                                <?php
                                $totalAmountStmt = $db->prepare("SELECT SUM(total_amount) as total FROM purchase_orders WHERE user_id = ? AND payment_method = 'manual' AND payment_status = 'completed' AND order_status = 'confirmed'");
                                $totalAmountStmt->execute([$userId]);
                                $totalAmount = $totalAmountStmt->fetch()['total'] ?? 0;
                                ?>
                                <h4 class="text-success"><?php echo formatCurrency($totalAmount); ?></h4>
                                <small class="text-muted">Total Value (Franchise Assigned)</small>
                            </div>
                            <div class="col-md-4">
                                <?php
                                $totalPVStmt = $db->prepare("SELECT SUM(pv_amount) as total FROM purchase_orders WHERE user_id = ? AND payment_method = 'manual' AND payment_status = 'completed' AND order_status = 'confirmed'");
                                $totalPVStmt->execute([$userId]);
                                $totalPV = $totalPVStmt->fetch()['total'] ?? 0;
                                ?>
                                <h4 class="text-info"><?php echo formatPV($totalPV); ?></h4>
                                <small class="text-muted">Total PV Earned (Franchise Assigned)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase Bills List -->
        <div class="row">
            <div class="col-12">
                <?php if (!empty($orders)): ?>
                    <?php foreach ($orders as $order): ?>
                        <div class="bill-item">
                            <div class="bill-header">
                                <div>
                                    <h6 class="mb-0">Order #<?php echo htmlspecialchars($order['order_id']); ?></h6>
                                    <small class="text-muted"><?php echo date('d M Y, h:i A', strtotime($order['created_at'])); ?></small>
                                </div>
                                <div>
                                    <?php
                                    $statusClass = match($order['order_status']) {
                                        'confirmed' => 'success',
                                        'pending' => 'warning',
                                        'cancelled' => 'danger',
                                        default => 'secondary'
                                    };
                                    ?>
                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                        <?php echo ucfirst($order['order_status']); ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="bill-details">
                                <div class="bill-detail-item">
                                    <span><strong>Product:</strong></span>
                                    <span><?php echo htmlspecialchars($order['product_name']); ?></span>
                                </div>
                                <div class="bill-detail-item">
                                    <span><strong>Quantity:</strong></span>
                                    <span><?php echo $order['quantity']; ?></span>
                                </div>
                                <div class="bill-detail-item">
                                    <span><strong>Unit Price:</strong></span>
                                    <span><?php echo formatCurrency($order['unit_price']); ?></span>
                                </div>
                                <div class="bill-detail-item">
                                    <span><strong>Total Amount:</strong></span>
                                    <span class="text-success"><strong><?php echo formatCurrency($order['total_amount']); ?></strong></span>
                                </div>
                                <div class="bill-detail-item">
                                    <span><strong>PV Earned:</strong></span>
                                    <span class="text-info"><?php echo formatPV($order['pv_amount']); ?></span>
                                </div>
                                <div class="bill-detail-item">
                                    <span><strong>Placement Side:</strong></span>
                                    <span>
                                        <span class="badge bg-<?php echo $order['placement_side'] === 'left' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($order['placement_side']); ?>
                                        </span>
                                    </span>
                                </div>
                                <div class="bill-detail-item">
                                    <span><strong>Assigned By:</strong></span>
                                    <span class="text-primary">
                                        <i class="fas fa-building me-1"></i>
                                        <?php echo htmlspecialchars($order['franchise_name'] ?? 'Franchise'); ?>
                                        (<?php echo htmlspecialchars($order['franchise_code'] ?? 'N/A'); ?>)
                                    </span>
                                </div>
                                <div class="bill-detail-item">
                                    <span><strong>Approval Status:</strong></span>
                                    <span>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Admin Approved
                                        </span>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="bill-actions">
                                <a href="bill-details.php?order_id=<?php echo urlencode($order['order_id']); ?>" 
                                   class="btn btn-outline-primary btn-sm me-2">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                                <a href="bill-details.php?order_id=<?php echo urlencode($order['order_id']); ?>&download=1" 
                                   class="btn btn-success btn-sm" target="_blank">
                                    <i class="fas fa-download me-1"></i>Download Bill
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Purchase bills pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Franchise Assignment Bills Found</h5>
                        <p class="text-muted">You don't have any products assigned by your franchise and approved by admin yet.</p>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> This section shows only products that were assigned to you by your franchise
                            and approved by the admin. Regular purchases are not shown here.
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
