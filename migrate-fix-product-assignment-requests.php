<?php
/**
 * Fix Product Assignment Requests Table
 * ShaktiPure MLM System
 * 
 * This migration adds the request_id column if missing and fixes any existing records
 * with empty request_id values to prevent duplicate entry errors.
 */

// Set execution time limit
set_time_limit(300);

// Determine if running from CLI or web
$isCLI = (php_sapi_name() === 'cli');

// Start HTML output for web interface
if (!$isCLI) {
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>Fix Product Assignment Requests - ShaktiPure MLM</title>";
    echo "<style>body{font-family:Arial,sans-serif;max-width:1200px;margin:0 auto;padding:20px;background:#f8f9fa;}";
    echo ".container{background:white;padding:30px;border-radius:10px;box-shadow:0 0 20px rgba(0,0,0,0.1);}";
    echo "</style></head><body><div class='container'>\n";
    echo "<h1 style='color: #007bff; text-align: center;'>🔧 Fix Product Assignment Requests Table</h1>\n";
}

try {
    // Include database configuration
    require_once 'includes/config.php';
    
    // Create PDO connection
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "<h2>1. Checking Current Table Structure</h2>\n";
    
    // Check if table exists
    $tableExistsStmt = $pdo->prepare("SHOW TABLES LIKE 'product_assignment_requests'");
    $tableExistsStmt->execute();
    $tableExists = $tableExistsStmt->fetch();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ Table 'product_assignment_requests' does not exist!</p>\n";
        echo "<p>Please run setup.php first to create the table.</p>\n";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Table 'product_assignment_requests' exists</p>\n";
    
    // Check current table structure
    $columnsStmt = $pdo->prepare("DESCRIBE product_assignment_requests");
    $columnsStmt->execute();
    $columns = $columnsStmt->fetchAll();
    
    $requestIdExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'request_id') {
            $requestIdExists = true;
            break;
        }
    }
    
    echo "<h2>2. Checking request_id Column</h2>\n";
    echo "<p>request_id column exists: " . ($requestIdExists ? '✅ Yes' : '❌ No') . "</p>\n";
    
    // Add request_id column if it doesn't exist
    if (!$requestIdExists) {
        echo "<h3>Adding request_id Column</h3>\n";
        try {
            echo "<p>🔄 Adding request_id column...</p>\n";
            $alterQuery = "ALTER TABLE product_assignment_requests ADD COLUMN request_id VARCHAR(30) UNIQUE NOT NULL AFTER id";
            $pdo->exec($alterQuery);
            echo "<p style='color: green;'>✅ request_id column added successfully!</p>\n";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Failed to add request_id column: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            throw $e;
        }
    }
    
    echo "<h2>3. Checking for Records with Empty request_id</h2>\n";
    
    // Check for records with empty request_id
    $emptyRequestIdStmt = $pdo->prepare("SELECT COUNT(*) as count FROM product_assignment_requests WHERE request_id = '' OR request_id IS NULL");
    $emptyRequestIdStmt->execute();
    $emptyCount = $emptyRequestIdStmt->fetch()['count'];
    
    echo "<p>Records with empty request_id: <strong>{$emptyCount}</strong></p>\n";
    
    if ($emptyCount > 0) {
        echo "<h3>Fixing Records with Empty request_id</h3>\n";
        
        // Get all records with empty request_id
        $emptyRecordsStmt = $pdo->prepare("SELECT id FROM product_assignment_requests WHERE request_id = '' OR request_id IS NULL ORDER BY id");
        $emptyRecordsStmt->execute();
        $emptyRecords = $emptyRecordsStmt->fetchAll();
        
        $updateStmt = $pdo->prepare("UPDATE product_assignment_requests SET request_id = ? WHERE id = ?");
        $fixedCount = 0;
        
        foreach ($emptyRecords as $record) {
            // Generate unique request ID
            $requestId = 'REQ' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Check if request ID already exists and generate a new one if needed
            $checkStmt = $pdo->prepare("SELECT id FROM product_assignment_requests WHERE request_id = ?");
            $checkStmt->execute([$requestId]);
            while ($checkStmt->fetch()) {
                $requestId = 'REQ' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $checkStmt->execute([$requestId]);
            }
            
            try {
                $updateStmt->execute([$requestId, $record['id']]);
                $fixedCount++;
                echo "<p style='color: green;'>✅ Fixed record ID {$record['id']} with request_id: {$requestId}</p>\n";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Failed to fix record ID {$record['id']}: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
        
        echo "<p style='color: green;'><strong>✅ Fixed {$fixedCount} out of {$emptyCount} records</strong></p>\n";
    } else {
        echo "<p style='color: green;'>✅ No records with empty request_id found</p>\n";
    }
    
    echo "<h2>4. Final Verification</h2>\n";
    
    // Final check
    $finalCheckStmt = $pdo->prepare("SELECT COUNT(*) as count FROM product_assignment_requests WHERE request_id = '' OR request_id IS NULL");
    $finalCheckStmt->execute();
    $finalEmptyCount = $finalCheckStmt->fetch()['count'];
    
    if ($finalEmptyCount == 0) {
        echo "<p style='color: green;'>✅ All records now have valid request_id values</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Still {$finalEmptyCount} records with empty request_id</p>\n";
    }
    
    // Test insert to make sure it works
    echo "<h2>5. Testing Insert Functionality</h2>\n";
    
    try {
        // Generate a test request ID
        $testRequestId = 'TEST' . time();
        
        // Try to insert a test record (we'll delete it immediately)
        $testInsertStmt = $pdo->prepare("INSERT INTO product_assignment_requests (request_id, franchise_id, user_id, product_id, quantity, description) VALUES (?, 1, 'TEST001', 1, 1, 'Test record')");
        $testInsertStmt->execute([$testRequestId]);
        
        // Delete the test record
        $deleteTestStmt = $pdo->prepare("DELETE FROM product_assignment_requests WHERE request_id = ?");
        $deleteTestStmt->execute([$testRequestId]);
        
        echo "<p style='color: green;'>✅ Insert functionality test successful!</p>\n";
    } catch (PDOException $e) {
        echo "<p style='color: orange;'>⚠️ Insert test failed (this might be due to foreign key constraints): " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    echo "<h2>6. Summary</h2>\n";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>✅ Migration Completed Successfully!</h3>\n";
    echo "<p>The following changes have been made to fix product assignment request errors:</p>\n";
    echo "<ul>\n";
    if (!$requestIdExists) {
        echo "<li>Added <code>request_id</code> column to product_assignment_requests table</li>\n";
    }
    if ($emptyCount > 0) {
        echo "<li>Fixed {$fixedCount} records with empty request_id values</li>\n";
    }
    echo "<li>Verified all records now have valid request_id values</li>\n";
    echo "<li>Tested insert functionality</li>\n";
    echo "</ul>\n";
    echo "<p><strong>The 'Duplicate entry for key request_id' error should now be fixed!</strong></p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>❌ Migration Failed</h3>\n";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check your database connection and try again.</p>\n";
    echo "</div>\n";
}

// End HTML output for web interface
if (!$isCLI) {
    echo "</div></body></html>\n";
}

return true;
?>
