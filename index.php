<?php
/**
 * Main Index Page - Modern E-commerce Homepage
 * ShaktiPure Industries
 */

// Enable error reporting in development
if (!defined('ENVIRONMENT') || ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Include essential files
require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/FileUpload.php';

// Check if user is already logged in and redirect accordingly
if (isLoggedIn()) {
    $userType = getCurrentUserType();
    switch ($userType) {
        case 'admin':
            header("Location: admin/dashboard.php");
            break;
        case 'franchise':
            header("Location: franchise/dashboard.php");
            break;
        case 'user':
            header("Location: user/dashboard.php");
            break;
        default:
            destroyUserSession();
            break;
    }
    exit();
}

// Get all active products for homepage
$db = Database::getInstance();
$productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' ORDER BY created_at DESC");
$productsStmt->execute();
$allProducts = $productsStmt->fetchAll();

// Get product count for display
$productCount = count($allProducts);

// For homepage, show first 12 products with option to view all
$homepageProductLimit = 12;
$displayProducts = array_slice($allProducts, 0, $homepageProductLimit);
$hasMoreProducts = $productCount > $homepageProductLimit;

$fileUpload = new FileUpload();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Shaktipure Industries Pvt Ltd is a leading manufacturer and exporter of high-quality water purifiers, water treatment plants, and water testing equipment. We offer a wide range of products for both residential and commercial use, ensuring safe and clean drinking water for all.">
    <title>Shaktipure Industries Pvt Ltd</title>
    <link rel="icon" type="image/png" href="assets/images/onlylogo.png">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="assets/css/homepage.css" rel="stylesheet">
</head>
<body>
    <!-- Top Header -->
    <div class="top-header">
        <div class="container">
            <div class="top-header-left">
                🔥 Welcome to Shaktipure Industries PVT. LTD.
            </div>
            <div class="top-header-right">
                <a href="tel:+91-9876543210"><i class="fas fa-phone"></i> +91-8460203679</a>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                <img src="assets/images/logo.png" alt="ShaktiPure" style="height: 40px; margin-right: 10px;" id="logo">
                <style>
                    @media (max-width: 770px) {
  #logo {
    display: none;
  }
}

@media (min-width: 770px) {
  #logo2 {
    display: none;
  }
}

/* Enhanced Product Card Styles for Homepage */
.product-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
    border: 2px solid transparent;
    border-radius: 12px;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.product-card .product-image img {
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.08);
}

.product-card:hover .product-title {
    color: #007bff;
}

.product-card:active {
    transform: translateY(-4px);
}

.pv-badge {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 8px;
    display: inline-block;
}

.product-hint {
    margin-top: 8px;
    text-align: center;
}

.product-hint small {
    font-weight: 500;
}

/* Add subtle animation to the click hint */
.product-card:hover .product-hint small {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
                </style>
                <div class="search-bar">
                    <form action="products.php" method="GET">
                        <input type="text" name="search" placeholder="Search for products, brands and more...">
                        <button type="submit"><i class="fas fa-search"></i></button>
                    </form>
                </div>
                
                <div class="header-actions">
                <img src="assets/images/logo.png" alt="ShaktiPure" style="height: 40px; margin-right: 10px;" id="logo2">
                    <a href="user/login.php" class="header-btn">
                        <i class="fas fa-user"></i>
                        <span>Account</span>
                    </a>
                    <a href="#" class="header-btn">
                        <i class="fas fa-heart"></i>
                        <span>Wishlist</span>
                    </a>
                    <a href="#" class="header-btn">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Cart</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navigation">
        <div class="container">
            <div class="nav-content">
                <button class="categories-btn">
                    <i class="fas fa-bars"></i>
                    PRODUCT CATEGORIES
                </button>
                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="#categories">Categories</a></li>
                    <li><a href="#products">Products</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Contact</a></li>
                    <li><a href="user/register.php">Register</a></li>
                    <li><a href="user/login.php">Login</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Banner Section - RootPure Style with Carousel -->
    <section class="hero-banner-section">
        <div class="hero-slider">
            <!-- Slide 1 -->
            <div class="hero-slide active">
                <div class="container">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Everyone <span class="text-green">Live Their Dreams</span> With Us</h1>
                            <p>Experience pure, healthy living with our premium water purification systems</p>
                            <a href="#products" class="btn-shop-now">Shopping Now</a>
                        </div>
                        <div class="hero-image">
                            <img src="assets/images/1.png" alt="Water Purifier" style="max-width: 300px;">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="hero-slide">
                <div class="container">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Be Safe <span class="text-green">With Shaktipure Gas Safe</span></h1>
                            <p>Safe with Us Make it ours today</p>
                            <a href="#products" class="btn-shop-now">Explore Products</a>
                        </div>
                        <div class="hero-image">
                            <img src="assets/images/gassafe.png" alt="Water Treatment" style="max-width: 300px;">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3 -->
            <div class="hero-slide">
                <div class="container">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Quality <span class="text-green">You Can Trust</span></h1>
                            <p>Leading manufacturer of water purifiers with 25+ years of excellence</p>
                            <a href="#products" class="btn-shop-now">Learn More</a>
                        </div>
                        <div class="hero-image">
                            <img src="assets/images/5.png" alt="Quality Products" style="max-width: 300px;">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carousel Navigation -->
            <div class="carousel-nav">
                <button class="carousel-btn prev" onclick="changeSlide(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="carousel-btn next" onclick="changeSlide(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <span class="indicator active" onclick="currentSlide(1)"></span>
                <span class="indicator" onclick="currentSlide(2)"></span>
                <span class="indicator" onclick="currentSlide(3)"></span>
            </div>
        </div>
    </section>

    <!-- Categories Section - RootPure Style -->
    <section class="categories-section" id="categories">
        <div class="container">
            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <h3>Agricultural</h3>
                    <a href="#" class="category-btn">Click Here</a>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3>Health Care</h3>
                    <a href="#" class="category-btn">Click Here</a>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3>Home Care</h3>
                    <a href="#" class="category-btn">Click Here</a>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3>Kitchen Essential</h3>
                    <a href="#" class="category-btn">Click Here</a>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3>Personal Care</h3>
                    <a href="#" class="category-btn">Click Here</a>
                </div>
                <div class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-tshirt"></i>
                    </div>
                    <h3>Clothing</h3>
                    <a href="#" class="category-btn">Click Here</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Top Products This Week Section -->
    <section class="top-products-section" id="products">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Top Product This Week</h2>
                <a href="products.php" class="show-all-btn">Show All Products</a>
            </div>
            <div class="products-grid">
                <?php if (!empty($displayProducts)): ?>
                    <?php foreach (array_slice($displayProducts, 0, 7) as $product): ?>
                        <div class="product-card" onclick="window.location.href='product-detail.php?id=<?php echo $product['id']; ?>'" style="cursor: pointer;">
                            <div class="product-image">
                                <?php if (!empty($product['image'])): ?>
                                    <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($product['image'])); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <img src="assets/images/placeholder-product.jpg" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>
                                <div class="product-hint">
                                    <small class="text-primary">
                                        <i class="fas fa-mouse-pointer me-1"></i>Click to view details
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-products">
                        <i class="fas fa-box-open"></i>
                        <h4>No Products Available</h4>
                        <p>No products found in the database. Please add products through the admin panel.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Popular Products Section -->
    <section class="popular-products-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Popular Products</h2>
            </div>
            <div class="products-grid">
                <?php if (!empty($displayProducts)): ?>
                    <?php foreach (array_slice($displayProducts, 0, 6) as $product): ?>
                        <div class="product-card" onclick="window.location.href='product-detail.php?id=<?php echo $product['id']; ?>'" style="cursor: pointer;">
                            <div class="product-image">
                                <?php if (!empty($product['image'])): ?>
                                    <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($product['image'])); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <img src="assets/images/placeholder-product.jpg" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="product-info">
                                <div class="product-price">
                                    <span class="current-price">₹<?php echo number_format($product['price'], 2); ?></span>
                                    <span class="pv-badge"><?php echo formatPV($product['pv_value']); ?> PV</span>
                                </div>
                                <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>
                                <div class="product-actions">
                                    <small class="text-primary">
                                        <i class="fas fa-mouse-pointer me-1"></i>Click to view details
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

        </div>
    </section>

    <!-- Product Tabs Section -->
    <section class="product-tabs-section">
        <div class="container">
            <div class="product-tabs">
                <div class="tab-content">
                    <div class="tab-pane active">
                        <h3 class="tab-title">New Arrivals</h3>
                        <div class="products-grid">
                            <?php if (!empty($displayProducts)): ?>
                                <?php foreach (array_slice($displayProducts, 0, 6) as $product): ?>
                                    <div class="product-card" onclick="window.location.href='product-detail.php?id=<?php echo $product['id']; ?>'" style="cursor: pointer;">
                                        <div class="product-image">
                                            <?php if (!empty($product['image'])): ?>
                                                <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($product['image'])); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                            <?php else: ?>
                                                <img src="assets/images/placeholder-product.jpg" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                            <?php endif; ?>
                                        </div>
                                        <div class="product-info">
                                            <h3 class="product-title"><?php echo htmlspecialchars($product['name']); ?></h3>
                                            <div class="product-price">
                                                <span class="current-price">₹<?php echo number_format($product['price'], 2); ?></span>
                                                <span class="pv-badge"><?php echo formatPV($product['pv_value']); ?> PV</span>
                                            </div>
                                            <div class="product-hint">
                                                <small class="text-primary">
                                                    <i class="fas fa-mouse-pointer me-1"></i>Click to view details
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer - RootPure Style -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ShaktiPure Industries</h3>
                    <p>Welcome to ShaktiPure Industries Pvt. Ltd. We would like to give you an excellent insight about our company and its capabilities. Our overall business attitude is shaped by our overall value care.</p>
                    <div class="footer-contact">
                        <p><strong>Address:</strong> D-224, Udhana Complex, Udhana, Surat-394210, Gujarat, India</p>
                        <p><strong>Phone:</strong> +91-8460203679</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>Information</h3>
                    <ul>
                        <li><a href="management.php">Management</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="privacy-policy.php">Privacy Policy</a></li>
                        <li><a href="terms-conditions.php">Terms & Conditions</a></li>
                        <li><a href="downloads.php">Downloads</a></li>
                        <li><a href="bankers.php">Our Bankers</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>My Account</h3>
                    <ul>
                        <li><a href="video-gallery.php">Video Gallery</a></li>
                        <li><a href="branches.php">Branches</a></li>
                        <li><a href="stockist.php">Stockist</a></li>
                        <li><a href="shipping-policy.php">Shipping Policy</a></li>
                        <li><a href="refund-policy.php">Refund Policy</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Follow Us</h3>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ShaktiPure Industries Pvt. Ltd. All Rights Reserved</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Categories button functionality
        document.querySelector('.categories-btn').addEventListener('click', function() {
            alert('Product categories feature coming soon!');
        });

        // Add to cart functionality
        document.querySelectorAll('.btn-add-cart').forEach(button => {
            button.addEventListener('click', function() {
                alert('Add to cart functionality will be implemented soon!');
            });
        });

        // Wishlist functionality
        document.querySelectorAll('.btn-wishlist').forEach(button => {
            button.addEventListener('click', function() {
                this.style.color = this.style.color === 'red' ? '' : 'red';
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Carousel functionality
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.hero-slide');
        const indicators = document.querySelectorAll('.indicator');
        const totalSlides = slides.length;
        let autoSlideInterval;

        function showSlide(index) {
            // Hide all slides
            slides.forEach(slide => slide.classList.remove('active'));
            indicators.forEach(indicator => indicator.classList.remove('active'));

            // Show current slide
            slides[index].classList.add('active');
            indicators[index].classList.add('active');
        }

        function changeSlide(direction) {
            currentSlideIndex += direction;

            if (currentSlideIndex >= totalSlides) {
                currentSlideIndex = 0;
            } else if (currentSlideIndex < 0) {
                currentSlideIndex = totalSlides - 1;
            }

            showSlide(currentSlideIndex);
            resetAutoSlide();
        }

        function currentSlide(index) {
            currentSlideIndex = index - 1;
            showSlide(currentSlideIndex);
            resetAutoSlide();
        }

        function nextSlide() {
            currentSlideIndex = (currentSlideIndex + 1) % totalSlides;
            showSlide(currentSlideIndex);
        }

        function startAutoSlide() {
            autoSlideInterval = setInterval(nextSlide, 5000); // Change slide every 5 seconds
        }

        function resetAutoSlide() {
            clearInterval(autoSlideInterval);
            startAutoSlide();
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ShaktiPure website loaded successfully!');

            // Start automatic carousel
            if (slides.length > 1) {
                startAutoSlide();

                // Pause auto-slide on hover
                const heroSection = document.querySelector('.hero-banner-section');
                heroSection.addEventListener('mouseenter', () => clearInterval(autoSlideInterval));
                heroSection.addEventListener('mouseleave', startAutoSlide);
            }
        });
    </script>
</body>
</html>
