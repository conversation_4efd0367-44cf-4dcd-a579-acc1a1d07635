<?php
/**
 * Production Environment Configuration
 * MLM Binary Plan System
 * 
 * This file contains production-specific settings and optimizations
 */

// Prevent direct access
if (!defined('ENVIRONMENT') || ENVIRONMENT !== 'production') {
    die('This file can only be loaded in production environment');
}

// Production Database Configuration
define('PROD_DB_HOST', 'localhost');
define('PROD_DB_NAME', 'shaktipure_mlm_prod');
define('PROD_DB_USER', 'mlm_user');
define('PROD_DB_PASS', 'secure_password_here');

// Production Security Settings
define('PROD_SESSION_TIMEOUT', 1800); // 30 minutes
define('PROD_MAX_LOGIN_ATTEMPTS', 3);
define('PROD_LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Production Performance Settings
define('PROD_CACHE_ENABLED', true);
define('PROD_CACHE_LIFETIME', 3600); // 1 hour
define('PROD_QUERY_CACHE_ENABLED', true);
define('PROD_COMPRESSION_ENABLED', true);

// Production Email Settings (use real SMTP)
define('PROD_SMTP_HOST', 'smtp.yourdomain.com');
define('PROD_SMTP_PORT', 587);
define('PROD_SMTP_USERNAME', '<EMAIL>');
define('PROD_SMTP_PASSWORD', 'email_password_here');
define('PROD_FROM_EMAIL', '<EMAIL>');
define('PROD_FROM_NAME', 'ShaktiPure MLM');

// Production Razorpay Settings (live mode)
define('PROD_RAZORPAY_KEY_ID', 'rzp_live_your_key_here');
define('PROD_RAZORPAY_KEY_SECRET', 'your_live_secret_here');
define('PROD_RAZORPAY_MODE', 'live');

// Production Site Settings
define('PROD_SITE_URL', 'https://yourdomain.com');
define('PROD_SITE_NAME', 'ShaktiPure MLM');
define('PROD_ADMIN_EMAIL', '<EMAIL>');

// Production File Upload Settings
define('PROD_UPLOAD_PATH', '/var/www/html/uploads/');
define('PROD_MAX_FILE_SIZE', 2097152); // 2MB for production
define('PROD_ALLOWED_EXTENSIONS', 'jpg,jpeg,png,gif,pdf,doc,docx');

// Production Logging Settings
define('PROD_LOG_LEVEL', 'ERROR'); // Only log errors in production
define('PROD_LOG_PATH', '/var/log/mlm/');
define('PROD_LOG_ROTATION', true);
define('PROD_LOG_MAX_SIZE', 10485760); // 10MB
define('PROD_LOG_MAX_FILES', 10);

// Production Cron Settings
define('PROD_CRON_ENABLED', true);
define('PROD_WEEKLY_MATCHING_DAY', 5); // friday
define('PROD_WEEKLY_MATCHING_HOUR', 23); // 11 PM
define('PROD_WEEKLY_MATCHING_MINUTE', 59); // 59 minutes (11:59 PM)
define('PROD_BATCH_SIZE', 500); // Larger batch size for production

// Production Rate Limiting
define('PROD_API_RATE_LIMIT', 100); // Requests per minute
define('PROD_LOGIN_RATE_LIMIT', 5); // Login attempts per minute

// Production Backup Settings
define('PROD_BACKUP_ENABLED', true);
define('PROD_BACKUP_PATH', '/var/backups/mlm/');
define('PROD_BACKUP_RETENTION_DAYS', 30);
define('PROD_BACKUP_SCHEDULE', 'daily'); // daily, weekly, monthly

// Production Monitoring Settings
define('PROD_MONITORING_ENABLED', true);
define('PROD_HEALTH_CHECK_URL', '/health-check.php');
define('PROD_UPTIME_MONITORING', true);

// Production CDN Settings
define('PROD_CDN_ENABLED', false);
define('PROD_CDN_URL', 'https://cdn.yourdomain.com');
define('PROD_STATIC_ASSETS_CDN', false);

// Production SSL Settings
define('PROD_FORCE_HTTPS', true);
define('PROD_HSTS_ENABLED', true);
define('PROD_HSTS_MAX_AGE', 31536000); // 1 year

// Production Error Handling
define('PROD_ERROR_REPORTING', false);
define('PROD_DISPLAY_ERRORS', false);
define('PROD_LOG_ERRORS', true);
define('PROD_ERROR_LOG_PATH', '/var/log/mlm/errors.log');

// Production Memory and Execution Limits
define('PROD_MEMORY_LIMIT', '256M');
define('PROD_MAX_EXECUTION_TIME', 30);
define('PROD_MAX_INPUT_TIME', 60);
define('PROD_POST_MAX_SIZE', '10M');
define('PROD_UPLOAD_MAX_FILESIZE', '2M');

// Production Database Connection Pool
define('PROD_DB_POOL_SIZE', 10);
define('PROD_DB_TIMEOUT', 30);
define('PROD_DB_CHARSET', 'utf8mb4');
define('PROD_DB_COLLATION', 'utf8mb4_unicode_ci');

// Production Cache Configuration
define('PROD_CACHE_TYPE', 'redis'); // redis, memcached, file
define('PROD_REDIS_HOST', 'localhost');
define('PROD_REDIS_PORT', 6379);
define('PROD_REDIS_PASSWORD', '');
define('PROD_REDIS_DATABASE', 0);

// Production Queue Configuration
define('PROD_QUEUE_ENABLED', true);
define('PROD_QUEUE_DRIVER', 'redis'); // redis, database
define('PROD_QUEUE_CONNECTION', 'default');

// Production Maintenance Mode
define('PROD_MAINTENANCE_MODE', false);
define('PROD_MAINTENANCE_MESSAGE', 'System is under maintenance. Please try again later.');
define('PROD_MAINTENANCE_ALLOWED_IPS', '127.0.0.1,::1');

// Production API Configuration
define('PROD_API_ENABLED', true);
define('PROD_API_VERSION', 'v1');
define('PROD_API_RATE_LIMIT_ENABLED', true);
define('PROD_API_AUTHENTICATION_REQUIRED', true);

// Production Notification Settings
define('PROD_NOTIFICATIONS_ENABLED', true);
define('PROD_EMAIL_NOTIFICATIONS', true);
define('PROD_SMS_NOTIFICATIONS', false);
define('PROD_PUSH_NOTIFICATIONS', false);

// Production Analytics
define('PROD_ANALYTICS_ENABLED', true);
define('PROD_GOOGLE_ANALYTICS_ID', 'GA-XXXXXXXXX');
define('PROD_FACEBOOK_PIXEL_ID', '');

// Production Security Headers
$productionSecurityHeaders = [
    'X-Frame-Options' => 'SAMEORIGIN',
    'X-Content-Type-Options' => 'nosniff',
    'X-XSS-Protection' => '1; mode=block',
    'Referrer-Policy' => 'strict-origin-when-cross-origin',
    'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com;",
    'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains'
];

// Apply production security headers
if (ENVIRONMENT === 'production') {
    foreach ($productionSecurityHeaders as $header => $value) {
        header("{$header}: {$value}");
    }
}

// Production PHP Configuration
if (ENVIRONMENT === 'production') {
    // Error reporting
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', PROD_ERROR_LOG_PATH);
    
    // Memory and execution limits
    ini_set('memory_limit', PROD_MEMORY_LIMIT);
    ini_set('max_execution_time', PROD_MAX_EXECUTION_TIME);
    ini_set('max_input_time', PROD_MAX_INPUT_TIME);
    ini_set('post_max_size', PROD_POST_MAX_SIZE);
    ini_set('upload_max_filesize', PROD_UPLOAD_MAX_FILESIZE);
    
    // Session security
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    // File upload security
    ini_set('file_uploads', 1);
    ini_set('upload_tmp_dir', '/tmp');
    
    // Disable dangerous functions
    ini_set('disable_functions', 'exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source');
    
    // Hide PHP version
    ini_set('expose_php', 0);
    
    // Output compression
    if (PROD_COMPRESSION_ENABLED) {
        ini_set('zlib.output_compression', 1);
        ini_set('zlib.output_compression_level', 6);
    }
}

// Production Database Configuration Class
class ProductionDatabase {
    private static $instance = null;
    private $connections = [];
    private $maxConnections = PROD_DB_POOL_SIZE;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        // Implement connection pooling logic here
        if (count($this->connections) < $this->maxConnections) {
            $connection = new PDO(
                "mysql:host=" . PROD_DB_HOST . ";dbname=" . PROD_DB_NAME . ";charset=" . PROD_DB_CHARSET,
                PROD_DB_USER,
                PROD_DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_TIMEOUT => PROD_DB_TIMEOUT,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . PROD_DB_CHARSET . " COLLATE " . PROD_DB_COLLATION
                ]
            );
            
            $this->connections[] = $connection;
            return $connection;
        }
        
        // Return existing connection from pool
        return $this->connections[array_rand($this->connections)];
    }
}

// Production Cache Configuration
class ProductionCache {
    private static $instance = null;
    private $cache = null;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        if (PROD_CACHE_ENABLED) {
            switch (PROD_CACHE_TYPE) {
                case 'redis':
                    if (class_exists('Redis')) {
                        $this->cache = new Redis();
                        $this->cache->connect(PROD_REDIS_HOST, PROD_REDIS_PORT);
                        if (PROD_REDIS_PASSWORD) {
                            $this->cache->auth(PROD_REDIS_PASSWORD);
                        }
                        $this->cache->select(PROD_REDIS_DATABASE);
                    }
                    break;
                    
                case 'memcached':
                    if (class_exists('Memcached')) {
                        $this->cache = new Memcached();
                        $this->cache->addServer('localhost', 11211);
                    }
                    break;
                    
                default:
                    // File-based cache fallback
                    $this->cache = null;
            }
        }
    }
    
    public function get($key) {
        if ($this->cache) {
            return $this->cache->get($key);
        }
        return false;
    }
    
    public function set($key, $value, $ttl = null) {
        if ($this->cache) {
            $ttl = $ttl ?: PROD_CACHE_LIFETIME;
            return $this->cache->setex($key, $ttl, serialize($value));
        }
        return false;
    }
    
    public function delete($key) {
        if ($this->cache) {
            return $this->cache->del($key);
        }
        return false;
    }
}

// Production Health Check
function productionHealthCheck() {
    $status = [
        'status' => 'healthy',
        'timestamp' => date('Y-m-d H:i:s'),
        'checks' => []
    ];
    
    // Database check
    try {
        $db = ProductionDatabase::getInstance()->getConnection();
        $stmt = $db->query('SELECT 1');
        $status['checks']['database'] = 'healthy';
    } catch (Exception $e) {
        $status['checks']['database'] = 'unhealthy';
        $status['status'] = 'unhealthy';
    }
    
    // Cache check
    if (PROD_CACHE_ENABLED) {
        try {
            $cache = ProductionCache::getInstance();
            $cache->set('health_check', 'test', 60);
            $result = $cache->get('health_check');
            $status['checks']['cache'] = ($result === 'test') ? 'healthy' : 'unhealthy';
        } catch (Exception $e) {
            $status['checks']['cache'] = 'unhealthy';
            $status['status'] = 'unhealthy';
        }
    }
    
    // Disk space check
    $freeSpace = disk_free_space('/');
    $totalSpace = disk_total_space('/');
    $usagePercent = (($totalSpace - $freeSpace) / $totalSpace) * 100;
    
    if ($usagePercent > 90) {
        $status['checks']['disk_space'] = 'unhealthy';
        $status['status'] = 'unhealthy';
    } else {
        $status['checks']['disk_space'] = 'healthy';
    }
    
    return $status;
}
?>
