<?php
/**
 * Diagnose Income Generation Issues
 * Debug script to identify problems with income generation
 */

require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';
require_once '../config/config.php';
require_once '../includes/WeeklyDateHelper.php';

// Initialize classes
$db = Database::getInstance();
$pvSystem = new PVSystem();
$wallet = new Wallet();
$config = Config::getInstance();

echo "<h2>Income Generation Diagnostic</h2>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// Test dates (Saturday-Friday weeks)
$previousWeek = WeeklyDateHelper::getPreviousWeek();
$weekStart = $previousWeek['start'];
$weekEnd = $previousWeek['end'];

echo "<h3>Test Parameters:</h3>";
echo "<ul>";
echo "<li>Week Start: {$weekStart}</li>";
echo "<li>Week End: {$weekEnd}</li>";
echo "<li>PV Rate: ₹" . $config->getPVRate() . "</li>";
echo "<li>Weekly Capping: ₹" . number_format($config->get('weekly_capping', 130000)) . "</li>";
echo "</ul>";

// Check for active users with PV
echo "<h3>1. Active Users with PV Transactions:</h3>";
try {
    $userPVStmt = $db->query("
        SELECT u.user_id, u.full_name, u.email,
               COUNT(pt.id) as total_transactions,
               SUM(CASE WHEN pt.side = 'left' THEN pt.pv_amount ELSE 0 END) as left_pv,
               SUM(CASE WHEN pt.side = 'right' THEN pt.pv_amount ELSE 0 END) as right_pv
        FROM users u
        LEFT JOIN pv_transactions pt ON u.user_id = pt.user_id
        WHERE u.status = 'active'
        GROUP BY u.user_id, u.full_name, u.email
        HAVING COUNT(pt.id) > 0
        ORDER BY (SUM(CASE WHEN pt.side = 'left' THEN pt.pv_amount ELSE 0 END) + SUM(CASE WHEN pt.side = 'right' THEN pt.pv_amount ELSE 0 END)) DESC
        LIMIT 10
    ");
    $usersWithPV = $userPVStmt->fetchAll();
    
    if (!empty($usersWithPV)) {
        echo "<table>";
        echo "<tr><th>User ID</th><th>Name</th><th>Transactions</th><th>Left PV</th><th>Right PV</th><th>Total PV</th></tr>";
        foreach ($usersWithPV as $user) {
            $totalPV = $user['left_pv'] + $user['right_pv'];
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>{$user['total_transactions']}</td>";
            echo "<td>" . number_format($user['left_pv'], 2) . "</td>";
            echo "<td>" . number_format($user['right_pv'], 2) . "</td>";
            echo "<td>" . number_format($totalPV, 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠ No active users with PV transactions found!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
}

// Check PV usage tracking
echo "<h3>2. PV Usage Tracking Status:</h3>";
try {
    $trackingStmt = $db->query("
        SELECT 
            COUNT(*) as total_tracking_records,
            SUM(original_amount) as total_original,
            SUM(used_amount) as total_used,
            SUM(remaining_amount) as total_remaining,
            COUNT(CASE WHEN remaining_amount > 0 THEN 1 END) as available_records
        FROM pv_usage_tracking
    ");
    $tracking = $trackingStmt->fetch();
    
    echo "<ul>";
    echo "<li>Total Tracking Records: {$tracking['total_tracking_records']}</li>";
    echo "<li>Total Original PV: " . number_format($tracking['total_original'] ?? 0, 2) . "</li>";
    echo "<li>Total Used PV: " . number_format($tracking['total_used'] ?? 0, 2) . "</li>";
    echo "<li>Total Remaining PV: " . number_format($tracking['total_remaining'] ?? 0, 2) . "</li>";
    echo "<li>Available Records: {$tracking['available_records']}</li>";
    echo "</ul>";
    
    if ($tracking['total_tracking_records'] == 0) {
        echo "<p class='warning'>⚠ No PV usage tracking records found. This might be the issue!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
}

// Test a specific user's income calculation
if (!empty($usersWithPV)) {
    $testUser = $usersWithPV[0];
    echo "<h3>3. Test Income Calculation for User: {$testUser['full_name']} ({$testUser['user_id']}):</h3>";
    
    try {
        // Check if already processed
        $existingStmt = $db->prepare("SELECT id FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
        $existingStmt->execute([$testUser['user_id'], $weekStart]);
        $existing = $existingStmt->fetch();
        
        if ($existing) {
            echo "<p class='info'>ℹ User already processed for this week. Checking existing record...</p>";
            
            $logStmt = $db->prepare("SELECT * FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
            $logStmt->execute([$testUser['user_id'], $weekStart]);
            $log = $logStmt->fetch();
            
            echo "<ul>";
            echo "<li>Left PV: " . number_format($log['left_pv'], 2) . "</li>";
            echo "<li>Right PV: " . number_format($log['right_pv'], 2) . "</li>";
            echo "<li>Matched PV: " . number_format($log['matched_pv'], 2) . "</li>";
            echo "<li>Gross Income: ₹" . number_format($log['gross_income_amount'], 2) . "</li>";
            echo "<li>Service Charge: ₹" . number_format($log['service_charge'], 2) . "</li>";
            echo "<li>TDS: ₹" . number_format($log['tds_amount'], 2) . "</li>";
            echo "<li>Net Income: ₹" . number_format($log['income_amount'], 2) . "</li>";
            echo "<li>Capping Applied: ₹" . number_format($log['weekly_capping_applied'], 2) . "</li>";
            echo "</ul>";
        } else {
            echo "<p class='info'>ℹ User not yet processed. Getting available PV...</p>";
            
            // Get available PV
            $availablePV = $pvSystem->getAvailablePVForWeek($testUser['user_id'], $weekStart);
            echo "<ul>";
            echo "<li>Available Left PV: " . number_format($availablePV['left_pv'], 2) . "</li>";
            echo "<li>Available Right PV: " . number_format($availablePV['right_pv'], 2) . "</li>";
            echo "<li>Potential Matched PV: " . number_format(min($availablePV['left_pv'], $availablePV['right_pv']), 2) . "</li>";
            echo "</ul>";
            
            if ($availablePV['left_pv'] == 0 && $availablePV['right_pv'] == 0) {
                echo "<p class='warning'>⚠ No available PV found for this user!</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error testing user: " . $e->getMessage() . "</p>";
    }
}

// Check wallet functionality
echo "<h3>4. Wallet System Test:</h3>";
try {
    $walletStmt = $db->query("
        SELECT 
            COUNT(*) as total_wallets,
            SUM(balance) as total_balance,
            COUNT(CASE WHEN balance > 0 THEN 1 END) as wallets_with_balance
        FROM wallet
    ");
    $walletStats = $walletStmt->fetch();
    
    echo "<ul>";
    echo "<li>Total Wallets: {$walletStats['total_wallets']}</li>";
    echo "<li>Total Balance: ₹" . number_format($walletStats['total_balance'] ?? 0, 2) . "</li>";
    echo "<li>Wallets with Balance: {$walletStats['wallets_with_balance']}</li>";
    echo "</ul>";
    
    // Check recent wallet transactions
    $recentStmt = $db->query("
        SELECT COUNT(*) as count
        FROM wallet_transactions
        WHERE transaction_type = 'credit'
        AND description LIKE '%Weekly PV Matching%'
        AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    $recentCount = $recentStmt->fetch()['count'];
    
    echo "<p>Recent Weekly PV Matching Credits (last 7 days): {$recentCount}</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking wallet: " . $e->getMessage() . "</p>";
}

// Check recent reports
echo "<h3>5. Recent Report Generation:</h3>";
try {
    $reportStmt = $db->query("
        SELECT *
        FROM weekly_income_reports
        ORDER BY report_generated_at DESC
        LIMIT 5
    ");
    $reports = $reportStmt->fetchAll();
    
    if (!empty($reports)) {
        echo "<table>";
        echo "<tr><th>Week</th><th>Users Earned</th><th>Total Income</th><th>Status</th><th>Generated</th></tr>";
        foreach ($reports as $report) {
            echo "<tr>";
            echo "<td>" . date('M d', strtotime($report['week_start_date'])) . " - " . date('M d, Y', strtotime($report['week_end_date'])) . "</td>";
            echo "<td>{$report['total_users_earned']}</td>";
            echo "<td>₹" . number_format($report['total_income_distributed'], 2) . "</td>";
            echo "<td>" . ucfirst($report['report_status']) . "</td>";
            echo "<td>" . date('M d, Y H:i', strtotime($report['report_generated_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠ No reports found!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking reports: " . $e->getMessage() . "</p>";
}

echo "<h3>Diagnostic Complete</h3>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>If no PV usage tracking records exist, run PV initialization</li>";
echo "<li>If users have PV but no available PV, check PV usage tracking</li>";
echo "<li>If available PV exists but no income generated, check processing logic</li>";
echo "<li>If income calculated but not credited, check wallet system</li>";
echo "</ul>";
?>
