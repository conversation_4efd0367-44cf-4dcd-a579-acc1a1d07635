<?php
/**
 * Contact Page
 * ShaktiPure Industries Pvt Ltd
 */

require_once 'includes/header.php';
renderHeader('Contact Us - ShaktiPure Industries Pvt Ltd');
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="page-header-content">
            <h1>Contact Us</h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Contact</span>
            </nav>
        </div>
    </div>
</section>

<!-- Contact Content -->
<section class="contact-section">
    <div class="container">
        <div class="contact-intro">
            <h2>Get in Touch</h2>
            <p>We're here to help! Contact us for product inquiries, technical support, or any questions about our water purification solutions.</p>
        </div>

        <div class="contact-content">
            <!-- Contact Form -->
            <div class="contact-form-section">
                <h3>Send us a Message</h3>
                <form class="contact-form" onsubmit="alert('Contact form will be functional soon!'); return false;">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject *</label>
                            <select id="subject" name="subject" required>
                                <option value="">Select Subject</option>
                                <option value="product-inquiry">Product Inquiry</option>
                                <option value="technical-support">Technical Support</option>
                                <option value="service-request">Service Request</option>
                                <option value="complaint">Complaint</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="message">Message *</label>
                        <textarea id="message" name="message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-paper-plane"></i>
                        Send Message
                    </button>
                </form>
            </div>

            <!-- Contact Information -->
            <div class="contact-info-section">
                <h3>Contact Information</h3>
                
                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="contact-details">
                        <h4>Head Office</h4>
                        <p>D-224, Udhana Complex, Udhana<br>Surat-394210, Gujarat, India</p>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="contact-details">
                        <h4>Phone</h4>
                        <p><a href="tel:+************">+91-8460203679</a></p>
                        <small>Mon - Sat: 9:00 AM - 6:00 PM</small>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-details">
                        <h4>Email</h4>
                        <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <small>Response within 24 hours</small>
                    </div>
                </div>

                <div class="contact-card">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="contact-details">
                        <h4>Business Hours</h4>
                        <p>Monday - Saturday<br>9:00 AM - 6:00 PM</p>
                        <small>Sunday: Closed</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Contact Options -->
        <div class="quick-contact">
            <h3>Quick Contact Options</h3>
            <div class="quick-contact-grid">
                <a href="tel:+************" class="quick-contact-item">
                    <div class="quick-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="quick-text">
                        <h4>Call Now</h4>
                        <p>Immediate assistance</p>
                    </div>
                </a>

                <a href="mailto:<EMAIL>" class="quick-contact-item">
                    <div class="quick-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="quick-text">
                        <h4>Email Us</h4>
                        <p>Detailed inquiries</p>
                    </div>
                </a>

                <a href="#" class="quick-contact-item" onclick="alert('WhatsApp integration coming soon!')">
                    <div class="quick-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="quick-text">
                        <h4>WhatsApp</h4>
                        <p>Quick messages</p>
                    </div>
                </a>

                <a href="branches.php" class="quick-contact-item">
                    <div class="quick-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="quick-text">
                        <h4>Visit Store</h4>
                        <p>Find nearest branch</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</section>

<?php renderFooter(); ?>

<style>
/* Page Header */
.page-header {
    background: var(--gradient-primary);
    padding: 60px 0;
    color: white;
}

.page-header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
}

.contact-intro {
    text-align: center;
    margin-bottom: 60px;
}

.contact-intro h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.contact-intro p {
    font-size: 16px;
    color: var(--text-gray-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-bottom: 80px;
}

/* Contact Form */
.contact-form-section h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 30px;
}

.contact-form {
    background: white;
    padding: 40px;
    border-radius: var(--card-border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-dark);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--button-border-radius);
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-green);
}

.submit-btn {
    background: var(--primary-green);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: var(--button-border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Contact Information */
.contact-info-section h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 30px;
}

.contact-card {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.contact-icon {
    background: var(--primary-green);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h4 {
    color: var(--text-dark);
    margin-bottom: 8px;
}

.contact-details p {
    color: var(--text-gray-light);
    margin-bottom: 5px;
    line-height: 1.5;
}

.contact-details a {
    color: var(--primary-green);
    text-decoration: none;
}

.contact-details a:hover {
    text-decoration: underline;
}

.contact-details small {
    color: var(--text-gray-light);
    font-size: 12px;
}

/* Quick Contact */
.quick-contact {
    text-align: center;
}

.quick-contact h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 40px;
}

.quick-contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.quick-contact-item {
    background: white;
    padding: 30px 20px;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.quick-contact-item:hover {
    transform: translateY(-5px);
    color: inherit;
    text-decoration: none;
}

.quick-icon {
    background: var(--primary-green);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.quick-text h4 {
    color: var(--text-dark);
    margin-bottom: 5px;
}

.quick-text p {
    color: var(--text-gray-light);
    font-size: 14px;
    margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .quick-contact-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .quick-contact-grid {
        grid-template-columns: 1fr;
    }
}
</style>
