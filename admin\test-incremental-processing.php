<?php
/**
 * Test Incremental Processing
 * Test script to verify incremental processing of remaining PVs works correctly
 */

require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';
require_once '../config/config.php';
require_once '../includes/WeeklyDateHelper.php';

// Initialize classes
$db = Database::getInstance();
$pvSystem = new PVSystem();
$wallet = new Wallet();
$config = Config::getInstance();

echo "<h2>Incremental Processing Test</h2>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// Test parameters (Saturday-Friday weeks)
$currentWeek = WeeklyDateHelper::getCurrentWeek();
$testWeekStart = $currentWeek['start'];
$testWeekEnd = $currentWeek['end'];

echo "<h3>Test Parameters:</h3>";
echo "<ul>";
echo "<li>Test Week: {$testWeekStart} to {$testWeekEnd}</li>";
echo "<li>PV Rate: ₹" . $config->getPVRate() . "</li>";
echo "</ul>";

// Step 1: Check if there's already a report for this week
echo "<h3>Step 1: Check Existing Report</h3>";
try {
    $existingReportStmt = $db->prepare("SELECT * FROM weekly_income_reports WHERE week_start_date = ?");
    $existingReportStmt->execute([$testWeekStart]);
    $existingReport = $existingReportStmt->fetch();
    
    if ($existingReport) {
        echo "<p class='info'>ℹ Existing report found for this week:</p>";
        echo "<ul>";
        echo "<li>Users Earned: {$existingReport['total_users_earned']}</li>";
        echo "<li>Total Income Distributed: ₹" . number_format($existingReport['total_income_distributed'], 2) . "</li>";
        echo "<li>Report Status: " . ucfirst($existingReport['report_status']) . "</li>";
        echo "<li>Generated At: " . date('M d, Y H:i', strtotime($existingReport['report_generated_at'])) . "</li>";
        echo "</ul>";
        
        $hasExistingReport = true;
    } else {
        echo "<p class='warning'>⚠ No existing report found for this week. Will create new report.</p>";
        $hasExistingReport = false;
    }
}
 catch (Exception $e) {
    echo "<p class='error'>✗ Error checking existing report: " . $e->getMessage() . "</p>";
    exit;
}

// Step 2: Check users with remaining PV
echo "<h3>Step 2: Check Users with Remaining PV</h3>";
try {
    $remainingPVStmt = $db->query("
        SELECT u.user_id, u.full_name,
               SUM(CASE WHEN put.side = 'left' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) as left_pv,
               SUM(CASE WHEN put.side = 'right' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) as right_pv
        FROM users u
        JOIN pv_usage_tracking put ON u.user_id = put.user_id
        WHERE u.status = 'active' AND put.remaining_amount > 0
        GROUP BY u.user_id, u.full_name, u.email
        HAVING SUM(CASE WHEN put.side = 'left' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) > 0 
           AND SUM(CASE WHEN put.side = 'right' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) > 0
        ORDER BY LEAST(SUM(CASE WHEN put.side = 'left' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END), 
                      SUM(CASE WHEN put.side = 'right' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END)) DESC
        LIMIT 10
    ");
    $usersWithRemainingPV = $remainingPVStmt->fetchAll();
    
    if (!empty($usersWithRemainingPV)) {
        echo "<p class='success'>✓ Found " . count($usersWithRemainingPV) . " users with remaining PV for processing</p>";
        
        echo "<table>";
        echo "<tr><th>User ID</th><th>Name</th><th>Left PV</th><th>Right PV</th><th>Potential Match</th></tr>";
        foreach ($usersWithRemainingPV as $user) {
            $potentialMatch = min($user['left_pv'], $user['right_pv']);
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>" . number_format($user['left_pv'], 2) . "</td>";
            echo "<td>" . number_format($user['right_pv'], 2) . "</td>";
            echo "<td>" . number_format($potentialMatch, 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠ No users found with remaining PV for processing</p>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>All available PV has been processed</li>";
        echo "<li>Users don't have matching PV on both sides</li>";
        echo "<li>PV usage tracking needs to be initialized</li>";
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking remaining PV: " . $e->getMessage() . "</p>";
}

// Step 3: Test incremental processing
if (!empty($usersWithRemainingPV)) {
    echo "<h3>Step 3: Test Incremental Processing</h3>";
    
    try {
        echo "<p>Running incremental processing for remaining PVs...</p>";
        
        // Get totals before processing
        $beforeStmt = $db->prepare("SELECT total_users_earned, total_income_distributed FROM weekly_income_reports WHERE week_start_date = ?");
        $beforeStmt->execute([$testWeekStart]);
        $beforeTotals = $beforeStmt->fetch();
        
        $usersBefore = $beforeTotals['total_users_earned'] ?? 0;
        $incomeBefore = $beforeTotals['total_income_distributed'] ?? 0;
        
        echo "<p>Before incremental processing:</p>";
        echo "<ul>";
        echo "<li>Users Earned: {$usersBefore}</li>";
        echo "<li>Total Income: ₹" . number_format($incomeBefore, 2) . "</li>";
        echo "</ul>";
        
        // Run incremental processing
        $result = $pvSystem->runWeeklyMatching($testWeekStart, $testWeekEnd, true);
        
        if ($result && is_array($result)) {
            echo "<p class='success'>✓ Incremental processing completed!</p>";
            
            echo "<h4>Processing Results:</h4>";
            echo "<ul>";
            echo "<li>Processed Users: {$result['processed']}</li>";
            echo "<li>Users with New Income: {$result['users_with_income']}</li>";
            echo "<li>Skipped Users (already processed): " . ($result['skipped_users'] ?? 0) . "</li>";
            echo "<li>Additional Gross Income: ₹" . number_format($result['total_gross_income'], 2) . "</li>";
            echo "<li>Additional Service Charge: ₹" . number_format($result['total_service_charge'], 2) . "</li>";
            echo "<li>Additional TDS: ₹" . number_format($result['total_tds_amount'], 2) . "</li>";
            echo "<li>Additional Net Income: ₹" . number_format($result['total_income'], 2) . "</li>";
            echo "<li>Additional Capping Applied: ₹" . number_format($result['total_capping'], 2) . "</li>";
            
            if (isset($result['error_count'])) {
                echo "<li>Processing Errors: {$result['error_count']}</li>";
            }
            echo "</ul>";
            
            // Get totals after processing
            $afterStmt = $db->prepare("SELECT total_users_earned, total_income_distributed FROM weekly_income_reports WHERE week_start_date = ?");
            $afterStmt->execute([$testWeekStart]);
            $afterTotals = $afterStmt->fetch();
            
            $usersAfter = $afterTotals['total_users_earned'] ?? 0;
            $incomeAfter = $afterTotals['total_income_distributed'] ?? 0;
            
            echo "<p>After incremental processing:</p>";
            echo "<ul>";
            echo "<li>Total Users Earned: {$usersAfter} (+" . ($usersAfter - $usersBefore) . ")</li>";
            echo "<li>Total Income: ₹" . number_format($incomeAfter, 2) . " (+₹" . number_format($incomeAfter - $incomeBefore, 2) . ")</li>";
            echo "</ul>";
            
            if (abs(($incomeAfter - $incomeBefore) - $result['total_income']) < 0.01) {
                echo "<p class='success'>✓ Report totals updated correctly!</p>";
            } else {
                echo "<p class='error'>✗ Report totals mismatch!</p>";
            }
            
        } else {
            echo "<p class='error'>✗ Incremental processing failed!</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Error during incremental processing: " . $e->getMessage() . "</p>";
    }
}

// Step 4: Summary and recommendations
echo "<h3>Step 4: Test Summary</h3>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
echo "<h4>What This Test Verified:</h4>";
echo "<ul>";
echo "<li>✓ Detection of existing reports for the same week</li>";
echo "<li>✓ Identification of users with remaining PV</li>";
echo "<li>✓ Incremental processing of remaining PVs only</li>";
echo "<li>✓ Proper updating of report totals</li>";
echo "<li>✓ Skipping of already processed users</li>";
echo "<li>✓ Addition of new income to existing totals</li>";
echo "</ul>";

echo "<h4>How Incremental Processing Works:</h4>";
echo "<ol>";
echo "<li><strong>Detection:</strong> System detects existing report for the week</li>";
echo "<li><strong>Remaining PV:</strong> Identifies users with unused PV</li>";
echo "<li><strong>Processing:</strong> Processes only remaining PVs, skips already processed users</li>";
echo "<li><strong>Accumulation:</strong> Adds new income to existing report totals</li>";
echo "<li><strong>Logging:</strong> Updates individual user logs with additional income</li>";
echo "</ol>";

echo "<h4>Benefits:</h4>";
echo "<ul>";
echo "<li>✓ No duplicate processing of the same PV</li>";
echo "<li>✓ Processes new PV added after initial report generation</li>";
echo "<li>✓ Maintains accurate cumulative totals</li>";
echo "<li>✓ Allows multiple report generations for the same week</li>";
echo "</ul>";
echo "</div>";

echo "<p class='success'><strong>Incremental Processing Test Complete!</strong></p>";
echo "<p><a href='weekly-income-reports.php'>← Back to Weekly Income Reports</a></p>";
?>
