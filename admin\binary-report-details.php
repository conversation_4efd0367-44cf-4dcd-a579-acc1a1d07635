<?php
/**
 * Binary Report Details - Admin Panel
 * View detailed information about a specific binary report
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/NewBinaryPVSystem.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Get database instance
$db = Database::getInstance();

// Get report ID from URL
$reportId = $_GET['report_id'] ?? '';

if (empty($reportId)) {
    header('Location: binary-reports.php');
    exit;
}

// Get report details
$reportStmt = $db->prepare("SELECT * FROM binary_reports WHERE report_id = ?");
$reportStmt->execute([$reportId]);
$report = $reportStmt->fetch();

if (!$report) {
    header('Location: binary-reports.php?error=report_not_found');
    exit;
}

// Get detailed income logs for this report
$logsStmt = $db->prepare("
    SELECT bil.*, u.full_name, u.email 
    FROM binary_income_logs bil
    JOIN users u ON bil.user_id = u.user_id
    WHERE bil.report_id = ?
    ORDER BY bil.income_amount DESC, u.full_name
");
$logsStmt->execute([$reportId]);
$incomeLogs = $logsStmt->fetchAll();

// Calculate statistics
$totalUsers = count($incomeLogs);
$usersWithIncome = count(array_filter($incomeLogs, function($log) { return $log['income_amount'] > 0; }));
$avgIncome = ($usersWithIncome > 0) ? $report['total_income_generated'] / $usersWithIncome : 0;
$totalLeftPV = array_sum(array_column($incomeLogs, 'left_pv'));
$totalRightPV = array_sum(array_column($incomeLogs, 'right_pv'));
$totalMatchedPV = array_sum(array_column($incomeLogs, 'matched_pv'));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary Report Details - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Binary Report Details</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="binary-reports.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Reports
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Report Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Report Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Report ID:</strong></td>
                                        <td><code><?php echo htmlspecialchars($report['report_id']); ?></code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Report Date:</strong></td>
                                        <td><?php echo date('M d, Y', strtotime($report['report_date'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'completed' => 'success',
                                                'processing' => 'warning',
                                                'failed' => 'danger'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass[$report['status']] ?? 'secondary'; ?>">
                                                <?php echo ucfirst($report['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Generated:</strong></td>
                                        <td><?php echo date('M d, Y H:i:s', strtotime($report['created_at'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Users Processed:</strong></td>
                                        <td><?php echo number_format($report['users_processed']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Income Generated:</strong></td>
                                        <td><strong>₹<?php echo number_format($report['total_income_generated'], 2); ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Average Income:</strong></td>
                                        <td>₹<?php echo number_format($avgIncome, 2); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Users with Income:</strong></td>
                                        <td><?php echo number_format($usersWithIncome); ?> of <?php echo number_format($totalUsers); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- PV Statistics -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card text-white bg-primary">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Left PV</h5>
                                <h3><?php echo number_format($totalLeftPV, 2); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-white bg-success">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Right PV</h5>
                                <h3><?php echo number_format($totalRightPV, 2); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-white bg-warning">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Matched PV</h5>
                                <h3><?php echo number_format($totalMatchedPV, 2); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Income Logs -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Individual User Income Details</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($incomeLogs)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Income Records Found</h5>
                                <p class="text-muted">This report has no individual income records.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>User</th>
                                            <th>Left PV</th>
                                            <th>Right PV</th>
                                            <th>Matched PV</th>
                                            <th>Income Amount</th>
                                            <th>Processed</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($incomeLogs as $log): ?>
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($log['full_name']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($log['user_id']); ?></small>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($log['email']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo number_format($log['left_pv'], 2); ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success"><?php echo number_format($log['right_pv'], 2); ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning text-dark"><?php echo number_format($log['matched_pv'], 2); ?></span>
                                                </td>
                                                <td>
                                                    <strong>₹<?php echo number_format($log['income_amount'], 2); ?></strong>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo date('M d, Y H:i', strtotime($log['created_at'])); ?>
                                                    </small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
