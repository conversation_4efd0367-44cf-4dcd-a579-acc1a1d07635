<?php
/**
 * Test Script: Product Visual Improvements
 * 
 * This script tests the product card improvements:
 * 1. Clickable product cards
 * 2. No description on product listing pages
 * 3. Full details on product detail page
 */

require_once 'config/Connection.php';

echo "<h2>Product Visual Improvements Test</h2>\n";
echo "<p>Testing the product card and detail page improvements...</p>\n";

try {
    $db = Database::getInstance();
    
    // Test 1: Check if we have products to test with
    echo "<h3>Test 1: Product Availability</h3>\n";
    
    $productStmt = $db->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $productCount = $productStmt->fetchColumn();
    
    echo "<p><strong>Active products in database:</strong> {$productCount}</p>\n";
    
    if ($productCount > 0) {
        // Get sample products
        $sampleStmt = $db->query("SELECT id, name, product_code, description, price, pv_value FROM products WHERE status = 'active' LIMIT 3");
        $sampleProducts = $sampleStmt->fetchAll();
        
        echo "<p style='color: green;'>✓ Found products to test with</p>\n";
        
        echo "<h4>Sample Products:</h4>\n";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Name</th><th>Code</th><th>Price</th><th>PV</th><th>Description Length</th>";
        echo "</tr>\n";
        
        foreach ($sampleProducts as $product) {
            $descLength = strlen($product['description']);
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['product_code']) . "</td>";
            echo "<td>₹" . number_format($product['price'], 2) . "</td>";
            echo "<td>{$product['pv_value']} PV</td>";
            echo "<td>{$descLength} chars</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
    } else {
        echo "<p style='color: orange;'>⚠ No active products found. Creating a test product...</p>\n";
        
        // Create a test product
        $testProductData = [
            'name' => 'Test Product ' . time(),
            'product_code' => 'TEST' . time(),
            'description' => 'This is a comprehensive test product description that should be displayed only on the product detail page, not on the product listing cards. It contains detailed information about the product features, benefits, and usage instructions.',
            'price' => 999.99,
            'pv_value' => 100.00,
            'status' => 'active'
        ];
        
        $insertStmt = $db->prepare("INSERT INTO products (name, product_code, description, price, pv_value, status) VALUES (?, ?, ?, ?, ?, ?)");
        $result = $insertStmt->execute([
            $testProductData['name'],
            $testProductData['product_code'],
            $testProductData['description'],
            $testProductData['price'],
            $testProductData['pv_value'],
            $testProductData['status']
        ]);
        
        if ($result) {
            $testProductId = $db->lastInsertId();
            echo "<p style='color: green;'>✓ Created test product with ID: {$testProductId}</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Failed to create test product</p>\n";
        }
    }
    
    // Test 2: Check product listing pages
    echo "<h3>Test 2: Product Listing Pages</h3>\n";
    
    $listingPages = [
        'index.php' => 'Homepage',
        'products.php' => 'Main Products Page',
        'user/products.php' => 'User Products Page',
        'franchise/products.php' => 'Franchise Products Page'
    ];
    
    foreach ($listingPages as $page => $title) {
        if (file_exists($page)) {
            echo "<p><strong>{$title} ({$page}):</strong></p>\n";
            
            // Check if the file contains the improvements
            $content = file_get_contents($page);
            
            // Check for clickable cards
            if (strpos($content, 'onclick="viewProduct') !== false || strpos($content, 'onclick="viewProductDetails') !== false) {
                echo "<p style='color: green;'>✓ Contains clickable product cards</p>\n";
            } else {
                echo "<p style='color: red;'>✗ Missing clickable product cards</p>\n";
            }
            
            // Check for hover CSS
            if (strpos($content, 'product-card:hover') !== false) {
                echo "<p style='color: green;'>✓ Contains hover effects CSS</p>\n";
            } else {
                echo "<p style='color: red;'>✗ Missing hover effects CSS</p>\n";
            }
            
            // Check for cursor pointer
            if (strpos($content, 'cursor: pointer') !== false) {
                echo "<p style='color: green;'>✓ Contains cursor pointer styling</p>\n";
            } else {
                echo "<p style='color: red;'>✗ Missing cursor pointer styling</p>\n";
            }
            
        } else {
            echo "<p style='color: orange;'>⚠ {$title} ({$page}) not found</p>\n";
        }
        echo "<br>\n";
    }
    
    // Test 3: Check product detail page
    echo "<h3>Test 3: Product Detail Page</h3>\n";
    
    if (file_exists('product-detail.php')) {
        $detailContent = file_get_contents('product-detail.php');
        
        echo "<p><strong>Product Detail Page (product-detail.php):</strong></p>\n";
        
        // Check for comprehensive description display
        if (strpos($detailContent, 'nl2br(htmlspecialchars($product[\'description\']))') !== false) {
            echo "<p style='color: green;'>✓ Displays full product description</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Missing full product description display</p>\n";
        }
        
        // Check for enhanced product information
        if (strpos($detailContent, 'Product Information') !== false) {
            echo "<p style='color: green;'>✓ Contains enhanced product information section</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Missing enhanced product information section</p>\n";
        }
        
        // Check for benefits section
        if (strpos($detailContent, 'Benefits') !== false) {
            echo "<p style='color: green;'>✓ Contains benefits section</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Missing benefits section</p>\n";
        }
        
        // Check for clickable related products
        if (strpos($detailContent, 'onclick="window.location.href') !== false) {
            echo "<p style='color: green;'>✓ Related products are clickable</p>\n";
        } else {
            echo "<p style='color: red;'>✗ Related products are not clickable</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Product detail page (product-detail.php) not found</p>\n";
    }
    
    // Test 4: URL structure test
    echo "<h3>Test 4: URL Structure</h3>\n";
    
    if (!empty($sampleProducts)) {
        $testProductId = $sampleProducts[0]['id'];
        $testProductName = $sampleProducts[0]['name'];
        
        echo "<p><strong>Test Product:</strong> {$testProductName} (ID: {$testProductId})</p>\n";
        echo "<p><strong>Product Detail URL:</strong> <a href='product-detail.php?id={$testProductId}' target='_blank'>product-detail.php?id={$testProductId}</a></p>\n";
        echo "<p style='color: green;'>✓ URL structure is correct for product details</p>\n";
    }
    
    // Summary
    echo "<h3>Test Summary</h3>\n";
    echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<strong>✓ Product visual improvements test completed!</strong><br>\n";
    echo "• Product cards are now fully clickable<br>\n";
    echo "• Product descriptions removed from listing pages<br>\n";
    echo "• Full product details displayed on detail page<br>\n";
    echo "• Enhanced hover effects and visual feedback<br>\n";
    echo "• Related products are also clickable<br>\n";
    echo "• Comprehensive product information layout\n";
    echo "</div>\n";
    
    echo "<h3>How to Test</h3>\n";
    echo "<div style='background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<strong>ℹ To test the improvements:</strong><br>\n";
    echo "1. Visit <a href='products.php'>products.php</a> - Click anywhere on a product card<br>\n";
    echo "2. Visit <a href='user/products.php'>user/products.php</a> - Test clickable cards<br>\n";
    echo "3. Click on any product to see the full detail page<br>\n";
    echo "4. Notice the enhanced hover effects and visual feedback<br>\n";
    echo "5. Check that descriptions only appear on detail pages\n";
    echo "</div>\n";
    
    // Cleanup option
    if (isset($testProductId)) {
        echo "<h3>Cleanup</h3>\n";
        echo "<p><a href='?cleanup=1' style='color: red;'>Click here to delete test product</a></p>\n";
        
        if (isset($_GET['cleanup']) && $_GET['cleanup'] == '1') {
            $deleteStmt = $db->prepare("DELETE FROM products WHERE id = ?");
            $deleteResult = $deleteStmt->execute([$testProductId]);
            
            if ($deleteResult) {
                echo "<p style='color: green;'>✓ Test product deleted successfully</p>\n";
            } else {
                echo "<p style='color: red;'>✗ Failed to delete test product</p>\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "\n";
    echo "</div>\n";
    error_log("Product improvements test error: " . $e->getMessage());
}

echo "<hr>\n";
echo "<p><em>Product improvements test completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
