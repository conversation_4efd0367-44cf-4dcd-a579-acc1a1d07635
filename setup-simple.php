<?php
/**
 * Simple Production Setup Script
 * ShaktiPure MLM System
 * 
 * This is a simplified version that avoids class dependencies
 * and focuses on core database setup with all migrations included.
 */

set_time_limit(300);

echo "<!DOCTYPE html>\n";
echo "<html><head><title>ShaktiPure MLM - Simple Setup</title>";
echo "<style>body{font-family:Arial,sans-serif;max-width:1000px;margin:0 auto;padding:20px;background:#f8f9fa;}";
echo ".container{background:white;padding:30px;border-radius:10px;box-shadow:0 0 20px rgba(0,0,0,0.1);}";
echo ".success{color:#28a745;} .error{color:#dc3545;} .warning{color:#ffc107;} .info{color:#17a2b8;}";
echo "</style></head><body><div class='container'>\n";
echo "<h1 style='color: #007bff; text-align: center;'>🚀 ShaktiPure MLM Simple Setup</h1>\n";

require_once 'config/database.php';

try {
    echo "<h2>📋 Database Connection</h2>\n";
    
    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);

    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE " . DB_NAME);
    
    echo "<p class='success'>✅ Database created/selected successfully</p>\n";
    
    echo "<h2>🗄️ Creating Tables</h2>\n";
    
    // Core tables with all enhancements
    $tables = [
        "admin" => "CREATE TABLE IF NOT EXISTS admin (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB",
        
        "franchise" => "CREATE TABLE IF NOT EXISTS franchise (
            id INT PRIMARY KEY AUTO_INCREMENT,
            franchise_code VARCHAR(20) UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            address TEXT,
            commission_rate DECIMAL(5,2) DEFAULT 5.00,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin(id)
        ) ENGINE=InnoDB",
        
        "users" => "CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            address TEXT,
            sponsor_id VARCHAR(20),
            franchise_id INT,
            placement_side ENUM('left', 'right'),
            self_pv DECIMAL(10,2) DEFAULT 0.00,
            upline_pv DECIMAL(10,2) DEFAULT 0.00,
            user_level ENUM('Beginner', 'Intermediate', 'Expert') DEFAULT 'Beginner',
            current_award VARCHAR(100) NULL,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (franchise_id) REFERENCES franchise(id),
            INDEX idx_sponsor (sponsor_id),
            INDEX idx_user_id (user_id),
            INDEX idx_user_level (user_level)
        ) ENGINE=InnoDB",
        
        "binary_tree" => "CREATE TABLE IF NOT EXISTS binary_tree (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            parent_id VARCHAR(20),
            left_child VARCHAR(20),
            right_child VARCHAR(20),
            level INT DEFAULT 0,
            position ENUM('left', 'right', 'root'),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user (user_id),
            INDEX idx_parent (parent_id),
            INDEX idx_level (level)
        ) ENGINE=InnoDB",
        
        "products" => "CREATE TABLE IF NOT EXISTS products (
            id INT PRIMARY KEY AUTO_INCREMENT,
            product_code VARCHAR(20) UNIQUE NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            image VARCHAR(255) NULL,
            price DECIMAL(10,2) NOT NULL,
            pv_value DECIMAL(10,2) NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin(id)
        ) ENGINE=InnoDB",
        
        "pv_transactions" => "CREATE TABLE IF NOT EXISTS pv_transactions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            transaction_type ENUM('purchase', 'bonus', 'manual', 'downline_bonus', 'self') NOT NULL,
            pv_amount DECIMAL(10,2) NOT NULL,
            side ENUM('left', 'right', 'self', 'upline') NOT NULL,
            product_id INT,
            reference_id VARCHAR(50),
            description TEXT,
            source_user_id VARCHAR(20) NULL,
            processing_status ENUM('pending', 'processed', 'cancelled') DEFAULT 'pending',
            created_by_type ENUM('admin', 'franchise', 'system') DEFAULT 'system',
            created_by_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (source_user_id) REFERENCES users(user_id) ON DELETE SET NULL,
            FOREIGN KEY (product_id) REFERENCES products(id),
            INDEX idx_user_side (user_id, side),
            INDEX idx_user_status (user_id, processing_status),
            INDEX idx_transaction_type (transaction_type)
        ) ENGINE=InnoDB",
        
        "wallet" => "CREATE TABLE IF NOT EXISTS wallet (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            balance DECIMAL(12,2) DEFAULT 0.00,
            total_earned DECIMAL(12,2) DEFAULT 0.00,
            total_withdrawn DECIMAL(12,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_wallet (user_id)
        ) ENGINE=InnoDB",
        
        "config" => "CREATE TABLE IF NOT EXISTS config (
            id INT PRIMARY KEY AUTO_INCREMENT,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value TEXT,
            description TEXT,
            updated_by INT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (updated_by) REFERENCES admin(id)
        ) ENGINE=InnoDB"
    ];
    
    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            echo "<p class='success'>✅ Created table: {$tableName}</p>\n";
        } catch (PDOException $e) {
            echo "<p class='error'>❌ Failed to create table {$tableName}: " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<h2>⚙️ Default Configuration</h2>\n";
    
    // Insert default config
    $defaultConfigs = [
        ['pv_rate', '0.10', 'PV to INR conversion rate (1 PV = ₹0.10)'],
        ['daily_capping', '130000.00', 'Maximum daily income per user in INR'],
        ['min_withdrawal', '500.00', 'Minimum withdrawal amount in INR'],
        ['razorpay_mode', 'test', 'Razorpay mode: test or live'],
        ['company_name', 'ShaktiPure MLM', 'Company name'],
        ['support_email', '<EMAIL>', 'Support email address'],
        ['support_phone', '+91-**********', 'Support phone number']
    ];

    $configStmt = $pdo->prepare("INSERT IGNORE INTO config (config_key, config_value, description) VALUES (?, ?, ?)");
    foreach ($defaultConfigs as $config) {
        $configStmt->execute($config);
    }
    echo "<p class='success'>✅ Default configuration inserted</p>\n";
    
    echo "<h2>👥 Default Accounts</h2>\n";
    
    // Insert default admin
    $adminPassword = password_hash('admin123', PASSWORD_BCRYPT);
    $adminStmt = $pdo->prepare("INSERT IGNORE INTO admin (username, email, password, full_name, phone) VALUES (?, ?, ?, ?, ?)");
    $adminStmt->execute(['admin', '<EMAIL>', $adminPassword, 'System Administrator', '+91-**********']);
    echo "<p class='success'>✅ Default admin account created</p>\n";
    
    // Get admin ID
    $adminIdStmt = $pdo->prepare("SELECT id FROM admin WHERE username = 'admin' LIMIT 1");
    $adminIdStmt->execute();
    $adminData = $adminIdStmt->fetch();
    $adminId = $adminData ? $adminData['id'] : 1;
    
    // Insert default franchise
    $franchisePassword = password_hash('franchise123', PASSWORD_BCRYPT);
    $franchiseStmt = $pdo->prepare("INSERT IGNORE INTO franchise (franchise_code, username, email, password, full_name, phone, address, commission_rate, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $franchiseStmt->execute(['FR0001', 'franchise', '<EMAIL>', $franchisePassword, 'Master Franchise', '+91-**********', 'Franchise Address', 5.00, 'active', $adminId]);
    echo "<p class='success'>✅ Default franchise account created</p>\n";
    
    // Insert master user
    $masterUserId = 'SP000001';
    $masterPassword = 'master123';
    $userStmt = $pdo->prepare("INSERT IGNORE INTO users (user_id, username, email, password, full_name, phone, address, status, user_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $userStmt->execute([$masterUserId, 'master', '<EMAIL>', $masterPassword, 'Master User', '+91-**********', 'Master Address', 'active', 'Expert']);
    
    // Create wallet for master user
    $walletStmt = $pdo->prepare("INSERT IGNORE INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, ?, ?, ?)");
    $walletStmt->execute([$masterUserId, 0.00, 0.00, 0.00]);
    
    // Add to binary tree
    $binaryTreeStmt = $pdo->prepare("INSERT IGNORE INTO binary_tree (user_id, parent_id, level, position) VALUES (?, ?, ?, ?)");
    $binaryTreeStmt->execute([$masterUserId, null, 0, 'root']);
    
    echo "<p class='success'>✅ Master user account created</p>\n";
    
    echo "<h2>🎉 Setup Complete!</h2>\n";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
    echo "<h3 style='color: #155724;'>Default Login Credentials:</h3>\n";
    echo "<p><strong>Admin Panel:</strong> <a href='admin/login.php'>admin/login.php</a> - admin:admin123</p>\n";
    echo "<p><strong>Franchise Panel:</strong> <a href='franchise/login.php'>franchise/login.php</a> - franchise:franchise123</p>\n";
    echo "<p><strong>User Panel:</strong> <a href='user/login.php'>user/login.php</a> - master:master123</p>\n";
    echo "<p style='color: #856404;'><strong>⚠️ Change all passwords after first login!</strong></p>\n";
    echo "</div>\n";

} catch (PDOException $e) {
    echo "<p class='error'>❌ Setup failed: " . $e->getMessage() . "</p>\n";
}

echo "</div></body></html>\n";
?>
