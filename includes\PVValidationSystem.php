<?php
/**
 * PV Validation System
 * Production-Level Validation and Error Handling for MLM PV System
 * 
 * Features:
 * - Comprehensive input validation
 * - Data integrity checks
 * - Transaction management
 * - Rollback mechanisms
 * - Error recovery procedures
 * - Audit trail validation
 */

require_once __DIR__ . '/../config/Connection.php';
require_once __DIR__ . '/../config/config.php';

class PVValidationSystem {
    private $db;
    private $config;
    private $logger;

    // Validation constants
    const MAX_PV_AMOUNT = 999999.99;
    const MIN_PV_AMOUNT = 0.01;
    const MAX_DESCRIPTION_LENGTH = 500;
    const MAX_REFERENCE_ID_LENGTH = 50;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
        $this->initializeLogger();
    }
    
    /**
     * Initialize logging system
     */
    private function initializeLogger() {
        $this->logger = [
            'info' => function($message, $context = []) {
                $this->logMessage('info', 'validation', $message, $context);
            },
            'error' => function($message, $context = []) {
                $this->logMessage('error', 'validation', $message, $context);
            },
            'warning' => function($message, $context = []) {
                $this->logMessage('warning', 'validation', $message, $context);
            }
        ];
    }
    
    /**
     * Validate PV transaction data
     */
    public function validatePVTransaction($data) {
        $errors = [];
        
        // Validate user ID
        if (empty($data['user_id'])) {
            $errors[] = "User ID is required";
        } elseif (!$this->isValidUserId($data['user_id'])) {
            $errors[] = "Invalid user ID format";
        } elseif (!$this->userExists($data['user_id'])) {
            $errors[] = "User does not exist or is inactive";
        }
        
        // Validate PV amount
        if (!isset($data['pv_amount'])) {
            $errors[] = "PV amount is required";
        } elseif (!is_numeric($data['pv_amount'])) {
            $errors[] = "PV amount must be numeric";
        } elseif ($data['pv_amount'] < self::MIN_PV_AMOUNT) {
            $errors[] = "PV amount must be at least " . self::MIN_PV_AMOUNT;
        } elseif ($data['pv_amount'] > self::MAX_PV_AMOUNT) {
            $errors[] = "PV amount cannot exceed " . self::MAX_PV_AMOUNT;
        }
        
        // Validate side
        if (empty($data['side'])) {
            $errors[] = "Side is required";
        } elseif (!in_array($data['side'], ['left', 'right', 'self', 'upline'])) {
            $errors[] = "Invalid side value";
        }
        
        // Validate transaction type
        if (empty($data['transaction_type'])) {
            $errors[] = "Transaction type is required";
        } elseif (!in_array($data['transaction_type'], ['purchase', 'bonus', 'manual', 'downline_bonus', 'self'])) {
            $errors[] = "Invalid transaction type";
        }
        
        // Validate optional fields
        if (!empty($data['description']) && strlen($data['description']) > self::MAX_DESCRIPTION_LENGTH) {
            $errors[] = "Description cannot exceed " . self::MAX_DESCRIPTION_LENGTH . " characters";
        }
        
        if (!empty($data['reference_id']) && strlen($data['reference_id']) > self::MAX_REFERENCE_ID_LENGTH) {
            $errors[] = "Reference ID cannot exceed " . self::MAX_REFERENCE_ID_LENGTH . " characters";
        }
        
        // Validate product ID if provided
        if (!empty($data['product_id']) && !$this->productExists($data['product_id'])) {
            $errors[] = "Product does not exist";
        }
        
        // Validate source user ID if provided
        if (!empty($data['source_user_id']) && !$this->userExists($data['source_user_id'])) {
            $errors[] = "Source user does not exist";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Validate income processing data
     */
    public function validateIncomeProcessing($userId, $processingPeriod, $processingType) {
        $errors = [];
        
        // Validate user ID
        if (!$this->userExists($userId)) {
            $errors[] = "User does not exist or is inactive";
        }
        
        // Validate processing period
        if (empty($processingPeriod)) {
            $errors[] = "Processing period is required";
        } elseif (!$this->isValidDate($processingPeriod)) {
            $errors[] = "Invalid processing period format";
        }
        
        // Validate processing type
        if (!in_array($processingType, ['daily', 'weekly', 'monthly'])) {
            $errors[] = "Invalid processing type";
        }
        
        // Check for duplicate processing
        if ($this->isDuplicateProcessing($userId, $processingPeriod, $processingType)) {
            $errors[] = "Income already processed for this period";
        }
        
        // Validate system state
        if (!$this->isSystemHealthy()) {
            $errors[] = "System is not in a healthy state for processing";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Validate data integrity
     */
    public function validateDataIntegrity($userId = null) {
        $issues = [];
        
        // Check PV transaction consistency
        $pvIssues = $this->checkPVTransactionConsistency($userId);
        if (!empty($pvIssues)) {
            $issues['pv_transactions'] = $pvIssues;
        }
        
        // Check PV usage tracking consistency
        $usageIssues = $this->checkPVUsageConsistency($userId);
        if (!empty($usageIssues)) {
            $issues['pv_usage_tracking'] = $usageIssues;
        }
        
        // Check income log consistency
        $incomeIssues = $this->checkIncomeLogConsistency($userId);
        if (!empty($incomeIssues)) {
            $issues['income_logs'] = $incomeIssues;
        }
        
        // Check wallet consistency
        $walletIssues = $this->checkWalletConsistency($userId);
        if (!empty($walletIssues)) {
            $issues['wallet'] = $walletIssues;
        }
        
        // Check binary tree consistency
        $treeIssues = $this->checkBinaryTreeConsistency($userId);
        if (!empty($treeIssues)) {
            $issues['binary_tree'] = $treeIssues;
        }
        
        return [
            'healthy' => empty($issues),
            'issues' => $issues
        ];
    }
    
    /**
     * Execute transaction with comprehensive error handling
     */
    public function executeWithTransaction($callback, $context = []) {
        $maxRetries = 3;
        $retryCount = 0;
        
        while ($retryCount < $maxRetries) {
            try {
                $this->db->beginTransaction();
                
                // Execute the callback
                $result = $callback();
                
                // Validate the result if it's an array with validation requirements
                if (is_array($result) && isset($result['validate']) && $result['validate']) {
                    $validation = $this->validateTransactionResult($result, $context);
                    if (!$validation['valid']) {
                        throw new Exception("Transaction validation failed: " . implode(', ', $validation['errors']));
                    }
                }
                
                $this->db->commit();
                
                $this->logger['info']("Transaction executed successfully", array_merge($context, [
                    'retry_count' => $retryCount
                ]));
                
                return $result;
                
            } catch (PDOException $e) {
                if ($this->db->inTransaction()) {
                    $this->db->rollback();
                }
                
                $retryCount++;
                
                // Check if it's a retryable error
                if ($this->isRetryableError($e) && $retryCount < $maxRetries) {
                    $this->logger['warning']("Retryable database error, attempting retry", [
                        'error' => $e->getMessage(),
                        'retry_count' => $retryCount,
                        'context' => $context
                    ]);
                    
                    // Wait before retry (exponential backoff)
                    usleep(pow(2, $retryCount) * 100000); // 200ms, 400ms, 800ms
                    continue;
                }
                
                $this->logger['error']("Database transaction failed", [
                    'error' => $e->getMessage(),
                    'retry_count' => $retryCount,
                    'context' => $context
                ]);
                
                throw new Exception("Database transaction failed after {$retryCount} retries: " . $e->getMessage());
                
            } catch (Exception $e) {
                if ($this->db->inTransaction()) {
                    $this->db->rollback();
                }
                
                $this->logger['error']("Transaction execution failed", [
                    'error' => $e->getMessage(),
                    'context' => $context
                ]);
                
                throw $e;
            }
        }
    }
    
    /**
     * Validate user ID format
     */
    private function isValidUserId($userId) {
        return preg_match('/^[A-Z]{3}\d{10,15}$/', $userId);
    }
    
    /**
     * Check if user exists and is active
     */
    private function userExists($userId) {
        $stmt = $this->db->prepare("SELECT user_id FROM users WHERE user_id = ? AND status = 'active'");
        $stmt->execute([$userId]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Check if product exists
     */
    private function productExists($productId) {
        $stmt = $this->db->prepare("SELECT id FROM products WHERE id = ? AND status = 'active'");
        $stmt->execute([$productId]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Validate date format
     */
    private function isValidDate($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
    
    /**
     * Check for duplicate processing
     */
    private function isDuplicateProcessing($userId, $processingPeriod, $processingType) {
        if ($processingType === 'weekly') {
            $stmt = $this->db->prepare("SELECT id FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
        } else {
            $stmt = $this->db->prepare("SELECT id FROM income_logs WHERE user_id = ? AND matching_date = ?");
        }

        $stmt->execute([$userId, $processingPeriod]);
        return $stmt->fetch() !== false;
    }

    /**
     * Check system health
     */
    private function isSystemHealthy() {
        try {
            // Check database connectivity
            $this->db->query("SELECT 1");

            // Check for critical locks
            $stmt = $this->db->prepare("SELECT COUNT(*) as lock_count FROM pv_processing_locks WHERE status = 'active' AND expires_at < NOW()");
            $stmt->execute();
            $result = $stmt->fetch();

            if ($result['lock_count'] > 10) {
                return false; // Too many expired locks
            }

            // Check for system errors in last hour
            $stmt = $this->db->prepare("SELECT COUNT(*) as error_count FROM system_logs WHERE log_type = 'error' AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
            $stmt->execute();
            $result = $stmt->fetch();

            if ($result['error_count'] > 100) {
                return false; // Too many recent errors
            }

            return true;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Check PV transaction consistency
     */
    private function checkPVTransactionConsistency($userId = null) {
        $issues = [];

        // Check for transactions without tracking records
        $whereClause = $userId ? "AND pt.user_id = ?" : "";
        $params = $userId ? [$userId] : [];

        $stmt = $this->db->prepare("
            SELECT COUNT(*) as untracked_count
            FROM pv_transactions pt
            LEFT JOIN pv_usage_tracking put ON pt.id = put.pv_transaction_id
            WHERE put.id IS NULL AND pt.processing_status = 'processed' {$whereClause}
        ");

        $stmt->execute($params);
        $result = $stmt->fetch();

        if ($result['untracked_count'] > 0) {
            $issues[] = "Found {$result['untracked_count']} processed PV transactions without tracking records";
        }

        // Check for negative PV amounts
        $whereClause2 = $userId ? "AND user_id = ?" : "";
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as negative_count
            FROM pv_transactions
            WHERE pv_amount < 0 {$whereClause2}
        ");

        $stmt->execute($params);
        $result = $stmt->fetch();

        if ($result['negative_count'] > 0) {
            $issues[] = "Found {$result['negative_count']} transactions with negative PV amounts";
        }

        return $issues;
    }

    /**
     * Check PV usage tracking consistency
     */
    private function checkPVUsageConsistency($userId = null) {
        $issues = [];

        $whereClause = $userId ? "AND user_id = ?" : "";
        $params = $userId ? [$userId] : [];

        // Check for inconsistent usage amounts
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as inconsistent_count
            FROM pv_usage_tracking
            WHERE used_amount + remaining_amount != original_amount {$whereClause}
        ");

        $stmt->execute($params);
        $result = $stmt->fetch();

        if ($result['inconsistent_count'] > 0) {
            $issues[] = "Found {$result['inconsistent_count']} PV usage records with inconsistent amounts";
        }

        // Check for negative remaining amounts
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as negative_count
            FROM pv_usage_tracking
            WHERE remaining_amount < 0 {$whereClause}
        ");

        $stmt->execute($params);
        $result = $stmt->fetch();

        if ($result['negative_count'] > 0) {
            $issues[] = "Found {$result['negative_count']} PV usage records with negative remaining amounts";
        }

        return $issues;
    }

    /**
     * Check income log consistency
     */
    private function checkIncomeLogConsistency($userId = null) {
        $issues = [];

        $whereClause = $userId ? "AND user_id = ?" : "";
        $params = $userId ? [$userId] : [];

        // Check for negative income amounts
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as negative_count
            FROM income_logs
            WHERE income_amount < 0 {$whereClause}
        ");

        $stmt->execute($params);
        $result = $stmt->fetch();

        if ($result['negative_count'] > 0) {
            $issues[] = "Found {$result['negative_count']} income logs with negative amounts";
        }

        // Check for impossible matching PV (matched > min(left, right))
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as impossible_count
            FROM income_logs
            WHERE matched_pv > LEAST(left_pv, right_pv) {$whereClause}
        ");

        $stmt->execute($params);
        $result = $stmt->fetch();

        if ($result['impossible_count'] > 0) {
            $issues[] = "Found {$result['impossible_count']} income logs with impossible matching PV";
        }

        return $issues;
    }

    /**
     * Check wallet consistency
     */
    private function checkWalletConsistency($userId = null) {
        $issues = [];

        $whereClause = $userId ? "WHERE w.user_id = ?" : "";
        $params = $userId ? [$userId] : [];

        // Check wallet balance consistency with transactions
        $stmt = $this->db->prepare("
            SELECT w.user_id, w.balance,
                   COALESCE(SUM(CASE WHEN wt.transaction_type = 'credit' THEN wt.amount ELSE -wt.amount END), 0) as calculated_balance
            FROM wallet w
            LEFT JOIN wallet_transactions wt ON w.user_id = wt.user_id
            {$whereClause}
            GROUP BY w.user_id, w.balance
            HAVING ABS(w.balance - calculated_balance) > 0.01
        ");

        $stmt->execute($params);
        $inconsistentWallets = $stmt->fetchAll();

        if (!empty($inconsistentWallets)) {
            $issues[] = "Found " . count($inconsistentWallets) . " wallets with inconsistent balances";
        }

        return $issues;
    }

    /**
     * Check binary tree consistency
     */
    private function checkBinaryTreeConsistency($userId = null) {
        $issues = [];

        // Check for orphaned nodes
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as orphaned_count
            FROM binary_tree bt
            LEFT JOIN users u ON bt.user_id = u.user_id
            WHERE u.user_id IS NULL
        ");

        $stmt->execute();
        $result = $stmt->fetch();

        if ($result['orphaned_count'] > 0) {
            $issues[] = "Found {$result['orphaned_count']} orphaned binary tree nodes";
        }

        // Check for circular references
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as circular_count
            FROM binary_tree bt1
            JOIN binary_tree bt2 ON bt1.parent_id = bt2.user_id
            WHERE bt2.parent_id = bt1.user_id
        ");

        $stmt->execute();
        $result = $stmt->fetch();

        if ($result['circular_count'] > 0) {
            $issues[] = "Found {$result['circular_count']} circular references in binary tree";
        }

        return $issues;
    }

    /**
     * Validate transaction result
     */
    private function validateTransactionResult($result, $context) {
        $errors = [];

        // Add specific validation logic based on context
        if (isset($context['operation']) && $context['operation'] === 'income_processing') {
            if (!isset($result['net_income']) || !is_numeric($result['net_income'])) {
                $errors[] = "Invalid net income in result";
            }

            if (!isset($result['matched_pv']) || !is_numeric($result['matched_pv'])) {
                $errors[] = "Invalid matched PV in result";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Check if database error is retryable
     */
    private function isRetryableError($e) {
        $retryableErrors = [
            'Lock wait timeout exceeded',
            'Deadlock found when trying to get lock',
            'Connection lost',
            'Server has gone away'
        ];

        foreach ($retryableErrors as $error) {
            if (strpos($e->getMessage(), $error) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log system messages
     */
    private function logMessage($level, $category, $message, $context = []) {
        try {
            // Try new schema first
            $stmt = $this->db->prepare("
                INSERT INTO system_logs (log_type, category, message, context, user_id, processing_period)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $level,
                $category,
                $message,
                json_encode($context),
                $context['user_id'] ?? null,
                $context['processing_period'] ?? null
            ]);
        } catch (Exception $e) {
            // Fall back to old schema
            try {
                $stmt = $this->db->prepare("
                    INSERT INTO system_logs (log_type, message)
                    VALUES (?, ?)
                ");

                $fullMessage = "[{$category}] {$message}";
                if (!empty($context)) {
                    $fullMessage .= " - Context: " . json_encode($context);
                }

                $stmt->execute([$level, $fullMessage]);
            } catch (Exception $e2) {
                // If logging fails completely, just continue
                error_log("Logging failed: " . $e2->getMessage());
            }
        }
    }
}
