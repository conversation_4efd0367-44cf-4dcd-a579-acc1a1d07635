[2025-08-09 21:37:14] Starting Enhanced PV System Deployment
[2025-08-09 21:37:14] ==================================================
[2025-08-09 21:37:14] Step 1: Pre-deployment validation
[2025-08-09 21:37:14] ✓ Database connectivity verified
[2025-08-09 21:37:14] ✓ Required PHP extensions verified
[2025-08-09 21:37:14] ✓ File permissions verified
[2025-08-09 21:37:14] ✓ Base system tables verified
[2025-08-09 21:37:14] Step 2: Creating database backup
[2025-08-09 21:37:15] ✓ Database backup created: pre_deployment_backup_2025-08-09_21-37-14.sql
[2025-08-09 21:37:15] Step 3: Updating database schema
[2025-08-09 21:37:15] ✓ Database schema updated
[2025-08-09 21:37:15] Schema output: ✅ Database created/selected successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Setup completed successfully.
🔐 Default Admin:
Username: admin
Password: admin123 (please change after login)

[2025-08-09 21:37:15] Step 4: Migrating existing data
[2025-08-09 21:37:15] ✓ Migrated 0 PV transactions to usage tracking
[2025-08-09 21:37:15] ❌ Deployment failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'processing_status' in 'where clause'
[2025-08-09 21:37:15] Rolling back changes...
[2025-08-09 21:37:15] Attempting to rollback changes...
[2025-08-09 21:37:15] ⚠ Backup file exists at: C:\xampp\htdocs\shaktipure/backups/pre_deployment_backup_2025-08-09_21-37-14.sql
[2025-08-09 21:37:15] ⚠ Manual database restore may be required
[2025-08-09 21:37:15] ⚠ You can restore using: mysql -u root database_name < backup_file.sql
