<?php
/**
 * Test Binary PV System
 * Comprehensive testing of the new binary matching system
 */

require_once '../config/Connection.php';
require_once '../config/config.php';
require_once '../includes/NewBinaryPVSystem.php';
require_once '../includes/BinaryTree.php';
require_once '../includes/Wallet.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_id'])) {
    die("Access denied. Admin login required.");
}

$db = Database::getInstance();
$binaryPVSystem = new NewBinaryPVSystem();
$binaryTree = new BinaryTree();
$wallet = new Wallet();

echo "<h2>Binary PV System Test</h2>";

try {
    // Test 1: Check database tables
    echo "<h3>1. Database Tables Test</h3>";
    $requiredTables = ['binary_reports', 'binary_income_logs', 'pv_transactions', 'users', 'wallet'];
    foreach ($requiredTables as $table) {
        try {
            $stmt = $db->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->fetch()) {
                echo "<p style='color: green;'>✓ Table '{$table}' exists</p>";
            } else {
                echo "<p style='color: red;'>✗ Table '{$table}' missing</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error checking table '{$table}': " . $e->getMessage() . "</p>";
        }
    }

    // Test 2: Check for test users
    echo "<h3>2. Test Users Check</h3>";
    $testUsers = $db->query("SELECT user_id, full_name, sponsor_id FROM users WHERE status = 'active' LIMIT 5")->fetchAll();
    if (empty($testUsers)) {
        echo "<p style='color: orange;'>⚠ No active users found for testing</p>";
    } else {
        echo "<p style='color: green;'>✓ Found " . count($testUsers) . " active users for testing</p>";
        foreach ($testUsers as $user) {
            echo "<p>- {$user['user_id']}: {$user['full_name']} (Sponsor: {$user['sponsor_id']})</p>";
        }
    }

    // Test 3: Check pending PVs
    echo "<h3>3. Pending PV Transactions</h3>";
    $pendingPVs = $db->query("SELECT COUNT(*) as count, SUM(pv_amount) as total FROM pv_transactions WHERE processing_status = 'pending'")->fetch();
    echo "<p>Pending PV Transactions: {$pendingPVs['count']} (Total PV: " . number_format($pendingPVs['total'], 2) . ")</p>";

    if ($pendingPVs['count'] == 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7;'>";
        echo "<h4>⚠ No Pending PVs Found</h4>";
        echo "<p>To test the binary matching system, you need some pending PV transactions. You can:</p>";
        echo "<ul>";
        echo "<li>Add PVs manually through the admin panel</li>";
        echo "<li>Create test PV transactions using the PV management system</li>";
        echo "<li>Import existing PV data</li>";
        echo "</ul>";
        echo "</div>";
    }

    // Test 4: Test binary tree structure for a sample user
    if (!empty($testUsers)) {
        echo "<h3>4. Binary Tree Structure Test</h3>";
        $testUser = $testUsers[0];
        $userId = $testUser['user_id'];
        
        echo "<p>Testing binary tree for user: {$userId} ({$testUser['full_name']})</p>";
        
        try {
            $leftLeg = $binaryTree->getLeftLeg($userId);
            $rightLeg = $binaryTree->getRightLeg($userId);
            
            echo "<p>Left Leg: " . count($leftLeg) . " users</p>";
            echo "<p>Right Leg: " . count($rightLeg) . " users</p>";
            
            // Test PV calculation
            $pvTotals = $binaryPVSystem->getUserTotalPV($userId);
            echo "<p>Left PV: " . number_format($pvTotals['left_pv'], 2) . "</p>";
            echo "<p>Right PV: " . number_format($pvTotals['right_pv'], 2) . "</p>";
            echo "<p>Potential Matched PV: " . number_format(min($pvTotals['left_pv'], $pvTotals['right_pv']), 2) . "</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Binary tree test failed: " . $e->getMessage() . "</p>";
        }
    }

    // Test 5: Wallet system test
    if (!empty($testUsers)) {
        echo "<h3>5. Wallet System Test</h3>";
        $testUser = $testUsers[0];
        $userId = $testUser['user_id'];
        
        try {
            $userWallet = $wallet->getWallet($userId);
            echo "<p>User {$userId} wallet balance: ₹" . number_format($userWallet['balance'], 2) . "</p>";
            echo "<p style='color: green;'>✓ Wallet system working correctly</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Wallet test failed: " . $e->getMessage() . "</p>";
        }
    }

    // Test 6: Configuration test
    echo "<h3>6. Configuration Test</h3>";
    try {
        $config = Config::getInstance();
        $pvRate = $config->getPVRate();
        echo "<p>PV Rate: ₹" . number_format($pvRate, 2) . " per PV</p>";
        echo "<p style='color: green;'>✓ Configuration system working correctly</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Configuration test failed: " . $e->getMessage() . "</p>";
    }

    // Test 7: Dry run report generation (if there are pending PVs)
    if ($pendingPVs['count'] > 0 && !empty($testUsers)) {
        echo "<h3>7. Dry Run Report Generation Test</h3>";
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border: 1px solid #b3d9ff;'>";
        echo "<h4>ℹ Simulation Only</h4>";
        echo "<p>This is a simulation of what would happen if you generate a report:</p>";
        
        $totalUsersWithIncome = 0;
        $totalPotentialIncome = 0;
        $pvRate = $config->getPVRate();
        
        foreach ($testUsers as $user) {
            $userId = $user['user_id'];
            $pvTotals = $binaryPVSystem->getUserTotalPV($userId);
            $matchedPV = min($pvTotals['left_pv'], $pvTotals['right_pv']);
            
            if ($matchedPV > 0) {
                $potentialIncome = $matchedPV * $pvRate;
                $totalUsersWithIncome++;
                $totalPotentialIncome += $potentialIncome;
                
                echo "<p>- {$user['full_name']}: {$matchedPV} matched PV = ₹" . number_format($potentialIncome, 2) . "</p>";
            }
        }
        
        echo "<p><strong>Summary:</strong></p>";
        echo "<p>Users with income: {$totalUsersWithIncome}</p>";
        echo "<p>Total potential income: ₹" . number_format($totalPotentialIncome, 2) . "</p>";
        echo "</div>";
    }

    // Summary and next steps
    echo "<h3>8. Summary and Next Steps</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>✅ System Status</h4>";
    echo "<p style='color: #155724;'>The binary PV system appears to be set up correctly. To use it:</p>";
    echo "<ol style='color: #155724;'>";
    echo "<li>Ensure you have pending PV transactions in the system</li>";
    echo "<li>Go to <strong>Admin → Reports → Binary PV Reports</strong></li>";
    echo "<li>Click <strong>Generate Report</strong></li>";
    echo "<li>Select the report date and submit</li>";
    echo "<li>The system will process all users and credit income to their wallets</li>";
    echo "</ol>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h4 style='color: #721c24;'>❌ Test Failed</h4>";
    echo "<p style='color: #721c24;'>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Binary PV System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        p { margin: 10px 0; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <div style="margin-top: 30px;">
        <a href="binary-reports.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Go to Binary Reports</a>
        <a href="add-test-pv-data.php" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Add Test PV Data</a>
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">← Back to Dashboard</a>
    </div>
</body>
</html>
