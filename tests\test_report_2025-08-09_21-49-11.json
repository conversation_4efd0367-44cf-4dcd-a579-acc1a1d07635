{"timestamp": "2025-08-09 21:49:11", "summary": {"total": 16, "passed": 11, "failed": 0, "errors": 5, "success_rate": 68.75, "total_duration_ms": 615.71}, "tests": [{"name": "Add PV Transaction", "result": "PASS", "duration": 9.2}, {"name": "Get Available PV", "result": "PASS", "duration": 1.78}, {"name": "PV Usage Tracking", "result": "PASS", "duration": 0.94}, {"name": "Input Validation", "result": "PASS", "duration": 2.22}, {"name": "Prevent Duplicate Income Processing", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547563501266", "duration": 0}, {"name": "PV Usage Tracking Consistency", "result": "PASS", "duration": 12.5}, {"name": "FIFO PV Usage", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547563501266", "duration": 0}, {"name": "System Data Integrity", "result": "ERROR", "error": "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'WHERE user_id = ?' at line 3", "duration": 0}, {"name": "Transaction Consistency", "result": "PASS", "duration": 0.97}, {"name": "No Negative Values", "result": "PASS", "duration": 0.73}, {"name": "Bulk PV Addition Performance", "result": "PASS", "duration": 576.51}, {"name": "Income Processing Performance", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547563501266", "duration": 0}, {"name": "Zero PV Amount Handling", "result": "PASS", "duration": 1.98}, {"name": "Large PV Amount Handling", "result": "PASS", "duration": 5.77}, {"name": "Invalid User ID Handling", "result": "PASS", "duration": 3.11}, {"name": "Complete Income Generation Workflow", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547563501266", "duration": 0}]}