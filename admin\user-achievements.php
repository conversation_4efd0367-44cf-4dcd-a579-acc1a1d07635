<?php
/**
 * Admin User Achievements Management Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../models/UserAchievement.php';
require_once '../includes/Validator.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$userAchievement = new UserAchievement();
$db = Database::getInstance();

$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        verifyCsrfToken();

        $action = $_POST['action'] ?? '';

    if ($action === 'assign_level') {
        $userId = sanitizeInput($_POST['user_id']);
        $level = sanitizeInput($_POST['level']);
        $notes = sanitizeInput($_POST['notes'] ?? '');

        if ($userAchievement->assignLevel($userId, $level, $adminId, $notes)) {
            setSuccessMessage("Level assigned successfully to user {$userId}!");
            Response::redirect('user-achievements.php');
        } else {
            $error = "Failed to assign level. Please try again.";
        }
    }

    elseif ($action === 'assign_award') {
        $userId = sanitizeInput($_POST['user_id']);
        $award = sanitizeInput($_POST['award']);
        $notes = sanitizeInput($_POST['notes'] ?? '');

        if ($userAchievement->assignAward($userId, $award, $adminId, $notes)) {
            setSuccessMessage("Award '{$award}' assigned successfully to user {$userId}!");
            Response::redirect('user-achievements.php');
        } else {
            $error = "Failed to assign award. Please try again.";
        }
    }

    elseif ($action === 'remove_award') {
        $userId = sanitizeInput($_POST['user_id']);
        $notes = sanitizeInput($_POST['notes'] ?? '');

        if ($userAchievement->removeAward($userId, $adminId, $notes)) {
            setSuccessMessage("Award removed successfully from user {$userId}!");
            Response::redirect('user-achievements.php');
        } else {
            $error = "Failed to remove award. Please try again.";
        }
    }

    } catch (Exception $e) {
        $error = 'Security verification failed. Please refresh the page and try again.';
        error_log('CSRF verification failed: ' . $e->getMessage());
    }
}

// Get search parameters
$search = $_GET['search'] ?? '';
$levelFilter = $_GET['level'] ?? '';
$awardFilter = $_GET['award'] ?? '';

// Build query
$whereConditions = ["u.status = 'active'"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(u.user_id LIKE ? OR u.full_name LIKE ? OR u.email LIKE ?)";
    $searchTerm = "%{$search}%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if (!empty($levelFilter)) {
    $whereConditions[] = "u.user_level = ?";
    $params[] = $levelFilter;
}

if (!empty($awardFilter)) {
    if ($awardFilter === 'none') {
        $whereConditions[] = "u.current_award IS NULL";
    } else {
        $whereConditions[] = "u.current_award = ?";
        $params[] = $awardFilter;
    }
}

$whereClause = implode(' AND ', $whereConditions);

// Pagination
$page = (int) ($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Get total count
$countStmt = $db->prepare("SELECT COUNT(*) FROM users u WHERE {$whereClause}");
$countStmt->execute($params);
$totalUsers = $countStmt->fetchColumn();
$totalPages = ceil($totalUsers / $limit);

// Get users
$usersStmt = $db->prepare("
    SELECT u.user_id, u.full_name, u.email, u.user_level, u.current_award, u.registration_date,
           f.franchise_code
    FROM users u
    LEFT JOIN franchise f ON u.franchise_id = f.id
    WHERE {$whereClause}
    ORDER BY u.full_name
    LIMIT ? OFFSET ?
");
$params[] = $limit;
$params[] = $offset;
$usersStmt->execute($params);
$users = $usersStmt->fetchAll();

// Get achievement statistics
$stats = $userAchievement->getAchievementStats();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Achievements - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/achievements.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center gap-3">
                    <h2 class="mb-0"><i class="fas fa-trophy me-2"></i>User Achievements</h2>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bulkAssignModal">
                            <i class="fas fa-users me-1"></i>Bulk Assign
                        </button>
                        <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#statsModal">
                            <i class="fas fa-chart-bar me-1"></i>Statistics
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h5 class="mb-0"><?php echo number_format($totalUsers); ?></h5>
                            <small class="text-muted">Total Active Users</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-star"></i>
                        </div>
                        <div>
                            <h5 class="mb-0"><?php echo count($stats['awards']); ?></h5>
                            <small class="text-muted">Users with Awards</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-level-up-alt"></i>
                        </div>
                        <div>
                            <h5 class="mb-0"><?php echo array_sum(array_column($stats['levels'], 'count')); ?></h5>
                            <small class="text-muted">Leveled Users</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon info me-3">
                            <i class="fas fa-history"></i>
                        </div>
                        <div>
                            <h5 class="mb-0"><?php echo count($stats['recent']); ?></h5>
                            <small class="text-muted">Recent Changes</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search Users</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="User ID, Name, or Email">
                    </div>
                    <div class="col-md-3">
                        <label for="level" class="form-label">Filter by Level</label>
                        <select class="form-select" id="level" name="level">
                            <option value="">All Levels</option>
                            <?php foreach (UserAchievement::LEVELS as $level): ?>
                                <option value="<?php echo $level; ?>" <?php echo $levelFilter === $level ? 'selected' : ''; ?>>
                                    <?php echo $level; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="award" class="form-label">Filter by Award</label>
                        <select class="form-select" id="award" name="award">
                            <option value="">All Awards</option>
                            <option value="none" <?php echo $awardFilter === 'none' ? 'selected' : ''; ?>>No Award</option>
                            <?php foreach (UserAchievement::AWARDS as $award): ?>
                                <option value="<?php echo $award; ?>" <?php echo $awardFilter === $award ? 'selected' : ''; ?>>
                                    <?php echo $award; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">User Achievements Management</h5>
            </div>
            <div class="card-body">
                <?php if (empty($users)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No users found</h5>
                        <p class="text-muted">Try adjusting your search criteria.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Level</th>
                                    <th>Current Award</th>
                                    <th>Franchise</th>
                                    <th>Joined</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    ID: <?php echo htmlspecialchars($user['user_id']); ?> |
                                                    <?php echo htmlspecialchars($user['email']); ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="level-badge level-<?php echo strtolower($user['user_level']); ?>">
                                                <?php echo $user['user_level']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($user['current_award']): ?>
                                                <span class="award-badge">
                                                    <i class="fas fa-trophy me-1"></i>
                                                    <?php echo htmlspecialchars($user['current_award']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">No award</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['franchise_code']): ?>
                                                <small><?php echo htmlspecialchars($user['franchise_code']); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">Direct</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('M d, Y', strtotime($user['registration_date'])); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary"
                                                        onclick="assignLevel('<?php echo $user['user_id']; ?>', '<?php echo htmlspecialchars($user['full_name']); ?>', '<?php echo $user['user_level']; ?>')">
                                                    <i class="fas fa-level-up-alt"></i>
                                                </button>
                                                <button class="btn btn-outline-success"
                                                        onclick="assignAward('<?php echo $user['user_id']; ?>', '<?php echo htmlspecialchars($user['full_name']); ?>', '<?php echo htmlspecialchars($user['current_award'] ?? ''); ?>')">
                                                    <i class="fas fa-trophy"></i>
                                                </button>
                                                <?php if ($user['current_award']): ?>
                                                    <button class="btn btn-outline-danger"
                                                            onclick="removeAward('<?php echo $user['user_id']; ?>', '<?php echo htmlspecialchars($user['full_name']); ?>')">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-info"
                                                        onclick="viewHistory('<?php echo $user['user_id']; ?>', '<?php echo htmlspecialchars($user['full_name']); ?>')">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&level=<?php echo urlencode($levelFilter); ?>&award=<?php echo urlencode($awardFilter); ?>">Previous</a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&level=<?php echo urlencode($levelFilter); ?>&award=<?php echo urlencode($awardFilter); ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&level=<?php echo urlencode($levelFilter); ?>&award=<?php echo urlencode($awardFilter); ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Assign Level Modal -->
    <div class="modal fade" id="assignLevelModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="assign_level">
                    <input type="hidden" name="user_id" id="levelUserId">

                    <div class="modal-header">
                        <h5 class="modal-title">Assign Level</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">User</label>
                            <input type="text" class="form-control" id="levelUserName" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="level" class="form-label">New Level</label>
                            <select class="form-select" name="level" id="levelSelect" required>
                                <option value="">Select Level</option>
                                <?php foreach (UserAchievement::LEVELS as $level): ?>
                                    <option value="<?php echo $level; ?>"><?php echo $level; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes (Optional)</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="Reason for level assignment..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Assign Level</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Assign Award Modal -->
    <div class="modal fade" id="assignAwardModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="assign_award">
                    <input type="hidden" name="user_id" id="awardUserId">

                    <div class="modal-header">
                        <h5 class="modal-title">Assign Award</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">User</label>
                            <input type="text" class="form-control" id="awardUserName" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="award" class="form-label">Award</label>
                            <select class="form-select" name="award" id="awardSelect" required>
                                <option value="">Select Award</option>
                                <?php foreach (UserAchievement::AWARDS as $award): ?>
                                    <option value="<?php echo $award; ?>"><?php echo $award; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes (Optional)</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="Reason for award assignment..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Assign Award</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Remove Award Modal -->
    <div class="modal fade" id="removeAwardModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="remove_award">
                    <input type="hidden" name="user_id" id="removeAwardUserId">

                    <div class="modal-header">
                        <h5 class="modal-title">Remove Award</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">User</label>
                            <input type="text" class="form-control" id="removeAwardUserName" readonly>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Are you sure you want to remove this user's award? This action will be logged.
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Reason for Removal</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="Reason for removing award..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Remove Award</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Achievement History Modal -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Achievement History</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="historyContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function assignLevel(userId, userName, currentLevel) {
            document.getElementById('levelUserId').value = userId;
            document.getElementById('levelUserName').value = userName;

            // Set current level as selected
            const levelSelect = document.getElementById('levelSelect');
            levelSelect.value = currentLevel;

            new bootstrap.Modal(document.getElementById('assignLevelModal')).show();
        }

        function assignAward(userId, userName, currentAward) {
            document.getElementById('awardUserId').value = userId;
            document.getElementById('awardUserName').value = userName;

            // Set current award as selected if exists
            const awardSelect = document.getElementById('awardSelect');
            awardSelect.value = currentAward || '';

            new bootstrap.Modal(document.getElementById('assignAwardModal')).show();
        }

        function removeAward(userId, userName) {
            document.getElementById('removeAwardUserId').value = userId;
            document.getElementById('removeAwardUserName').value = userName;

            new bootstrap.Modal(document.getElementById('removeAwardModal')).show();
        }

        function viewHistory(userId, userName) {
            document.querySelector('#historyModal .modal-title').textContent = `Achievement History - ${userName}`;

            // Show loading
            document.getElementById('historyContent').innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;

            // Load history via AJAX
            fetch(`ajax/get-achievement-history.php?user_id=${userId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('historyContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('historyContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Failed to load achievement history.
                        </div>
                    `;
                });

            new bootstrap.Modal(document.getElementById('historyModal')).show();
        }
    </script>
</body>
</html>
