-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: shaktipure
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin`
--

DROP TABLE IF EXISTS `admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin`
--

LOCK TABLES `admin` WRITE;
/*!40000 ALTER TABLE `admin` DISABLE KEYS */;
INSERT INTO `admin` VALUES (1,'admin','<EMAIL>','admin123','System Administrator','+91-9999999999','active','2025-07-28 10:42:01','2025-07-28 10:43:23');
/*!40000 ALTER TABLE `admin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `binary_tree`
--

DROP TABLE IF EXISTS `binary_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `binary_tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `parent_id` varchar(20) DEFAULT NULL,
  `left_child` varchar(20) DEFAULT NULL,
  `right_child` varchar(20) DEFAULT NULL,
  `level` int(11) DEFAULT 0,
  `position` enum('left','right','root') DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user` (`user_id`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_level` (`level`),
  CONSTRAINT `binary_tree_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `binary_tree`
--

LOCK TABLES `binary_tree` WRITE;
/*!40000 ALTER TABLE `binary_tree` DISABLE KEYS */;
INSERT INTO `binary_tree` VALUES (1,'SP000001',NULL,'SP20257568','SP20256192',1,NULL,'2025-07-28 10:42:44','2025-07-28 10:52:36'),(2,'SP20257568','SP000001',NULL,NULL,2,'left','2025-07-28 10:44:30','2025-07-28 10:44:30'),(3,'SP20256192','SP000001',NULL,NULL,2,'right','2025-07-28 10:52:36','2025-07-28 10:52:36'),(8,'TEST17547560706505',NULL,NULL,NULL,0,'root','2025-08-09 16:14:30','2025-08-09 16:14:30');
/*!40000 ALTER TABLE `binary_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config`
--

DROP TABLE IF EXISTS `config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL,
  `config_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `config_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config`
--

LOCK TABLES `config` WRITE;
/*!40000 ALTER TABLE `config` DISABLE KEYS */;
INSERT INTO `config` VALUES (1,'pv_rate','0.10','PV to INR conversion rate (1 PV = ₹0.10)',NULL,'2025-07-28 10:42:01'),(2,'daily_capping','130000.00','Maximum daily income per user in INR',NULL,'2025-07-28 10:42:01'),(3,'min_withdrawal','500.00','Minimum withdrawal amount in INR',NULL,'2025-07-28 10:42:01'),(4,'razorpay_mode','test','Razorpay mode: test or live',NULL,'2025-07-28 10:42:01'),(5,'company_name','ShaktiPure MLM','Company name',NULL,'2025-07-28 10:42:01'),(6,'support_email','<EMAIL>','Support email address',NULL,'2025-07-28 10:42:01'),(7,'support_phone','+91-9999999999','Support phone number',NULL,'2025-07-28 10:42:01');
/*!40000 ALTER TABLE `config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `downline_pv_cache`
--

DROP TABLE IF EXISTS `downline_pv_cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `downline_pv_cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `cache_period` varchar(20) NOT NULL,
  `cache_type` enum('daily','weekly','monthly') NOT NULL,
  `left_downline_pv` decimal(12,2) NOT NULL DEFAULT 0.00,
  `right_downline_pv` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_downline_users` int(11) NOT NULL DEFAULT 0,
  `cache_generated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `cache_expires_at` timestamp NULL DEFAULT NULL,
  `is_valid` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_cache_period` (`user_id`,`cache_period`,`cache_type`),
  KEY `idx_user_cache_type` (`user_id`,`cache_type`),
  KEY `idx_cache_expires` (`cache_expires_at`),
  KEY `idx_is_valid` (`is_valid`),
  CONSTRAINT `downline_pv_cache_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `downline_pv_cache`
--

LOCK TABLES `downline_pv_cache` WRITE;
/*!40000 ALTER TABLE `downline_pv_cache` DISABLE KEYS */;
/*!40000 ALTER TABLE `downline_pv_cache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `franchise`
--

DROP TABLE IF EXISTS `franchise`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `franchise` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `franchise_code` varchar(20) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `address` text DEFAULT NULL,
  `commission_rate` decimal(5,2) DEFAULT 5.00,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `franchise_code` (`franchise_code`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `franchise_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `franchise`
--

LOCK TABLES `franchise` WRITE;
/*!40000 ALTER TABLE `franchise` DISABLE KEYS */;
INSERT INTO `franchise` VALUES (1,'FR0001','franchise','<EMAIL>','franchise123','Master Franchise','+91-8888888888','Franchise Address',5.00,'active',1,'2025-07-28 10:42:44','2025-07-28 10:42:44');
/*!40000 ALTER TABLE `franchise` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `income_logs`
--

DROP TABLE IF EXISTS `income_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `income_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `left_pv` decimal(10,2) NOT NULL,
  `right_pv` decimal(10,2) NOT NULL,
  `matched_pv` decimal(10,2) NOT NULL,
  `income_amount` decimal(10,2) NOT NULL,
  `capping_applied` decimal(10,2) DEFAULT 0.00,
  `carry_forward_left` decimal(10,2) DEFAULT 0.00,
  `carry_forward_right` decimal(10,2) DEFAULT 0.00,
  `matching_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_date` (`user_id`,`matching_date`),
  KEY `idx_matching_date` (`matching_date`),
  CONSTRAINT `income_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `income_logs`
--

LOCK TABLES `income_logs` WRITE;
/*!40000 ALTER TABLE `income_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `income_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `income_processing_status`
--

DROP TABLE IF EXISTS `income_processing_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `income_processing_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `processing_type` enum('daily','weekly','monthly') NOT NULL,
  `processing_period` varchar(20) NOT NULL,
  `period_start_date` date NOT NULL,
  `period_end_date` date NOT NULL,
  `status` enum('pending','processing','completed','failed','cancelled') DEFAULT 'pending',
  `total_users_processed` int(11) DEFAULT 0,
  `total_users_with_income` int(11) DEFAULT 0,
  `total_income_distributed` decimal(12,2) DEFAULT 0.00,
  `total_capping_applied` decimal(12,2) DEFAULT 0.00,
  `processing_started_at` timestamp NULL DEFAULT NULL,
  `processing_completed_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `created_by_type` enum('system','admin','cron') DEFAULT 'system',
  `created_by_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_processing_period` (`processing_type`,`processing_period`),
  KEY `idx_status` (`status`),
  KEY `idx_processing_type_period` (`processing_type`,`processing_period`),
  KEY `idx_period_dates` (`period_start_date`,`period_end_date`),
  KEY `idx_processing_started` (`processing_started_at`),
  KEY `idx_processing_completed` (`processing_completed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `income_processing_status`
--

LOCK TABLES `income_processing_status` WRITE;
/*!40000 ALTER TABLE `income_processing_status` DISABLE KEYS */;
/*!40000 ALTER TABLE `income_processing_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `login_logs`
--

DROP TABLE IF EXISTS `login_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('admin','franchise','user') NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `login_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `logout_time` timestamp NULL DEFAULT NULL,
  `status` enum('success','failed') DEFAULT 'success',
  PRIMARY KEY (`id`),
  KEY `idx_user_type` (`user_type`,`user_id`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `login_logs`
--

LOCK TABLES `login_logs` WRITE;
/*!40000 ALTER TABLE `login_logs` DISABLE KEYS */;
INSERT INTO `login_logs` VALUES (1,'user','SP000001','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-28 10:43:08','2025-07-28 10:43:49','success'),(2,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-28 11:04:35',NULL,'success'),(3,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-29 05:50:30',NULL,'success'),(4,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-29 09:07:38','2025-07-29 09:08:38','success'),(5,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-29 09:17:47','2025-07-29 09:17:55','success'),(6,'user','SP000001','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-29 09:18:00','2025-07-29 09:18:37','success'),(7,'user','SP000001','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-30 06:26:23',NULL,'success'),(8,'user','SP000001','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-30 06:29:56','2025-07-30 06:53:26','success'),(9,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-31 08:30:40','2025-07-31 08:48:00','success'),(10,'user','SP20257568','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-31 08:48:16','2025-07-31 08:50:08','success'),(11,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-04 15:07:32',NULL,'success'),(12,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36','2025-08-08 10:27:39','2025-08-08 10:28:44','success');
/*!40000 ALTER TABLE `login_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_assignment_requests`
--

DROP TABLE IF EXISTS `product_assignment_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_assignment_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `franchise_id` int(11) NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) DEFAULT 1,
  `pv_side` enum('left','right') DEFAULT 'left',
  `description` text DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `requested_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `processed_at` timestamp NULL DEFAULT NULL,
  `processed_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_franchise_status` (`franchise_id`,`status`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_status_requested` (`status`,`requested_at`),
  KEY `idx_requested_at` (`requested_at`),
  CONSTRAINT `product_assignment_requests_ibfk_1` FOREIGN KEY (`franchise_id`) REFERENCES `franchise` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_assignment_requests_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `product_assignment_requests_ibfk_3` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_assignment_requests_ibfk_4` FOREIGN KEY (`processed_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_assignment_requests`
--

LOCK TABLES `product_assignment_requests` WRITE;
/*!40000 ALTER TABLE `product_assignment_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_assignment_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_code` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `pv_value` decimal(10,2) NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `product_code` (`product_code`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES (1,'SP002','Gas saf','diuhfdfiguhouguho','SP002_688863340a79e.png',4599.00,2500.00,'active',1,'2025-07-29 05:59:16','2025-07-29 05:59:16'),(2,'sp0002','Braclete','THIS IS TESTING PRODUCT','sp0002_68888f92e9a02.png',4599.00,2500.00,'active',1,'2025-07-29 09:08:34','2025-07-29 09:08:34');
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_orders`
--

DROP TABLE IF EXISTS `purchase_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(30) NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) DEFAULT 1,
  `total_amount` decimal(10,2) NOT NULL,
  `pv_amount` decimal(10,2) NOT NULL,
  `placement_side` enum('left','right') NOT NULL,
  `payment_method` enum('razorpay','manual') DEFAULT 'razorpay',
  `payment_id` varchar(100) DEFAULT NULL,
  `payment_status` enum('pending','completed','failed','refunded') DEFAULT 'pending',
  `order_status` enum('pending','confirmed','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  KEY `idx_user_status` (`user_id`,`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  CONSTRAINT `purchase_orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_orders_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_orders`
--

LOCK TABLES `purchase_orders` WRITE;
/*!40000 ALTER TABLE `purchase_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `purchase_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_income_audit_trail`
--

DROP TABLE IF EXISTS `pv_income_audit_trail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_income_audit_trail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `processing_period` varchar(20) NOT NULL,
  `processing_type` enum('daily','weekly','monthly') NOT NULL,
  `left_pv_available` decimal(10,2) NOT NULL DEFAULT 0.00,
  `right_pv_available` decimal(10,2) NOT NULL DEFAULT 0.00,
  `left_pv_carried` decimal(10,2) NOT NULL DEFAULT 0.00,
  `right_pv_carried` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_left_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_right_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `matched_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `gross_income` decimal(10,2) NOT NULL DEFAULT 0.00,
  `capping_applied` decimal(10,2) NOT NULL DEFAULT 0.00,
  `deductions_applied` decimal(10,2) NOT NULL DEFAULT 0.00,
  `net_income` decimal(10,2) NOT NULL DEFAULT 0.00,
  `carry_forward_left` decimal(10,2) NOT NULL DEFAULT 0.00,
  `carry_forward_right` decimal(10,2) NOT NULL DEFAULT 0.00,
  `pv_transactions_used` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`pv_transactions_used`)),
  `calculation_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`calculation_details`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_period` (`user_id`,`processing_period`),
  KEY `idx_processing_type_period` (`processing_type`,`processing_period`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_matched_pv` (`matched_pv`),
  KEY `idx_net_income` (`net_income`),
  CONSTRAINT `pv_income_audit_trail_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_income_audit_trail`
--

LOCK TABLES `pv_income_audit_trail` WRITE;
/*!40000 ALTER TABLE `pv_income_audit_trail` DISABLE KEYS */;
/*!40000 ALTER TABLE `pv_income_audit_trail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_performance_metrics`
--

DROP TABLE IF EXISTS `pv_performance_metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_performance_metrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `metric_type` enum('processing_time','users_processed','memory_usage','database_queries') NOT NULL,
  `processing_period` varchar(20) NOT NULL,
  `processing_type` enum('daily','weekly','monthly') NOT NULL,
  `metric_value` decimal(15,4) NOT NULL,
  `metric_unit` varchar(20) NOT NULL,
  `additional_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`additional_data`)),
  `recorded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_metric_type_period` (`metric_type`,`processing_period`),
  KEY `idx_processing_type_period` (`processing_type`,`processing_period`),
  KEY `idx_recorded_at` (`recorded_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_performance_metrics`
--

LOCK TABLES `pv_performance_metrics` WRITE;
/*!40000 ALTER TABLE `pv_performance_metrics` DISABLE KEYS */;
/*!40000 ALTER TABLE `pv_performance_metrics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_processing_locks`
--

DROP TABLE IF EXISTS `pv_processing_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_processing_locks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lock_type` enum('daily_processing','weekly_processing','user_processing') NOT NULL,
  `lock_key` varchar(100) NOT NULL,
  `locked_by` varchar(100) NOT NULL,
  `locked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL,
  `process_id` varchar(50) DEFAULT NULL,
  `status` enum('active','expired','released') DEFAULT 'active',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_lock` (`lock_type`,`lock_key`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_status` (`status`),
  KEY `idx_locked_by` (`locked_by`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_processing_locks`
--

LOCK TABLES `pv_processing_locks` WRITE;
/*!40000 ALTER TABLE `pv_processing_locks` DISABLE KEYS */;
INSERT INTO `pv_processing_locks` VALUES (1,'user_processing','TEST17547556648679','system','2025-08-09 16:07:44','2025-08-09 17:07:44','17268_68977250317a5','released'),(3,'user_processing','TEST17547556794967','system','2025-08-09 16:07:59','2025-08-09 17:07:59','19260_6897725fa8a50','released'),(5,'user_processing','TEST17547558849415','system','2025-08-09 16:11:24','2025-08-09 17:11:24','16904_6897732cc2b73','released'),(7,'user_processing','TEST17547559402035','system','2025-08-09 16:12:21','2025-08-09 17:12:21','9764_6897736501e36','released'),(9,'user_processing','TEST17547561919286','system','2025-08-09 16:16:32','2025-08-09 17:16:32','22480_689774601dd32','released'),(14,'user_processing','TEST17547563501266','system','2025-08-09 16:19:10','2025-08-09 17:19:10','12996_689774fef0ea0','released'),(19,'user_processing','TEST17547564063420','system','2025-08-09 16:20:06','2025-08-09 17:20:06','9636_6897753661205','released');
/*!40000 ALTER TABLE `pv_processing_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_transactions`
--

DROP TABLE IF EXISTS `pv_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `transaction_type` enum('purchase','bonus','manual','downline_bonus','self') NOT NULL,
  `pv_amount` decimal(10,2) NOT NULL,
  `side` enum('left','right','self','upline') NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `reference_id` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by_type` enum('admin','franchise','system') DEFAULT 'system',
  `created_by_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `source_user_id` varchar(20) DEFAULT NULL,
  `processing_status` enum('pending','processed','cancelled') DEFAULT 'pending',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `idx_user_side` (`user_id`,`side`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `pv_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `pv_transactions_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=454 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_transactions`
--

LOCK TABLES `pv_transactions` WRITE;
/*!40000 ALTER TABLE `pv_transactions` DISABLE KEYS */;
INSERT INTO `pv_transactions` VALUES (7,'SP000001','manual',319.00,'left',NULL,NULL,'Test PV - Left Side','admin',1,'2025-07-28 11:00:07',NULL,'pending','2025-08-09 16:14:12'),(8,'SP000001','manual',403.00,'right',NULL,NULL,'Test PV - Right Side','admin',1,'2025-07-28 11:00:07',NULL,'pending','2025-08-09 16:14:12'),(9,'SP20257568','manual',310.00,'left',NULL,NULL,'Test PV - Left Side','admin',1,'2025-07-28 11:00:07',NULL,'pending','2025-08-09 16:14:12'),(10,'SP20257568','manual',109.00,'right',NULL,NULL,'Test PV - Right Side','admin',1,'2025-07-28 11:00:07',NULL,'pending','2025-08-09 16:14:12'),(11,'SP20256192','manual',177.00,'left',NULL,NULL,'Test PV - Left Side','admin',1,'2025-07-28 11:00:07',NULL,'pending','2025-08-09 16:14:12'),(12,'SP20256192','manual',319.00,'right',NULL,NULL,'Test PV - Right Side','admin',1,'2025-07-28 11:00:07',NULL,'pending','2025-08-09 16:14:12');
/*!40000 ALTER TABLE `pv_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_usage_tracking`
--

DROP TABLE IF EXISTS `pv_usage_tracking`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_usage_tracking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `pv_transaction_id` int(11) NOT NULL,
  `side` enum('left','right','self') NOT NULL,
  `original_amount` decimal(10,2) NOT NULL,
  `used_amount` decimal(10,2) DEFAULT 0.00,
  `remaining_amount` decimal(10,2) NOT NULL,
  `week_used` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `processing_period` varchar(20) DEFAULT NULL,
  `status` enum('available','partially_used','fully_used') DEFAULT 'available',
  PRIMARY KEY (`id`),
  KEY `pv_transaction_id` (`pv_transaction_id`),
  KEY `idx_user_remaining` (`user_id`,`remaining_amount`),
  KEY `idx_side` (`side`),
  KEY `idx_week_used` (`week_used`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `pv_usage_tracking_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `pv_usage_tracking_ibfk_2` FOREIGN KEY (`pv_transaction_id`) REFERENCES `pv_transactions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=436 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_usage_tracking`
--

LOCK TABLES `pv_usage_tracking` WRITE;
/*!40000 ALTER TABLE `pv_usage_tracking` DISABLE KEYS */;
INSERT INTO `pv_usage_tracking` VALUES (1,'SP000001',7,'left',319.00,319.00,0.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:52',NULL,'available'),(2,'SP000001',8,'right',403.00,319.00,84.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:52',NULL,'available'),(3,'SP20257568',9,'left',310.00,109.00,201.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:59',NULL,'available'),(4,'SP20257568',10,'right',109.00,109.00,0.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:59',NULL,'available'),(5,'SP20256192',11,'left',177.00,177.00,0.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:59',NULL,'available'),(6,'SP20256192',12,'right',319.00,177.00,142.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:59',NULL,'available');
/*!40000 ALTER TABLE `pv_usage_tracking` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_logs`
--

DROP TABLE IF EXISTS `system_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `log_type` enum('info','warning','error','debug','pv_processing','income_generation') NOT NULL,
  `message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `category` varchar(50) NOT NULL DEFAULT 'general',
  `context` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`context`)),
  `user_id` varchar(20) DEFAULT NULL,
  `processing_period` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=483 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_logs`
--

LOCK TABLES `system_logs` WRITE;
/*!40000 ALTER TABLE `system_logs` DISABLE KEYS */;
INSERT INTO `system_logs` VALUES (1,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547558849415\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(2,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User ID is required\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(3,'error','[pv_processing] Failed to process user income - Context: {\"user_id\":\"TEST17547558849415\",\"processing_period\":\"2025-08-09\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'where clause\'\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(4,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547558849415\",\"pv_amount\":50,\"side\":\"right\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(5,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547558849415\",\"pv_amount\":30,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(6,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547558849415\",\"pv_amount\":1,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(7,'error','[pv_processing] Failed to process user income - Context: {\"user_id\":\"TEST17547558849415\",\"processing_period\":\"2025-08-11\",\"error\":\"Unable to acquire processing lock for user TEST17547558849415\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(8,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547558849415\",\"pv_amount\":0,\"side\":\"left\",\"error\":\"PV amount must be a positive number\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(9,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547558849415\",\"pv_amount\":999999.99,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(10,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"INVALID_USER\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User not found or inactive\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(11,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547558849415\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:11:24','general',NULL,NULL,NULL),(12,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547559402035\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:12:20','general',NULL,NULL,NULL),(13,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User ID is required\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(14,'error','[pv_processing] Failed to process user income - Context: {\"user_id\":\"TEST17547559402035\",\"processing_period\":\"2025-08-09\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'where clause\'\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(15,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547559402035\",\"pv_amount\":50,\"side\":\"right\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(16,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547559402035\",\"pv_amount\":30,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(17,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547559402035\",\"pv_amount\":1,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(18,'error','[pv_processing] Failed to process user income - Context: {\"user_id\":\"TEST17547559402035\",\"processing_period\":\"2025-08-11\",\"error\":\"Unable to acquire processing lock for user TEST17547559402035\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(19,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547559402035\",\"pv_amount\":0,\"side\":\"left\",\"error\":\"PV amount must be a positive number\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(20,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547559402035\",\"pv_amount\":999999.99,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(21,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"INVALID_USER\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User not found or inactive\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(22,'error','[pv_processing] Failed to add PV - Context: {\"user_id\":\"TEST17547559402035\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"SQLSTATE[42S22]: Column not found: 1054 Unknown column \'status\' in \'field list\'\"}','2025-08-09 16:12:21','general',NULL,NULL,NULL),(23,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":100,\"side\":\"left\",\"transaction_id\":\"26\"}','TEST17547561919286',NULL),(24,'error','Failed to add PV','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User ID is required\"}','',NULL),(25,'info','Income processed successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"processing_period\":\"2025-08-09\",\"processing_type\":\"daily\",\"total_left_pv\":100,\"total_right_pv\":0,\"matched_pv\":0,\"gross_income\":0,\"net_income\":0,\"capping_applied\":0,\"deductions\":0,\"carry_forward_left\":100,\"carry_forward_right\":0}','TEST17547561919286','2025-08-09'),(26,'error','Failed to process user income','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"processing_period\":\"2025-08-09\",\"error\":\"Unable to acquire processing lock for user TEST17547561919286\"}','TEST17547561919286','2025-08-09'),(27,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":50,\"side\":\"right\",\"transaction_id\":\"27\"}','TEST17547561919286',NULL),(28,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":30,\"side\":\"left\",\"transaction_id\":\"28\"}','TEST17547561919286',NULL),(29,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":20,\"side\":\"left\",\"transaction_id\":\"29\"}','TEST17547561919286',NULL),(30,'error','Failed to process user income','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"processing_period\":\"2025-08-10\",\"error\":\"Unable to acquire processing lock for user TEST17547561919286\"}','TEST17547561919286','2025-08-10'),(31,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"30\"}','TEST17547561919286',NULL),(32,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"31\"}','TEST17547561919286',NULL),(33,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"32\"}','TEST17547561919286',NULL),(34,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"33\"}','TEST17547561919286',NULL),(35,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"34\"}','TEST17547561919286',NULL),(36,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"35\"}','TEST17547561919286',NULL),(37,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"36\"}','TEST17547561919286',NULL),(38,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"37\"}','TEST17547561919286',NULL),(39,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"38\"}','TEST17547561919286',NULL),(40,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"39\"}','TEST17547561919286',NULL),(41,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"40\"}','TEST17547561919286',NULL),(42,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"41\"}','TEST17547561919286',NULL),(43,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"42\"}','TEST17547561919286',NULL),(44,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"43\"}','TEST17547561919286',NULL),(45,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"44\"}','TEST17547561919286',NULL),(46,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"45\"}','TEST17547561919286',NULL),(47,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"46\"}','TEST17547561919286',NULL),(48,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"47\"}','TEST17547561919286',NULL),(49,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"48\"}','TEST17547561919286',NULL),(50,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"49\"}','TEST17547561919286',NULL),(51,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"50\"}','TEST17547561919286',NULL),(52,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"51\"}','TEST17547561919286',NULL),(53,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"52\"}','TEST17547561919286',NULL),(54,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"53\"}','TEST17547561919286',NULL),(55,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"54\"}','TEST17547561919286',NULL),(56,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"55\"}','TEST17547561919286',NULL),(57,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"56\"}','TEST17547561919286',NULL),(58,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"57\"}','TEST17547561919286',NULL),(59,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"58\"}','TEST17547561919286',NULL),(60,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"59\"}','TEST17547561919286',NULL),(61,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"60\"}','TEST17547561919286',NULL),(62,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"61\"}','TEST17547561919286',NULL),(63,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"62\"}','TEST17547561919286',NULL),(64,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"63\"}','TEST17547561919286',NULL),(65,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"64\"}','TEST17547561919286',NULL),(66,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"65\"}','TEST17547561919286',NULL),(67,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"66\"}','TEST17547561919286',NULL),(68,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"67\"}','TEST17547561919286',NULL),(69,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"68\"}','TEST17547561919286',NULL),(70,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"69\"}','TEST17547561919286',NULL),(71,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"70\"}','TEST17547561919286',NULL),(72,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"71\"}','TEST17547561919286',NULL),(73,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"72\"}','TEST17547561919286',NULL),(74,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"73\"}','TEST17547561919286',NULL),(75,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"74\"}','TEST17547561919286',NULL),(76,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"75\"}','TEST17547561919286',NULL),(77,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"76\"}','TEST17547561919286',NULL),(78,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"77\"}','TEST17547561919286',NULL),(79,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"78\"}','TEST17547561919286',NULL),(80,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"79\"}','TEST17547561919286',NULL),(81,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"80\"}','TEST17547561919286',NULL),(82,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"81\"}','TEST17547561919286',NULL),(83,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"82\"}','TEST17547561919286',NULL),(84,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"83\"}','TEST17547561919286',NULL),(85,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"84\"}','TEST17547561919286',NULL),(86,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"85\"}','TEST17547561919286',NULL),(87,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"86\"}','TEST17547561919286',NULL),(88,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"87\"}','TEST17547561919286',NULL),(89,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"88\"}','TEST17547561919286',NULL),(90,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"89\"}','TEST17547561919286',NULL),(91,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"90\"}','TEST17547561919286',NULL),(92,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"91\"}','TEST17547561919286',NULL),(93,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"92\"}','TEST17547561919286',NULL),(94,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"93\"}','TEST17547561919286',NULL),(95,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"94\"}','TEST17547561919286',NULL),(96,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"95\"}','TEST17547561919286',NULL),(97,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"96\"}','TEST17547561919286',NULL),(98,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"97\"}','TEST17547561919286',NULL),(99,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"98\"}','TEST17547561919286',NULL),(100,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"99\"}','TEST17547561919286',NULL),(101,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"100\"}','TEST17547561919286',NULL),(102,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"101\"}','TEST17547561919286',NULL),(103,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"102\"}','TEST17547561919286',NULL),(104,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"103\"}','TEST17547561919286',NULL),(105,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"104\"}','TEST17547561919286',NULL),(106,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"105\"}','TEST17547561919286',NULL),(107,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"106\"}','TEST17547561919286',NULL),(108,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"107\"}','TEST17547561919286',NULL),(109,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"108\"}','TEST17547561919286',NULL),(110,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"109\"}','TEST17547561919286',NULL),(111,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"110\"}','TEST17547561919286',NULL),(112,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"111\"}','TEST17547561919286',NULL),(113,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"112\"}','TEST17547561919286',NULL),(114,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"113\"}','TEST17547561919286',NULL),(115,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"114\"}','TEST17547561919286',NULL),(116,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"115\"}','TEST17547561919286',NULL),(117,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"116\"}','TEST17547561919286',NULL),(118,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"117\"}','TEST17547561919286',NULL),(119,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"118\"}','TEST17547561919286',NULL),(120,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"119\"}','TEST17547561919286',NULL),(121,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"120\"}','TEST17547561919286',NULL),(122,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"121\"}','TEST17547561919286',NULL),(123,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"122\"}','TEST17547561919286',NULL),(124,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"123\"}','TEST17547561919286',NULL),(125,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"124\"}','TEST17547561919286',NULL),(126,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"125\"}','TEST17547561919286',NULL),(127,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"126\"}','TEST17547561919286',NULL),(128,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"127\"}','TEST17547561919286',NULL),(129,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"128\"}','TEST17547561919286',NULL),(130,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"129\"}','TEST17547561919286',NULL),(131,'error','Failed to process user income','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"processing_period\":\"2025-08-11\",\"error\":\"Unable to acquire processing lock for user TEST17547561919286\"}','TEST17547561919286','2025-08-11'),(132,'error','Failed to add PV','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":0,\"side\":\"left\",\"error\":\"PV amount must be a positive number\"}','TEST17547561919286',NULL),(133,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":999999.99,\"side\":\"left\",\"transaction_id\":\"130\"}','TEST17547561919286',NULL),(134,'error','Failed to add PV','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"INVALID_USER\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User not found or inactive\"}','INVALID_USER',NULL),(135,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":100,\"side\":\"left\",\"transaction_id\":\"131\"}','TEST17547561919286',NULL),(136,'info','PV added successfully','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"pv_amount\":80,\"side\":\"right\",\"transaction_id\":\"132\"}','TEST17547561919286',NULL),(137,'error','Failed to process user income','2025-08-09 16:16:32','pv_processing','{\"user_id\":\"TEST17547561919286\",\"processing_period\":\"2025-08-12\",\"error\":\"Unable to acquire processing lock for user TEST17547561919286\"}','TEST17547561919286','2025-08-12'),(138,'info','PV added successfully','2025-08-09 16:19:10','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":100,\"side\":\"left\",\"transaction_id\":\"133\"}','TEST17547563501266',NULL),(139,'error','Failed to add PV','2025-08-09 16:19:10','pv_processing','{\"user_id\":\"\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User ID is required\"}','',NULL),(140,'info','Income processed successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"processing_period\":\"2025-08-19\",\"processing_type\":\"daily\",\"total_left_pv\":100,\"total_right_pv\":0,\"matched_pv\":0,\"gross_income\":0,\"net_income\":0,\"capping_applied\":0,\"deductions\":0,\"carry_forward_left\":100,\"carry_forward_right\":0}','TEST17547563501266','2025-08-19'),(141,'error','Failed to process user income','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"processing_period\":\"2025-08-19\",\"error\":\"Unable to acquire processing lock for user TEST17547563501266\"}','TEST17547563501266','2025-08-19'),(142,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":50,\"side\":\"right\",\"transaction_id\":\"134\"}','TEST17547563501266',NULL),(143,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":30,\"side\":\"left\",\"transaction_id\":\"135\"}','TEST17547563501266',NULL),(144,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":20,\"side\":\"left\",\"transaction_id\":\"136\"}','TEST17547563501266',NULL),(145,'error','Failed to process user income','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"processing_period\":\"2025-08-20\",\"error\":\"Unable to acquire processing lock for user TEST17547563501266\"}','TEST17547563501266','2025-08-20'),(146,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"137\"}','TEST17547563501266',NULL),(147,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"138\"}','TEST17547563501266',NULL),(148,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"139\"}','TEST17547563501266',NULL),(149,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"140\"}','TEST17547563501266',NULL),(150,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"141\"}','TEST17547563501266',NULL),(151,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"142\"}','TEST17547563501266',NULL),(152,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"143\"}','TEST17547563501266',NULL),(153,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"144\"}','TEST17547563501266',NULL),(154,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"145\"}','TEST17547563501266',NULL),(155,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"146\"}','TEST17547563501266',NULL),(156,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"147\"}','TEST17547563501266',NULL),(157,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"148\"}','TEST17547563501266',NULL),(158,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"149\"}','TEST17547563501266',NULL),(159,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"150\"}','TEST17547563501266',NULL),(160,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"151\"}','TEST17547563501266',NULL),(161,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"152\"}','TEST17547563501266',NULL),(162,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"153\"}','TEST17547563501266',NULL),(163,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"154\"}','TEST17547563501266',NULL),(164,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"155\"}','TEST17547563501266',NULL),(165,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"156\"}','TEST17547563501266',NULL),(166,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"157\"}','TEST17547563501266',NULL),(167,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"158\"}','TEST17547563501266',NULL),(168,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"159\"}','TEST17547563501266',NULL),(169,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"160\"}','TEST17547563501266',NULL),(170,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"161\"}','TEST17547563501266',NULL),(171,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"162\"}','TEST17547563501266',NULL),(172,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"163\"}','TEST17547563501266',NULL),(173,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"164\"}','TEST17547563501266',NULL),(174,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"165\"}','TEST17547563501266',NULL),(175,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"166\"}','TEST17547563501266',NULL),(176,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"167\"}','TEST17547563501266',NULL),(177,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"168\"}','TEST17547563501266',NULL),(178,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"169\"}','TEST17547563501266',NULL),(179,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"170\"}','TEST17547563501266',NULL),(180,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"171\"}','TEST17547563501266',NULL),(181,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"172\"}','TEST17547563501266',NULL),(182,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"173\"}','TEST17547563501266',NULL),(183,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"174\"}','TEST17547563501266',NULL),(184,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"175\"}','TEST17547563501266',NULL),(185,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"176\"}','TEST17547563501266',NULL),(186,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"177\"}','TEST17547563501266',NULL),(187,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"178\"}','TEST17547563501266',NULL),(188,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"179\"}','TEST17547563501266',NULL),(189,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"180\"}','TEST17547563501266',NULL),(190,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"181\"}','TEST17547563501266',NULL),(191,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"182\"}','TEST17547563501266',NULL),(192,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"183\"}','TEST17547563501266',NULL),(193,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"184\"}','TEST17547563501266',NULL),(194,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"185\"}','TEST17547563501266',NULL),(195,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"186\"}','TEST17547563501266',NULL),(196,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"187\"}','TEST17547563501266',NULL),(197,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"188\"}','TEST17547563501266',NULL),(198,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"189\"}','TEST17547563501266',NULL),(199,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"190\"}','TEST17547563501266',NULL),(200,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"191\"}','TEST17547563501266',NULL),(201,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"192\"}','TEST17547563501266',NULL),(202,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"193\"}','TEST17547563501266',NULL),(203,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"194\"}','TEST17547563501266',NULL),(204,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"195\"}','TEST17547563501266',NULL),(205,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"196\"}','TEST17547563501266',NULL),(206,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"197\"}','TEST17547563501266',NULL),(207,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"198\"}','TEST17547563501266',NULL),(208,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"199\"}','TEST17547563501266',NULL),(209,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"200\"}','TEST17547563501266',NULL),(210,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"201\"}','TEST17547563501266',NULL),(211,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"202\"}','TEST17547563501266',NULL),(212,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"203\"}','TEST17547563501266',NULL),(213,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"204\"}','TEST17547563501266',NULL),(214,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"205\"}','TEST17547563501266',NULL),(215,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"206\"}','TEST17547563501266',NULL),(216,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"207\"}','TEST17547563501266',NULL),(217,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"208\"}','TEST17547563501266',NULL),(218,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"209\"}','TEST17547563501266',NULL),(219,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"210\"}','TEST17547563501266',NULL),(220,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"211\"}','TEST17547563501266',NULL),(221,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"212\"}','TEST17547563501266',NULL),(222,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"213\"}','TEST17547563501266',NULL),(223,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"214\"}','TEST17547563501266',NULL),(224,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"215\"}','TEST17547563501266',NULL),(225,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"216\"}','TEST17547563501266',NULL),(226,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"217\"}','TEST17547563501266',NULL),(227,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"218\"}','TEST17547563501266',NULL),(228,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"219\"}','TEST17547563501266',NULL),(229,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"220\"}','TEST17547563501266',NULL),(230,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"221\"}','TEST17547563501266',NULL),(231,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"222\"}','TEST17547563501266',NULL),(232,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"223\"}','TEST17547563501266',NULL),(233,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"224\"}','TEST17547563501266',NULL),(234,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"225\"}','TEST17547563501266',NULL),(235,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"226\"}','TEST17547563501266',NULL),(236,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"227\"}','TEST17547563501266',NULL),(237,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"228\"}','TEST17547563501266',NULL),(238,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"229\"}','TEST17547563501266',NULL),(239,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"230\"}','TEST17547563501266',NULL),(240,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"231\"}','TEST17547563501266',NULL),(241,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"232\"}','TEST17547563501266',NULL),(242,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"233\"}','TEST17547563501266',NULL),(243,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"234\"}','TEST17547563501266',NULL),(244,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"235\"}','TEST17547563501266',NULL),(245,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"236\"}','TEST17547563501266',NULL),(246,'error','Failed to process user income','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"processing_period\":\"2025-08-21\",\"error\":\"Unable to acquire processing lock for user TEST17547563501266\"}','TEST17547563501266','2025-08-21'),(247,'error','Failed to add PV','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":0,\"side\":\"left\",\"error\":\"PV amount must be a positive number\"}','TEST17547563501266',NULL),(248,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":999999.99,\"side\":\"left\",\"transaction_id\":\"237\"}','TEST17547563501266',NULL),(249,'error','Failed to add PV','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"INVALID_USER\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User not found or inactive\"}','INVALID_USER',NULL),(250,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":100,\"side\":\"left\",\"transaction_id\":\"238\"}','TEST17547563501266',NULL),(251,'info','PV added successfully','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"pv_amount\":80,\"side\":\"right\",\"transaction_id\":\"239\"}','TEST17547563501266',NULL),(252,'error','Failed to process user income','2025-08-09 16:19:11','pv_processing','{\"user_id\":\"TEST17547563501266\",\"processing_period\":\"2025-08-22\",\"error\":\"Unable to acquire processing lock for user TEST17547563501266\"}','TEST17547563501266','2025-08-22'),(253,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":100,\"side\":\"left\",\"transaction_id\":\"240\"}','TEST17547564063420',NULL),(254,'error','Failed to add PV','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User ID is required\"}','',NULL),(255,'info','Income processed successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"processing_period\":\"2025-08-19\",\"processing_type\":\"daily\",\"total_left_pv\":100,\"total_right_pv\":0,\"matched_pv\":0,\"gross_income\":0,\"net_income\":0,\"capping_applied\":0,\"deductions\":0,\"carry_forward_left\":100,\"carry_forward_right\":0}','TEST17547564063420','2025-08-19'),(256,'error','Failed to process user income','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"processing_period\":\"2025-08-19\",\"error\":\"Unable to acquire processing lock for user TEST17547564063420\"}','TEST17547564063420','2025-08-19'),(257,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":50,\"side\":\"right\",\"transaction_id\":\"241\"}','TEST17547564063420',NULL),(258,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":30,\"side\":\"left\",\"transaction_id\":\"242\"}','TEST17547564063420',NULL),(259,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":20,\"side\":\"left\",\"transaction_id\":\"243\"}','TEST17547564063420',NULL),(260,'error','Failed to process user income','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"processing_period\":\"2025-08-20\",\"error\":\"Unable to acquire processing lock for user TEST17547564063420\"}','TEST17547564063420','2025-08-20'),(261,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"244\"}','TEST17547564063420',NULL),(262,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"245\"}','TEST17547564063420',NULL),(263,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"246\"}','TEST17547564063420',NULL),(264,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"247\"}','TEST17547564063420',NULL),(265,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"248\"}','TEST17547564063420',NULL),(266,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"249\"}','TEST17547564063420',NULL),(267,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"250\"}','TEST17547564063420',NULL),(268,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"251\"}','TEST17547564063420',NULL),(269,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"252\"}','TEST17547564063420',NULL),(270,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"253\"}','TEST17547564063420',NULL),(271,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"254\"}','TEST17547564063420',NULL),(272,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"255\"}','TEST17547564063420',NULL),(273,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"256\"}','TEST17547564063420',NULL),(274,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"257\"}','TEST17547564063420',NULL),(275,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"258\"}','TEST17547564063420',NULL),(276,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"259\"}','TEST17547564063420',NULL),(277,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"260\"}','TEST17547564063420',NULL),(278,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"261\"}','TEST17547564063420',NULL),(279,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"262\"}','TEST17547564063420',NULL),(280,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"263\"}','TEST17547564063420',NULL),(281,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"264\"}','TEST17547564063420',NULL),(282,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"265\"}','TEST17547564063420',NULL),(283,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"266\"}','TEST17547564063420',NULL),(284,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"267\"}','TEST17547564063420',NULL),(285,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"268\"}','TEST17547564063420',NULL),(286,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"269\"}','TEST17547564063420',NULL),(287,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"270\"}','TEST17547564063420',NULL),(288,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"271\"}','TEST17547564063420',NULL),(289,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"272\"}','TEST17547564063420',NULL),(290,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"273\"}','TEST17547564063420',NULL),(291,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"274\"}','TEST17547564063420',NULL),(292,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"275\"}','TEST17547564063420',NULL),(293,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"276\"}','TEST17547564063420',NULL),(294,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"277\"}','TEST17547564063420',NULL),(295,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"278\"}','TEST17547564063420',NULL),(296,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"279\"}','TEST17547564063420',NULL),(297,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"280\"}','TEST17547564063420',NULL),(298,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"281\"}','TEST17547564063420',NULL),(299,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"282\"}','TEST17547564063420',NULL),(300,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"283\"}','TEST17547564063420',NULL),(301,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"284\"}','TEST17547564063420',NULL),(302,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"285\"}','TEST17547564063420',NULL),(303,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"286\"}','TEST17547564063420',NULL),(304,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"287\"}','TEST17547564063420',NULL),(305,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"288\"}','TEST17547564063420',NULL),(306,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"289\"}','TEST17547564063420',NULL),(307,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"290\"}','TEST17547564063420',NULL),(308,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"291\"}','TEST17547564063420',NULL),(309,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"292\"}','TEST17547564063420',NULL),(310,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"293\"}','TEST17547564063420',NULL),(311,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"294\"}','TEST17547564063420',NULL),(312,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"295\"}','TEST17547564063420',NULL),(313,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"296\"}','TEST17547564063420',NULL),(314,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"297\"}','TEST17547564063420',NULL),(315,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"298\"}','TEST17547564063420',NULL),(316,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"299\"}','TEST17547564063420',NULL),(317,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"300\"}','TEST17547564063420',NULL),(318,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"301\"}','TEST17547564063420',NULL),(319,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"302\"}','TEST17547564063420',NULL),(320,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"303\"}','TEST17547564063420',NULL),(321,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"304\"}','TEST17547564063420',NULL),(322,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"305\"}','TEST17547564063420',NULL),(323,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"306\"}','TEST17547564063420',NULL),(324,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"307\"}','TEST17547564063420',NULL),(325,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"308\"}','TEST17547564063420',NULL),(326,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"309\"}','TEST17547564063420',NULL),(327,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"310\"}','TEST17547564063420',NULL),(328,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"311\"}','TEST17547564063420',NULL),(329,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"312\"}','TEST17547564063420',NULL),(330,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"313\"}','TEST17547564063420',NULL),(331,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"314\"}','TEST17547564063420',NULL),(332,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"315\"}','TEST17547564063420',NULL),(333,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"316\"}','TEST17547564063420',NULL),(334,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"317\"}','TEST17547564063420',NULL),(335,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"318\"}','TEST17547564063420',NULL),(336,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"319\"}','TEST17547564063420',NULL),(337,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"320\"}','TEST17547564063420',NULL),(338,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"321\"}','TEST17547564063420',NULL),(339,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"322\"}','TEST17547564063420',NULL),(340,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"323\"}','TEST17547564063420',NULL),(341,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"324\"}','TEST17547564063420',NULL),(342,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"325\"}','TEST17547564063420',NULL),(343,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"326\"}','TEST17547564063420',NULL),(344,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"327\"}','TEST17547564063420',NULL),(345,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"328\"}','TEST17547564063420',NULL),(346,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"329\"}','TEST17547564063420',NULL),(347,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"330\"}','TEST17547564063420',NULL),(348,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"331\"}','TEST17547564063420',NULL),(349,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"332\"}','TEST17547564063420',NULL),(350,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"333\"}','TEST17547564063420',NULL),(351,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"334\"}','TEST17547564063420',NULL),(352,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"335\"}','TEST17547564063420',NULL),(353,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"336\"}','TEST17547564063420',NULL),(354,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"337\"}','TEST17547564063420',NULL),(355,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"338\"}','TEST17547564063420',NULL),(356,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"339\"}','TEST17547564063420',NULL),(357,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"340\"}','TEST17547564063420',NULL),(358,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"341\"}','TEST17547564063420',NULL),(359,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"342\"}','TEST17547564063420',NULL),(360,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"343\"}','TEST17547564063420',NULL),(361,'error','Failed to process user income','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"processing_period\":\"2025-08-21\",\"error\":\"Unable to acquire processing lock for user TEST17547564063420\"}','TEST17547564063420','2025-08-21'),(362,'error','Failed to add PV','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":0,\"side\":\"left\",\"error\":\"PV amount must be a positive number\"}','TEST17547564063420',NULL),(363,'info','PV added successfully','2025-08-09 16:20:06','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":999999.99,\"side\":\"left\",\"transaction_id\":\"344\"}','TEST17547564063420',NULL),(364,'error','Failed to add PV','2025-08-09 16:20:07','pv_processing','{\"user_id\":\"INVALID_USER\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User not found or inactive\"}','INVALID_USER',NULL),(365,'info','PV added successfully','2025-08-09 16:20:07','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":100,\"side\":\"left\",\"transaction_id\":\"345\"}','TEST17547564063420',NULL),(366,'info','PV added successfully','2025-08-09 16:20:07','pv_processing','{\"user_id\":\"TEST17547564063420\",\"pv_amount\":80,\"side\":\"right\",\"transaction_id\":\"346\"}','TEST17547564063420',NULL),(367,'error','Failed to process user income','2025-08-09 16:20:07','pv_processing','{\"user_id\":\"TEST17547564063420\",\"processing_period\":\"2025-08-22\",\"error\":\"Unable to acquire processing lock for user TEST17547564063420\"}','TEST17547564063420','2025-08-22'),(368,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":100,\"side\":\"left\",\"transaction_id\":\"347\"}','TEST17547565735687',NULL),(369,'error','Failed to add PV','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User ID is required\"}','',NULL),(370,'info','Income processed successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"processing_period\":\"2025-08-19\",\"processing_type\":\"daily\",\"total_left_pv\":100,\"total_right_pv\":0,\"matched_pv\":0,\"gross_income\":0,\"net_income\":0,\"capping_applied\":0,\"deductions\":0,\"carry_forward_left\":100,\"carry_forward_right\":0}','TEST17547565735687','2025-08-19'),(371,'info','User already processed for period','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"processing_period\":\"2025-08-19\",\"processing_type\":\"daily\"}','TEST17547565735687','2025-08-19'),(372,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":50,\"side\":\"right\",\"transaction_id\":\"348\"}','TEST17547565735687',NULL),(373,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":30,\"side\":\"left\",\"transaction_id\":\"349\"}','TEST17547565735687',NULL),(374,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":20,\"side\":\"left\",\"transaction_id\":\"350\"}','TEST17547565735687',NULL),(375,'info','Income processed successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"processing_period\":\"2025-08-20\",\"processing_type\":\"daily\",\"total_left_pv\":250,\"total_right_pv\":50,\"matched_pv\":50,\"gross_income\":5,\"net_income\":4.25,\"capping_applied\":0,\"deductions\":0.75,\"carry_forward_left\":200,\"carry_forward_right\":0,\"service_charge\":0.5,\"tds_amount\":0.25}','TEST17547565735687','2025-08-20'),(376,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"351\"}','TEST17547565735687',NULL),(377,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"352\"}','TEST17547565735687',NULL),(378,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"353\"}','TEST17547565735687',NULL),(379,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"354\"}','TEST17547565735687',NULL),(380,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"355\"}','TEST17547565735687',NULL),(381,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"356\"}','TEST17547565735687',NULL),(382,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"357\"}','TEST17547565735687',NULL),(383,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"358\"}','TEST17547565735687',NULL),(384,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"359\"}','TEST17547565735687',NULL),(385,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"360\"}','TEST17547565735687',NULL),(386,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"361\"}','TEST17547565735687',NULL),(387,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"362\"}','TEST17547565735687',NULL),(388,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"363\"}','TEST17547565735687',NULL),(389,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"364\"}','TEST17547565735687',NULL),(390,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"365\"}','TEST17547565735687',NULL),(391,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"366\"}','TEST17547565735687',NULL),(392,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"367\"}','TEST17547565735687',NULL),(393,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"368\"}','TEST17547565735687',NULL),(394,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"369\"}','TEST17547565735687',NULL),(395,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"370\"}','TEST17547565735687',NULL),(396,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"371\"}','TEST17547565735687',NULL),(397,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"372\"}','TEST17547565735687',NULL),(398,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"373\"}','TEST17547565735687',NULL),(399,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"374\"}','TEST17547565735687',NULL),(400,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"375\"}','TEST17547565735687',NULL),(401,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"376\"}','TEST17547565735687',NULL),(402,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"377\"}','TEST17547565735687',NULL),(403,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"378\"}','TEST17547565735687',NULL),(404,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"379\"}','TEST17547565735687',NULL),(405,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"380\"}','TEST17547565735687',NULL),(406,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"381\"}','TEST17547565735687',NULL),(407,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"382\"}','TEST17547565735687',NULL),(408,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"383\"}','TEST17547565735687',NULL),(409,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"384\"}','TEST17547565735687',NULL),(410,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"385\"}','TEST17547565735687',NULL),(411,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"386\"}','TEST17547565735687',NULL),(412,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"387\"}','TEST17547565735687',NULL),(413,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"388\"}','TEST17547565735687',NULL),(414,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"389\"}','TEST17547565735687',NULL),(415,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"390\"}','TEST17547565735687',NULL),(416,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"391\"}','TEST17547565735687',NULL),(417,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"392\"}','TEST17547565735687',NULL),(418,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"393\"}','TEST17547565735687',NULL),(419,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"394\"}','TEST17547565735687',NULL),(420,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"395\"}','TEST17547565735687',NULL),(421,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"396\"}','TEST17547565735687',NULL),(422,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"397\"}','TEST17547565735687',NULL),(423,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"398\"}','TEST17547565735687',NULL),(424,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"399\"}','TEST17547565735687',NULL),(425,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"400\"}','TEST17547565735687',NULL),(426,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"401\"}','TEST17547565735687',NULL),(427,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"402\"}','TEST17547565735687',NULL),(428,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"403\"}','TEST17547565735687',NULL),(429,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"404\"}','TEST17547565735687',NULL),(430,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"405\"}','TEST17547565735687',NULL),(431,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"406\"}','TEST17547565735687',NULL),(432,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"407\"}','TEST17547565735687',NULL),(433,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"408\"}','TEST17547565735687',NULL),(434,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"409\"}','TEST17547565735687',NULL),(435,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"410\"}','TEST17547565735687',NULL),(436,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"411\"}','TEST17547565735687',NULL),(437,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"412\"}','TEST17547565735687',NULL),(438,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"413\"}','TEST17547565735687',NULL),(439,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"414\"}','TEST17547565735687',NULL),(440,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"415\"}','TEST17547565735687',NULL),(441,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"416\"}','TEST17547565735687',NULL),(442,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"417\"}','TEST17547565735687',NULL),(443,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"418\"}','TEST17547565735687',NULL),(444,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"419\"}','TEST17547565735687',NULL),(445,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"420\"}','TEST17547565735687',NULL),(446,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"421\"}','TEST17547565735687',NULL),(447,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"422\"}','TEST17547565735687',NULL),(448,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"423\"}','TEST17547565735687',NULL),(449,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"424\"}','TEST17547565735687',NULL),(450,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"425\"}','TEST17547565735687',NULL),(451,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"426\"}','TEST17547565735687',NULL),(452,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"427\"}','TEST17547565735687',NULL),(453,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"428\"}','TEST17547565735687',NULL),(454,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"429\"}','TEST17547565735687',NULL),(455,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"430\"}','TEST17547565735687',NULL),(456,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"431\"}','TEST17547565735687',NULL),(457,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"432\"}','TEST17547565735687',NULL),(458,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"433\"}','TEST17547565735687',NULL),(459,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"434\"}','TEST17547565735687',NULL),(460,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"435\"}','TEST17547565735687',NULL),(461,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"436\"}','TEST17547565735687',NULL),(462,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"437\"}','TEST17547565735687',NULL),(463,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"438\"}','TEST17547565735687',NULL),(464,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"439\"}','TEST17547565735687',NULL),(465,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"440\"}','TEST17547565735687',NULL),(466,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"441\"}','TEST17547565735687',NULL),(467,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"442\"}','TEST17547565735687',NULL),(468,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"443\"}','TEST17547565735687',NULL),(469,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"444\"}','TEST17547565735687',NULL),(470,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"445\"}','TEST17547565735687',NULL),(471,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"446\"}','TEST17547565735687',NULL),(472,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"447\"}','TEST17547565735687',NULL),(473,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"448\"}','TEST17547565735687',NULL),(474,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"449\"}','TEST17547565735687',NULL),(475,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":1,\"side\":\"left\",\"transaction_id\":\"450\"}','TEST17547565735687',NULL),(476,'info','Income processed successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"processing_period\":\"2025-08-21\",\"processing_type\":\"daily\",\"total_left_pv\":350,\"total_right_pv\":0,\"matched_pv\":0,\"gross_income\":0,\"net_income\":0,\"capping_applied\":0,\"deductions\":0,\"carry_forward_left\":350,\"carry_forward_right\":0}','TEST17547565735687','2025-08-21'),(477,'error','Failed to add PV','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":0,\"side\":\"left\",\"error\":\"PV amount must be a positive number\"}','TEST17547565735687',NULL),(478,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":999999.99,\"side\":\"left\",\"transaction_id\":\"451\"}','TEST17547565735687',NULL),(479,'error','Failed to add PV','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"INVALID_USER\",\"pv_amount\":100,\"side\":\"left\",\"error\":\"User not found or inactive\"}','INVALID_USER',NULL),(480,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":100,\"side\":\"left\",\"transaction_id\":\"452\"}','TEST17547565735687',NULL),(481,'info','PV added successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"pv_amount\":80,\"side\":\"right\",\"transaction_id\":\"453\"}','TEST17547565735687',NULL),(482,'info','Income processed successfully','2025-08-09 16:22:53','pv_processing','{\"user_id\":\"TEST17547565735687\",\"processing_period\":\"2025-08-22\",\"processing_type\":\"daily\",\"total_left_pv\":1000599.99,\"total_right_pv\":80,\"matched_pv\":80,\"gross_income\":8,\"net_income\":6.8,\"capping_applied\":0,\"deductions\":1.2000000000000002,\"carry_forward_left\":1000519.99,\"carry_forward_right\":0,\"service_charge\":0.8,\"tds_amount\":0.4}','TEST17547565735687','2025-08-22');
/*!40000 ALTER TABLE `system_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_achievements`
--

DROP TABLE IF EXISTS `user_achievements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_achievements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `achievement_type` enum('level','award') NOT NULL,
  `previous_value` varchar(100) DEFAULT NULL,
  `new_value` varchar(100) NOT NULL,
  `assigned_by` int(11) NOT NULL,
  `admin_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `assigned_by` (`assigned_by`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_achievement_type` (`achievement_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `user_achievements_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `user_achievements_ibfk_2` FOREIGN KEY (`assigned_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_achievements`
--

LOCK TABLES `user_achievements` WRITE;
/*!40000 ALTER TABLE `user_achievements` DISABLE KEYS */;
INSERT INTO `user_achievements` VALUES (1,'SP20257568','award',NULL,'Rising Star',1,'','2025-07-31 08:47:20'),(2,'SP000001','level','Beginner','Intermediate',1,'Test level assignment','2025-07-31 08:49:00'),(3,'SP000001','award',NULL,'Top Performer',1,'Test award assignment','2025-07-31 08:49:00');
/*!40000 ALTER TABLE `user_achievements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `address` text DEFAULT NULL,
  `sponsor_id` varchar(20) DEFAULT NULL,
  `franchise_id` int(11) DEFAULT NULL,
  `placement_side` enum('left','right') DEFAULT NULL,
  `self_pv` decimal(10,2) DEFAULT 0.00,
  `upline_pv` decimal(10,2) DEFAULT 0.00,
  `user_level` enum('Beginner','Intermediate','Expert') DEFAULT 'Beginner',
  `current_award` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `registration_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `email` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `username` (`username`),
  KEY `franchise_id` (`franchise_id`),
  KEY `idx_sponsor` (`sponsor_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_level` (`user_level`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`franchise_id`) REFERENCES `franchise` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'SP000001','master','master123','Master User','+91-9999999999','Master Address',NULL,NULL,NULL,0.00,0.00,'Intermediate','Top Performer','active','2025-07-28 10:42:44','2025-07-28 10:42:44','2025-07-31 08:49:00',NULL),(2,'SP20257568','abhisheksharma276','123456','Abhishek Sharma','7041101901','tHIS IS TEST ADDRESS','SP000001',1,'left',0.00,0.00,'Beginner','Rising Star','active','2025-07-28 10:44:30','2025-07-28 10:44:30','2025-07-31 08:47:20',NULL),(4,'SP20256192','sanjaysharma480','123456','sanjay sharma','7041101901','sdjfnjadsb','SP000001',1,'right',0.00,0.00,'Beginner',NULL,'active','2025-07-28 10:52:36','2025-07-28 10:52:36','2025-07-28 10:52:36','<EMAIL>'),(9,'TEST17547560706505','test_user_1754756070','$2y$10$gpddZ6o9OjIljp2DOD12SeieemrtZy1bVwOdzg9aU7fXtSupNntJK','Test User','+1234567890',NULL,NULL,NULL,NULL,0.00,0.00,'Beginner',NULL,'active','2025-08-09 16:14:30','2025-08-09 16:14:30','2025-08-09 16:14:30','<EMAIL>');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wallet`
--

DROP TABLE IF EXISTS `wallet`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `balance` decimal(12,2) DEFAULT 0.00,
  `total_earned` decimal(12,2) DEFAULT 0.00,
  `total_withdrawn` decimal(12,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_wallet` (`user_id`),
  CONSTRAINT `wallet_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wallet`
--

LOCK TABLES `wallet` WRITE;
/*!40000 ALTER TABLE `wallet` DISABLE KEYS */;
INSERT INTO `wallet` VALUES (1,'SP000001',27.12,27.12,0.00,'2025-07-28 10:42:44','2025-07-28 11:02:52'),(2,'SP20257568',9.27,9.27,0.00,'2025-07-28 10:44:30','2025-07-28 11:02:59'),(3,'SP20256192',15.05,15.05,0.00,'2025-07-28 10:52:36','2025-07-28 11:02:59');
/*!40000 ALTER TABLE `wallet` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wallet_transactions`
--

DROP TABLE IF EXISTS `wallet_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `transaction_type` enum('credit','debit') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `reference_type` enum('pv_matching','withdrawal','bonus','manual') NOT NULL,
  `reference_id` varchar(50) DEFAULT NULL,
  `balance_before` decimal(12,2) NOT NULL,
  `balance_after` decimal(12,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_type` (`user_id`,`transaction_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `wallet_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wallet_transactions`
--

LOCK TABLES `wallet_transactions` WRITE;
/*!40000 ALTER TABLE `wallet_transactions` DISABLE KEYS */;
INSERT INTO `wallet_transactions` VALUES (1,'SP000001','credit',27.12,'Weekly PV Matching Income for week 2025-07-28 to 2025-08-03 (Net after deductions)','pv_matching',NULL,0.00,27.12,'2025-07-28 11:02:52'),(2,'SP20257568','credit',9.27,'Weekly PV Matching Income for week 2025-07-28 to 2025-08-03 (Net after deductions)','pv_matching',NULL,0.00,9.27,'2025-07-28 11:02:59'),(3,'SP20256192','credit',15.05,'Weekly PV Matching Income for week 2025-07-28 to 2025-08-03 (Net after deductions)','pv_matching',NULL,0.00,15.05,'2025-07-28 11:02:59');
/*!40000 ALTER TABLE `wallet_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `weekly_income_logs`
--

DROP TABLE IF EXISTS `weekly_income_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `weekly_income_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `week_start_date` date NOT NULL,
  `week_end_date` date NOT NULL,
  `left_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `right_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `matched_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `gross_income_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `service_charge` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tds_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `income_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `weekly_capping_applied` decimal(10,2) DEFAULT 0.00,
  `carry_forward_left` decimal(10,2) DEFAULT 0.00,
  `carry_forward_right` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_week` (`user_id`,`week_start_date`),
  KEY `idx_user_week` (`user_id`,`week_start_date`),
  KEY `idx_week_start_date` (`week_start_date`),
  KEY `idx_income_amount` (`income_amount`),
  CONSTRAINT `weekly_income_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `weekly_income_logs`
--

LOCK TABLES `weekly_income_logs` WRITE;
/*!40000 ALTER TABLE `weekly_income_logs` DISABLE KEYS */;
INSERT INTO `weekly_income_logs` VALUES (1,'SP000001','2025-07-21','2025-07-27',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:30'),(2,'SP20257568','2025-07-21','2025-07-27',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:30'),(3,'SP20256192','2025-07-21','2025-07-27',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:30'),(4,'SP000001','2025-07-07','2025-07-13',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:49'),(5,'SP20257568','2025-07-07','2025-07-13',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:49'),(6,'SP20256192','2025-07-07','2025-07-13',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:49'),(10,'SP000001','2025-07-28','2025-08-03',319.00,403.00,319.00,31.90,3.19,1.60,27.12,0.00,0.00,84.00,'2025-07-28 11:02:52'),(11,'SP20257568','2025-07-28','2025-08-03',310.00,109.00,109.00,10.90,1.09,0.55,9.27,0.00,201.00,0.00,'2025-07-28 11:02:59'),(12,'SP20256192','2025-07-28','2025-08-03',177.00,319.00,177.00,17.70,1.77,0.89,15.05,0.00,0.00,142.00,'2025-07-28 11:02:59'),(13,'SP000001','2025-08-04','2025-08-10',0.00,168.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,168.00,'2025-07-28 11:03:41'),(14,'SP20257568','2025-08-04','2025-08-10',402.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,402.00,0.00,'2025-07-28 11:03:41'),(15,'SP20256192','2025-08-04','2025-08-10',0.00,284.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,284.00,'2025-07-28 11:03:41');
/*!40000 ALTER TABLE `weekly_income_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `weekly_income_reports`
--

DROP TABLE IF EXISTS `weekly_income_reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `weekly_income_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `week_start_date` date NOT NULL,
  `week_end_date` date NOT NULL,
  `total_users_earned` int(11) NOT NULL DEFAULT 0,
  `total_gross_income` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_service_charge` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_tds_amount` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_income_distributed` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_capping_applied` decimal(12,2) DEFAULT 0.00,
  `report_status` enum('processing','generated','sent','failed') DEFAULT 'processing',
  `report_generated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_week` (`week_start_date`),
  KEY `idx_week_start_date` (`week_start_date`),
  KEY `idx_report_status` (`report_status`),
  KEY `idx_generated_at` (`report_generated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `weekly_income_reports`
--

LOCK TABLES `weekly_income_reports` WRITE;
/*!40000 ALTER TABLE `weekly_income_reports` DISABLE KEYS */;
INSERT INTO `weekly_income_reports` VALUES (1,'2025-07-21','2025-07-27',0,0.00,0.00,0.00,0.00,0.00,'generated','2025-07-28 10:58:30'),(3,'2025-07-07','2025-07-13',0,0.00,0.00,0.00,0.00,0.00,'generated','2025-07-28 11:01:22'),(8,'2025-07-28','2025-08-03',0,0.00,0.00,0.00,0.00,0.00,'generated','2025-08-08 10:28:29'),(9,'2025-08-04','2025-08-10',0,0.00,0.00,0.00,0.00,0.00,'generated','2025-07-28 11:03:41');
/*!40000 ALTER TABLE `weekly_income_reports` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `withdrawals`
--

DROP TABLE IF EXISTS `withdrawals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `withdrawals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `bank_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`bank_details`)),
  `status` enum('pending','approved','rejected','processed') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `processed_by` int(11) DEFAULT NULL,
  `requested_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `processed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_status` (`status`),
  KEY `idx_user_status` (`user_id`,`status`),
  CONSTRAINT `withdrawals_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `withdrawals_ibfk_2` FOREIGN KEY (`processed_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `withdrawals`
--

LOCK TABLES `withdrawals` WRITE;
/*!40000 ALTER TABLE `withdrawals` DISABLE KEYS */;
/*!40000 ALTER TABLE `withdrawals` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-09 21:53:07
