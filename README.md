# ShaktiPure MLM Binary Plan System - Production Optimized

A complete PHP-based Multi-Level Marketing (MLM) Binary Plan website with advanced features, optimized for production-level performance and security.

## 🚀 Production Features

### 🔐 Enhanced Security
- Comprehensive .htaccess security rules
- CSRF protection and input validation
- SQL injection prevention
- XSS protection with security headers
- Session security with HTTP-only cookies
- Rate limiting for API endpoints
- Secure file upload handling

### 🏗️ MVC Architecture
- Clean separation of concerns with Models, Views, and Controllers
- Base classes for consistent functionality
- Proper dependency injection
- Centralized error handling

### 📊 Performance Optimizations
- Database indexing on frequently queried columns
- Query optimization and caching
- Batch processing for large operations
- Memory-optimized weekly matching cron job
- Compressed assets and browser caching
- Connection pooling for database operations

### 🧬 MLM Binary Tree Logic
- Optimized binary tree structure (Left/Right system)
- Sponsor ID based registration with validation
- Interactive visual binary tree representation
- Automatic placement system with conflict resolution
- Real-time PV propagation

### 💸 Advanced PV (Point Value) System
- 1 PV = ₹0.10 conversion rate (configurable)
- Permanent PV history with detailed tracking
- Optimized matching PV logic with carry forward
- Weekly capping: Max ₹130,000 per user
- Automated income calculation and distribution
- Comprehensive payout logging

### 👨‍💼 Enhanced Admin Panel
- Advanced user management with bulk operations
- Real-time PV overview and analytics dashboard
- Product management with image upload and PV configuration
- Automated withdrawal approval system with notifications
- Comprehensive franchise management and monitoring
- System health monitoring and performance metrics
- Weekly income report automation with email notifications

### 🏪 Advanced Franchise Panel
- Streamlined user registration with validation
- Product assignment workflow with admin approval
- Real-time commission tracking and analytics
- Franchise-specific reporting and KPIs
- Secure access controls with role-based permissions
- Billing and invoice generation

### 💰 Enhanced Wallet & Withdrawal System
- Secure digital wallet with transaction history
- Automated income credit from optimized PV matching
- Flexible withdrawal requests with configurable minimums
- Multi-level approval workflow with audit trails
- Real-time balance updates and notifications
- Comprehensive transaction logging and reporting

### 🛍 Advanced Product System
- Product catalog with image management and search
- Dynamic PV values with real-time updates
- Secure Razorpay payment integration (test/live modes)
- Automated product assignment with approval workflow
- Comprehensive purchase history and analytics
- Inventory tracking and low-stock alerts

## 🚀 Installation & Deployment

### Development Environment

1. **Clone Repository**
   ```bash
   git clone https://github.com/yourusername/shaktipure.git
   cd shaktipure
   ```

2. **Setup Database**
   ```bash
   php setup.php
   ```

3. **Configure Development Environment**
   - Edit `config/database.php` with your database credentials
   - Update Razorpay test keys for payment integration
   - Ensure ENVIRONMENT is set to 'development' in config

4. **Run Database Optimization**
   ```bash
   php optimize_database.php
   ```

### Production Deployment

1. **Server Requirements**
   - PHP 7.4+ with required extensions (PDO, MySQL, GD, etc.)
   - MySQL 5.7+ or MariaDB 10.3+
   - Apache with mod_rewrite or Nginx
   - 512MB+ RAM recommended for weekly cron jobs

2. **Production Configuration**
   - Copy `config/production.php.example` to `config/production.php`
   - Update production settings with your server details
   - Set ENVIRONMENT to 'production' in config
   - Configure secure database credentials

3. **Security Setup**
   - Ensure .htaccess rules are applied
   - Set proper file permissions (755 for directories, 644 for files)
   - Configure SSL certificate for HTTPS
   - Set up proper firewall rules

4. **Cron Job Setup**
   ```bash
   # Weekly PV matching (every Friday at 11:59 PM)
   59 23 * * 5 php /path/to/shaktipure/cron/optimized-weekly-matching.php >> /path/to/shaktipure/logs/cron.log 2>&1

   # Daily system maintenance (every day at 2 AM)
   0 2 * * * php /path/to/shaktipure/cron/daily-maintenance.php >> /path/to/shaktipure/logs/maintenance.log 2>&1
   ```

5. **Performance Monitoring**
   - Set up monitoring for `/health-check.php` endpoint
   - Configure alerts for system issues
   - Monitor server resources and database performance

3. **Default Admin Credentials**
   - Username: `admin`
   - Password: `admin123`
   - **Important**: Change password after first login

## 📁 Project Structure (Optimized MVC Architecture)

```
shaktipure/
├── admin/                  # Admin panel files
├── franchise/              # Franchise panel files
├── user/                   # User dashboard files
├── config/                 # Configuration files
│   ├── database.php        # Database configuration
│   ├── Connection.php      # Database connection class
│   ├── config.php          # Application configuration
│   └── production.php      # Production environment settings
├── controllers/            # Controller classes
│   ├── BaseController.php  # Base controller with common functionality
│   ├── AdminController.php # Admin-specific controllers
│   ├── UserController.php  # User-specific controllers
│   └── ...                 # Other controller classes
├── models/                 # Model classes
│   ├── BaseModel.php       # Base model with common functionality
│   ├── User.php            # User model
│   ├── Product.php         # Product model
│   └── ...                 # Other model classes
├── views/                  # View templates
│   ├── layouts/            # Layout templates
│   ├── admin/              # Admin view templates
│   ├── user/               # User view templates
│   └── ...                 # Other view templates
├── includes/               # Common includes
│   ├── functions.php       # Common functions
│   ├── session.php         # Session management
│   ├── Logger.php          # Centralized logging system
│   └── ...                 # Other utility classes
├── assets/                 # Static assets
│   ├── css/                # CSS files
│   ├── js/                 # JavaScript files
│   └── images/             # Image files
├── uploads/                # File uploads
│   └── products/           # Product images
├── logs/                   # Log files
│   ├── error.log           # Error logs
│   ├── payout.log          # Payout logs
│   └── ...                 # Other log files
├── cron/                   # Cron job scripts
│   ├── optimized-weekly-matching.php # Optimized weekly matching
│   └── daily-maintenance.php         # Daily maintenance tasks
├── .htaccess               # Apache configuration for security/performance
├── setup.php               # Database setup script
├── optimize_database.php   # Database optimization script
├── health-check.php        # System health monitoring endpoint
├── error.php               # Custom error page
├── index.php               # Main landing page
└── README.md               # This file
```

## 🗄️ Database Schema (Optimized with Indexes)

### Core Tables
- `admin` - Admin users with role-based permissions
- `franchise` - Franchise users with commission configuration
- `users` - Regular users with sponsor relationships
- `binary_tree` - Optimized binary tree structure with indexing
- `products` - Product catalog with PV values and images

### Transaction Tables
- `pv_transactions` - PV transaction history with detailed tracking
- `wallet` - User wallet balances with audit trails
- `wallet_transactions` - Detailed wallet transaction history
- `withdrawals` - Withdrawal requests with approval workflow
- `purchase_orders` - Product purchase orders with payment tracking

### Reporting Tables
- `income_logs` - Daily PV matching income logs
- `weekly_income_logs` - Weekly PV matching with carry forward
- `weekly_income_reports` - Aggregated weekly income reports

### System Tables
- `login_logs` - User login history with security tracking
- `system_logs` - System events and error logging
- `config` - System configuration with environment overrides

## ⚙️ Configuration

### Environment Settings
- Development: Local testing with detailed error reporting
- Production: Optimized for performance and security

### Database Settings (Development)
- Host: localhost
- Database: shaktipure_mlm
- Default user: root (no password)

### Database Settings (Production)
- Host: Configured in production.php
- Database: shaktipure_mlm_prod
- Secure user with limited permissions
- Connection pooling for performance

### MLM Settings
- PV Rate: ₹0.10 per PV (configurable)
- Weekly Capping: ₹130,000 (configurable)
- Minimum Withdrawal: ₹500 (configurable)
- Commission Rates: Configurable per franchise

### Payment Integration
- Razorpay integration for product purchases
- Test mode enabled by default

## Security Features

- Password hashing with bcrypt
- Session timeout management
- CSRF token protection
- SQL injection prevention with PDO
- Input sanitization
- User activity logging

## Usage

1. **Admin Functions**
   - Create and manage franchises
   - Add products with PV values
   - Monitor user activities
   - Approve withdrawals
   - View system analytics

2. **Franchise Functions**
   - Register users under franchise
   - Assign products to users
   - View commission earnings
   - Manage franchise users

3. **User Functions**
   - View binary tree structure
   - Check PV status (Left/Right/Matching)
   - Monitor wallet balance
   - Request withdrawals
   - Purchase products
   - Share referral links

## Development

- PHP 7.4+ required
- MySQL 5.7+ required
- Bootstrap 5 for frontend
- Font Awesome for icons
- PDO for database operations

## Support

For technical support, contact: <EMAIL>
