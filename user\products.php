<?php
/**
 * User Products Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

// Get available products
$db = Database::getInstance();
$productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' ORDER BY name");
$productsStmt->execute();
$products = $productsStmt->fetchAll();

// Get user's purchase history
$ordersStmt = $db->prepare("
    SELECT po.*, p.name as product_name 
    FROM purchase_orders po 
    JOIN products p ON po.product_id = p.id 
    WHERE po.user_id = ? 
    ORDER BY po.created_at DESC 
    LIMIT 10
");
$ordersStmt->execute([$userId]);
$orders = $ordersStmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
            border-color: #0d6efd;
        }

        .product-card:hover .card-title {
            color: #0d6efd;
        }

        .product-card:active {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
            <img src="../assets/images/logo.png" alt="" style="height:50px; width:auto;"></i>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tree.php">
                            <i class="fas fa-sitemap me-1"></i>Binary Tree
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.php">
                            <i class="fas fa-shopping-cart me-1"></i>Products
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>Available Products
                        </h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <?php if (!empty($products)): ?>
            <div class="row mb-5">
                <?php foreach ($products as $product): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 product-card" onclick="viewProductDetails(<?php echo $product['id']; ?>)" style="cursor: pointer;">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                <p class="card-text text-muted mb-3">
                                    <small><i class="fas fa-tag me-1"></i>Code: <?php echo htmlspecialchars($product['product_code']); ?></small>
                                </p>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <strong>Price:</strong><br>
                                        <span class="text-success h5"><?php echo formatCurrency($product['price']); ?></span>
                                    </div>
                                    <div class="col-6">
                                        <strong>PV Value:</strong><br>
                                        <span class="text-primary h5"><?php echo formatPV($product['pv_value']); ?></span>
                                    </div>
                                </div>

                                <div class="text-center mb-3">
                                    <small class="text-primary">
                                        <i class="fas fa-mouse-pointer me-1"></i>Click to view details
                                    </small>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="event.stopPropagation(); purchaseProduct(<?php echo $product['id']; ?>)">
                                        <i class="fas fa-shopping-cart me-2"></i>Purchase Now
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="event.stopPropagation(); viewProductDetails(<?php echo $product['id']; ?>)">
                                        <i class="fas fa-eye me-2"></i>View Full Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="row mb-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                            <h5>No Products Available</h5>
                            <p class="text-muted">Products will be available soon. Please check back later.</p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Purchase History -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Purchase History</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($orders)): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Product</th>
                                            <th>Amount</th>
                                            <th>PV</th>
                                            <th>Side</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orders as $order): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($order['order_id']); ?></td>
                                                <td><?php echo htmlspecialchars($order['product_name']); ?></td>
                                                <td><?php echo formatCurrency($order['total_amount']); ?></td>
                                                <td><?php echo formatPV($order['pv_amount']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $order['placement_side'] === 'left' ? 'success' : 'warning'; ?>">
                                                        <?php echo ucfirst($order['placement_side']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch ($order['order_status']) {
                                                        case 'confirmed':
                                                            $statusClass = 'success';
                                                            break;
                                                        case 'pending':
                                                            $statusClass = 'warning';
                                                            break;
                                                        case 'cancelled':
                                                            $statusClass = 'danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                                        <?php echo ucfirst($order['order_status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($order['created_at'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-shopping-bag fa-3x mb-3"></i>
                                <h5>No purchases yet</h5>
                                <p>Your purchase history will appear here</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchase Modal -->
    <div class="modal fade" id="purchaseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Purchase Product</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="productDetails"></div>
                    <form id="purchaseForm">
                        <input type="hidden" id="productId" name="product_id">
                        
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" value="1" min="1" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">PV Placement Side</label>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="placement_side" id="leftSide" value="left" required>
                                        <label class="form-check-label" for="leftSide">
                                            <i class="fas fa-arrow-left me-2"></i>Left Side
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="placement_side" id="rightSide" value="right" required>
                                        <label class="form-check-label" for="rightSide">
                                            <i class="fas fa-arrow-right me-2"></i>Right Side
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> This is a demo version. Razorpay integration will be added for live payments.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="processPurchase()">
                        <i class="fas fa-credit-card me-2"></i>Proceed to Payment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const products = <?php echo json_encode($products); ?>;
        
        function purchaseProduct(productId) {
            const product = products.find(p => p.id == productId);
            if (!product) return;
            
            document.getElementById('productId').value = productId;
            
            const productDetails = `
                <div class="card mb-3">
                    <div class="card-body">
                        <h6>${product.name}</h6>
                        <p class="text-muted">${product.description}</p>
                        <div class="row">
                            <div class="col-6">
                                <strong>Price:</strong> ₹${parseFloat(product.price).toFixed(2)}
                            </div>
                            <div class="col-6">
                                <strong>PV:</strong> ${parseFloat(product.pv_value).toFixed(2)} PV
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('productDetails').innerHTML = productDetails;
            
            const modal = new bootstrap.Modal(document.getElementById('purchaseModal'));
            modal.show();
        }
        
        function processPurchase() {
            alert('Razorpay integration will be implemented for live payments. This is a demo version.');
            // In the real implementation, this would integrate with Razorpay
        }

        function viewProductDetails(productId) {
            // Redirect to product detail page
            window.location.href = '../product-detail.php?id=' + productId;
        }
    </script>
</body>
</html>
