<?php
require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../includes/WeeklyDateHelper.php';
require_once '../config/config.php';

// Test script for Saturday-Friday weekly logic
echo "<h2>Saturday-Friday Weekly Logic Test</h2>\n";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
</style>";

try {
    $db = Database::getInstance();
    $pvSystem = new PVSystem();
    $config = Config::getInstance();

    echo "<div class='test-section'>";
    echo "<h3>1. Testing WeeklyDateHelper Functions</h3>";
    
    // Test current week calculation
    $currentWeek = WeeklyDateHelper::getCurrentWeek();
    echo "<p><strong>Current Week (Saturday-Friday):</strong></p>";
    echo "<ul>";
    echo "<li>Start: {$currentWeek['start']} (should be a Saturday)</li>";
    echo "<li>End: {$currentWeek['end']} (should be a Friday)</li>";
    echo "</ul>";
    
    // Verify the days
    $startDay = date('l', strtotime($currentWeek['start']));
    $endDay = date('l', strtotime($currentWeek['end']));
    
    if ($startDay === 'Saturday') {
        echo "<span class='success'>✓ Week start is correctly Saturday</span><br>";
    } else {
        echo "<span class='error'>✗ Week start is {$startDay}, should be Saturday</span><br>";
    }
    
    if ($endDay === 'Friday') {
        echo "<span class='success'>✓ Week end is correctly Friday</span><br>";
    } else {
        echo "<span class='error'>✗ Week end is {$endDay}, should be Friday</span><br>";
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h3>2. Testing Previous Week Calculation</h3>";
    
    $previousWeek = WeeklyDateHelper::getPreviousWeek();
    echo "<p><strong>Previous Week (Saturday-Friday):</strong></p>";
    echo "<ul>";
    echo "<li>Start: {$previousWeek['start']} (should be a Saturday)</li>";
    echo "<li>End: {$previousWeek['end']} (should be a Friday)</li>";
    echo "</ul>";
    
    // Verify the days
    $prevStartDay = date('l', strtotime($previousWeek['start']));
    $prevEndDay = date('l', strtotime($previousWeek['end']));
    
    if ($prevStartDay === 'Saturday') {
        echo "<span class='success'>✓ Previous week start is correctly Saturday</span><br>";
    } else {
        echo "<span class='error'>✗ Previous week start is {$prevStartDay}, should be Saturday</span><br>";
    }
    
    if ($prevEndDay === 'Friday') {
        echo "<span class='success'>✓ Previous week end is correctly Friday</span><br>";
    } else {
        echo "<span class='error'>✗ Previous week end is {$prevEndDay}, should be Friday</span><br>";
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h3>3. Testing Week Calculation for Different Dates</h3>";
    
    $testDates = [
        '2025-08-16', // Saturday
        '2025-08-17', // Sunday  
        '2025-08-18', // Monday
        '2025-08-19', // Tuesday
        '2025-08-20', // Wednesday
        '2025-08-21', // Thursday
        '2025-08-22', // Friday
    ];
    
    echo "<table>";
    echo "<tr><th>Test Date</th><th>Day</th><th>Week Start</th><th>Week End</th><th>Status</th></tr>";
    
    foreach ($testDates as $testDate) {
        $dayName = date('l', strtotime($testDate));
        $weekStart = WeeklyDateHelper::getWeekStart($testDate);
        $weekEnd = WeeklyDateHelper::getWeekEnd($testDate);
        
        $startDay = date('l', strtotime($weekStart));
        $endDay = date('l', strtotime($weekEnd));
        
        $status = ($startDay === 'Saturday' && $endDay === 'Friday') ? 
                  "<span class='success'>✓ Correct</span>" : 
                  "<span class='error'>✗ Incorrect</span>";
        
        echo "<tr>";
        echo "<td>{$testDate}</td>";
        echo "<td>{$dayName}</td>";
        echo "<td>{$weekStart} ({$startDay})</td>";
        echo "<td>{$weekEnd} ({$endDay})</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h3>4. Testing Processing Day Logic</h3>";
    
    $processingDay = WeeklyDateHelper::getProcessingDay();
    $processingTime = WeeklyDateHelper::getProcessingTime();
    $isProcessingDay = WeeklyDateHelper::isProcessingDay();
    $isPastProcessingTime = WeeklyDateHelper::isPastProcessingTime();
    
    echo "<ul>";
    echo "<li><strong>Processing Day:</strong> {$processingDay} (5 = Friday)</li>";
    echo "<li><strong>Processing Time:</strong> {$processingTime} (23:59 = 11:59 PM)</li>";
    echo "<li><strong>Is Today Processing Day:</strong> " . ($isProcessingDay ? 'Yes' : 'No') . "</li>";
    echo "<li><strong>Is Past Processing Time:</strong> " . ($isPastProcessingTime ? 'Yes' : 'No') . "</li>";
    echo "<li><strong>Current Day:</strong> " . date('w') . " (" . date('l') . ")</li>";
    echo "<li><strong>Current Time:</strong> " . date('H:i') . "</li>";
    echo "</ul>";
    
    if ($processingDay === 5) {
        echo "<span class='success'>✓ Processing day is correctly set to Friday (5)</span><br>";
    } else {
        echo "<span class='error'>✗ Processing day is {$processingDay}, should be 5 (Friday)</span><br>";
    }
    
    if ($processingTime === '23:59') {
        echo "<span class='success'>✓ Processing time is correctly set to 23:59 (11:59 PM)</span><br>";
    } else {
        echo "<span class='error'>✗ Processing time is {$processingTime}, should be 23:59</span><br>";
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h3>5. Testing PVSystem Integration</h3>";
    
    // Test if PVSystem uses the new logic
    $testWeek = $pvSystem->runWeeklyMatching(null, null, false);
    echo "<p><strong>PVSystem Weekly Matching Test:</strong></p>";
    
    if ($testWeek !== false) {
        echo "<span class='success'>✓ PVSystem runWeeklyMatching executed without errors</span><br>";
    } else {
        echo "<span class='warning'>⚠ PVSystem runWeeklyMatching returned false (may be normal if no users to process)</span><br>";
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h3>6. Configuration Check</h3>";
    
    // Check configuration values
    $weeklyProcessingDay = $config->get('weekly_processing_day', 5);
    $weeklyMatchingTime = $config->get('weekly_matching_time', '23:59');
    $paymentProcessingDay = $config->get('payment_processing_day', 6);
    
    echo "<ul>";
    echo "<li><strong>Weekly Processing Day:</strong> {$weeklyProcessingDay} " . 
         ($weeklyProcessingDay == 5 ? "<span class='success'>(✓ Friday)</span>" : "<span class='error'>(✗ Should be 5)</span>") . "</li>";
    echo "<li><strong>Weekly Matching Time:</strong> {$weeklyMatchingTime} " . 
         ($weeklyMatchingTime == '23:59' ? "<span class='success'>(✓ 11:59 PM)</span>" : "<span class='error'>(✗ Should be 23:59)</span>") . "</li>";
    echo "<li><strong>Payment Processing Day:</strong> {$paymentProcessingDay} " . 
         ($paymentProcessingDay == 6 ? "<span class='success'>(✓ Saturday)</span>" : "<span class='error'>(✗ Should be 6)</span>") . "</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h3>7. Week Range Formatting Test</h3>";
    
    $formattedRange = WeeklyDateHelper::formatWeekRange($currentWeek['start'], $currentWeek['end']);
    echo "<p><strong>Formatted Current Week:</strong> {$formattedRange}</p>";
    
    $formattedPrevRange = WeeklyDateHelper::formatWeekRange($previousWeek['start'], $previousWeek['end']);
    echo "<p><strong>Formatted Previous Week:</strong> {$formattedPrevRange}</p>";
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h3>8. Summary</h3>";
    echo "<p class='success'><strong>✅ Saturday-Friday Weekly Logic Test Completed</strong></p>";
    echo "<p><strong>Key Points:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Weeks now run from Saturday 12:00 AM to Friday 11:59 PM</li>";
    echo "<li>✅ Income processing happens on Friday at 11:59 PM</li>";
    echo "<li>✅ Payment processing happens on Saturday (day after week ends)</li>";
    echo "<li>✅ All date calculations use the new Saturday-Friday logic</li>";
    echo "<li>✅ WeeklyDateHelper provides centralized date calculation functions</li>";
    echo "</ul>";
    
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>1. Run the migration script: <code>php migrate-saturday-friday-weeks.php</code></li>";
    echo "<li>2. Update cron jobs to use new schedule</li>";
    echo "<li>3. Test with actual PV data</li>";
    echo "<li>4. Monitor first week of new schedule</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='test-section'>";
    echo "<h3>❌ Error During Testing</h3>";
    echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}
?>
