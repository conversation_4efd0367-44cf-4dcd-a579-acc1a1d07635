<?php
/**
 * Fix PV Usage Tracking Table Structure
 * Ensures the pv_usage_tracking table has the correct structure for the new report system
 */

require_once '../config/Connection.php';

echo "<!DOCTYPE html>\n";
echo "<html><head><title>Fix PV Usage Tracking</title></head><body>\n";
echo "<h1>🔧 Fix PV Usage Tracking Table Structure</h1>\n";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. Checking Current Table Structure</h2>\n";
    
    // Check if table exists
    $tableExistsStmt = $db->query("SHOW TABLES LIKE 'pv_usage_tracking'");
    $tableExists = $tableExistsStmt->fetch();
    
    if (!$tableExists) {
        echo "<p style='color: orange;'>⚠️ Table pv_usage_tracking does not exist. Creating it...</p>\n";
        
        $createTableSQL = "
        CREATE TABLE pv_usage_tracking (
            id INT PRIMARY KEY AUTO_INCREMENT,
            pv_transaction_id INT NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            side ENUM('left', 'right', 'self') NOT NULL,
            original_amount DECIMAL(10,2) NOT NULL,
            used_amount DECIMAL(10,2) DEFAULT 0.00,
            remaining_amount DECIMAL(10,2) NOT NULL,
            week_used DATE NULL,
            processing_period VARCHAR(20) NULL,
            status ENUM('available', 'partially_used', 'fully_used') DEFAULT 'available',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (pv_transaction_id) REFERENCES pv_transactions(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_remaining (user_id, remaining_amount),
            INDEX idx_side (side),
            INDEX idx_week_used (week_used),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        )";
        
        $db->exec($createTableSQL);
        echo "<p style='color: green;'>✅ Table pv_usage_tracking created successfully!</p>\n";
    } else {
        echo "<p style='color: green;'>✅ Table pv_usage_tracking exists.</p>\n";
        
        // Check if status column exists
        $columnsStmt = $db->query("DESCRIBE pv_usage_tracking");
        $columns = $columnsStmt->fetchAll();
        
        $hasStatusColumn = false;
        $hasProcessingPeriodColumn = false;
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'status') {
                $hasStatusColumn = true;
            }
            if ($column['Field'] === 'processing_period') {
                $hasProcessingPeriodColumn = true;
            }
        }
        
        // Add missing columns
        if (!$hasStatusColumn) {
            echo "<p style='color: orange;'>⚠️ Adding missing 'status' column...</p>\n";
            $db->exec("ALTER TABLE pv_usage_tracking ADD COLUMN status ENUM('available', 'partially_used', 'fully_used') DEFAULT 'available' AFTER remaining_amount");
            echo "<p style='color: green;'>✅ Status column added!</p>\n";
        }
        
        if (!$hasProcessingPeriodColumn) {
            echo "<p style='color: orange;'>⚠️ Adding missing 'processing_period' column...</p>\n";
            $db->exec("ALTER TABLE pv_usage_tracking ADD COLUMN processing_period VARCHAR(20) NULL AFTER week_used");
            echo "<p style='color: green;'>✅ Processing period column added!</p>\n";
        }
        
        // Add missing indexes
        try {
            $db->exec("CREATE INDEX IF NOT EXISTS idx_status ON pv_usage_tracking (status)");
            echo "<p style='color: green;'>✅ Status index added!</p>\n";
        } catch (Exception $e) {
            // Index might already exist
        }
    }
    
    echo "<h2>2. Initializing Existing PV Transactions</h2>\n";
    
    // Check if we need to initialize existing PV transactions
    $trackingCountStmt = $db->query("SELECT COUNT(*) as count FROM pv_usage_tracking");
    $trackingCount = $trackingCountStmt->fetch()['count'];
    
    $pvCountStmt = $db->query("SELECT COUNT(*) as count FROM pv_transactions WHERE processing_status = 'pending'");
    $pvCount = $pvCountStmt->fetch()['count'];
    
    echo "<p>PV Usage Tracking records: {$trackingCount}</p>\n";
    echo "<p>Pending PV Transactions: {$pvCount}</p>\n";
    
    if ($trackingCount == 0 && $pvCount > 0) {
        echo "<p style='color: orange;'>⚠️ Initializing PV usage tracking for existing transactions...</p>\n";
        
        $existingPVStmt = $db->query("
            SELECT id, user_id, side, pv_amount, created_at 
            FROM pv_transactions 
            WHERE processing_status = 'pending'
            ORDER BY created_at ASC
        ");
        $existingPV = $existingPVStmt->fetchAll();
        
        $insertStmt = $db->prepare("
            INSERT INTO pv_usage_tracking 
            (pv_transaction_id, user_id, side, original_amount, remaining_amount, status, created_at) 
            VALUES (?, ?, ?, ?, ?, 'available', ?)
        ");
        
        $initialized = 0;
        foreach ($existingPV as $pv) {
            try {
                $insertStmt->execute([
                    $pv['id'],
                    $pv['user_id'],
                    $pv['side'],
                    $pv['pv_amount'],
                    $pv['pv_amount'], // Initially, remaining = original
                    $pv['created_at']
                ]);
                $initialized++;
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Warning: Could not initialize PV transaction ID {$pv['id']}: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
        
        echo "<p style='color: green;'>✅ Initialized {$initialized} PV transactions in tracking table.</p>\n";
    } else {
        echo "<p style='color: green;'>✅ PV usage tracking is already initialized.</p>\n";
    }
    
    echo "<h2>3. Summary</h2>\n";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3>✅ PV Usage Tracking Fixed Successfully!</h3>\n";
    echo "<p>The new generate report system is now ready to use with:</p>\n";
    echo "<ul>\n";
    echo "<li>Proper PV usage tracking to prevent double-counting</li>\n";
    echo "<li>FIFO processing of PV transactions</li>\n";
    echo "<li>Carry-forward logic for unmatched PV</li>\n";
    echo "<li>Complete audit trail for all income calculations</li>\n";
    echo "</ul>\n";
    echo "<p><strong>You can now generate reports from the admin panel!</strong></p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Stack trace:</p><pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "</body></html>\n";
?>
