<?php
/**
 * Add Test PV Data
 * Creates sample PV transactions for testing the binary system
 */

require_once '../config/Connection.php';
require_once '../config/config.php';
require_once '../includes/NewBinaryPVSystem.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_id'])) {
    die("Access denied. Admin login required.");
}

$db = Database::getInstance();
$binaryPVSystem = new NewBinaryPVSystem();

$message = '';
$messageType = '';

// Handle form submission
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_test_data') {
        try {
            $db->beginTransaction();
            
            // Get some active users
            $users = $db->query("SELECT user_id FROM users WHERE status = 'active' LIMIT 10")->fetchAll();
            
            if (empty($users)) {
                throw new Exception("No active users found. Please create some users first.");
            }
            
            $addedTransactions = 0;
            
            // Add random PV transactions for testing
            foreach ($users as $user) {
                $userId = $user['user_id'];
                
                // Add random left PV (50-500)
                $leftPV = rand(50, 500);
                $binaryPVSystem->addPV($userId, $leftPV, 'left', 'manual', null, 'TEST_' . uniqid(), 'Test PV for binary matching', 'admin', $_SESSION['admin_id']);
                $addedTransactions++;
                
                // Add random right PV (30-400)
                $rightPV = rand(30, 400);
                $binaryPVSystem->addPV($userId, $rightPV, 'right', 'manual', null, 'TEST_' . uniqid(), 'Test PV for binary matching', 'admin', $_SESSION['admin_id']);
                $addedTransactions++;
            }
            
            $db->commit();
            
            $message = "Successfully added $addedTransactions test PV transactions for " . count($users) . " users.";
            $messageType = 'success';
            
        } catch (Exception $e) {
            if ($db->inTransaction()) {
                $db->rollback();
            }
            $message = "Error adding test data: " . $e->getMessage();
            $messageType = 'danger';
        }
    }
    
    if ($action === 'clear_test_data') {
        try {
            $result = $db->exec("DELETE FROM pv_transactions WHERE description LIKE 'Test PV for binary matching%'");
            $message = "Cleared $result test PV transactions.";
            $messageType = 'success';
        } catch (Exception $e) {
            $message = "Error clearing test data: " . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get current statistics
$totalUsers = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'")->fetch()['count'];
$pendingPVs = $db->query("SELECT COUNT(*) as count, SUM(pv_amount) as total FROM pv_transactions WHERE processing_status = 'pending'")->fetch();
$testPVs = $db->query("SELECT COUNT(*) as count, SUM(pv_amount) as total FROM pv_transactions WHERE description LIKE 'Test PV for binary matching%'")->fetch();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Test PV Data - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-database"></i> Add Test PV Data</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Current Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5>Active Users</h5>
                                        <h3><?php echo number_format($totalUsers); ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5>Pending PVs</h5>
                                        <h3><?php echo number_format($pendingPVs['count']); ?></h3>
                                        <small>Total: <?php echo number_format($pendingPVs['total'], 2); ?> PV</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body text-center">
                                        <h5>Test PVs</h5>
                                        <h3><?php echo number_format($testPVs['count']); ?></h3>
                                        <small>Total: <?php echo number_format($testPVs['total'], 2); ?> PV</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Add Test Data</h5>
                                        <p class="card-text">Add random PV transactions for testing the binary matching system.</p>
                                        <form method="POST">
                                            <input type="hidden" name="action" value="add_test_data">
                                            <button type="submit" class="btn btn-success" <?php echo ($totalUsers == 0) ? 'disabled' : ''; ?>>
                                                <i class="fas fa-plus"></i> Add Test PV Data
                                            </button>
                                        </form>
                                        <?php if ($totalUsers == 0): ?>
                                            <small class="text-muted">No active users found. Create users first.</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Clear Test Data</h5>
                                        <p class="card-text">Remove all test PV transactions from the system.</p>
                                        <form method="POST" onsubmit="return confirm('Are you sure you want to clear all test PV data?')">
                                            <input type="hidden" name="action" value="clear_test_data">
                                            <button type="submit" class="btn btn-danger" <?php echo ($testPVs['count'] == 0) ? 'disabled' : ''; ?>>
                                                <i class="fas fa-trash"></i> Clear Test Data
                                            </button>
                                        </form>
                                        <?php if ($testPVs['count'] == 0): ?>
                                            <small class="text-muted">No test data found.</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Information -->
                        <div class="alert alert-info mt-4">
                            <h6><i class="fas fa-info-circle"></i> How Test Data Works:</h6>
                            <ul class="mb-0">
                                <li>Creates random PV amounts (50-500 for left, 30-400 for right) for each active user</li>
                                <li>All test PVs are marked as 'pending' and ready for processing</li>
                                <li>Test data is clearly labeled and can be safely removed</li>
                                <li>Use this data to test the binary matching system</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="text-center mt-4">
                    <a href="test-binary-system.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-vial"></i> Test System
                    </a>
                    <a href="binary-reports.php" class="btn btn-success me-2">
                        <i class="fas fa-chart-bar"></i> Generate Report
                    </a>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
