<?php
/**
 * System Health Check
 * Monitors system health and logs issues
 */

// Prevent direct browser access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

require_once __DIR__ . '/../config/Connection.php';
require_once __DIR__ . '/../includes/PVValidationSystem.php';

try {
    $db = Database::getInstance();
    $validator = new PVValidationSystem();
    
    echo "[" . date('Y-m-d H:i:s') . "] Starting system health check...\n";
    
    // Check database connectivity
    $db->query("SELECT 1");
    echo "✓ Database connectivity OK\n";
    
    // Check for expired locks
    $stmt = $db->prepare("SELECT COUNT(*) as expired_count FROM pv_processing_locks WHERE status = 'active' AND expires_at < NOW()");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result['expired_count'] > 0) {
        echo "⚠ Found {$result['expired_count']} expired locks\n";
        // Clean up expired locks
        $db->prepare("UPDATE pv_processing_locks SET status = 'expired' WHERE status = 'active' AND expires_at < NOW()")->execute();
        echo "✓ Cleaned up expired locks\n";
    } else {
        echo "✓ No expired locks found\n";
    }
    
    // Check for recent errors
    $stmt = $db->prepare("SELECT COUNT(*) as error_count FROM system_logs WHERE log_type = 'error' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result['error_count'] > 10) {
        echo "⚠ High error rate: {$result['error_count']} errors in last hour\n";
    } else {
        echo "✓ Error rate normal: {$result['error_count']} errors in last hour\n";
    }
    
    // Check pending transactions
    $stmt = $db->prepare("SELECT COUNT(*) as pending_count FROM pv_transactions WHERE processing_status = 'pending'");
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "ℹ Pending transactions: {$result['pending_count']}\n";
    
    echo "[" . date('Y-m-d H:i:s') . "] Health check completed\n";
    
} catch (Exception $e) {
    echo "[" . date('Y-m-d H:i:s') . "] Health check failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
