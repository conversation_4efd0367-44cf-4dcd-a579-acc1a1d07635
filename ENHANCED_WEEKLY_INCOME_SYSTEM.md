# Enhanced Weekly Income System

## Overview

The Enhanced Weekly Income System is a comprehensive upgrade to the MLM platform's weekly income processing, introducing multiple reports per week, user-level duplicate prevention, enhanced reporting features, and integrated automatic payment processing.

## Key Features

### 1. Multiple Reports Per Week ✅
- **Previous Limitation**: Only one report per week was allowed
- **Enhancement**: Unlimited reports per week with unique report IDs and numbering
- **Benefits**: 
  - Flexibility for corrections and adjustments
  - Incremental processing capabilities
  - Better audit trail

**Implementation Details:**
- Each report gets a unique `report_id` in format: `WR{YYYYMMDD}_{NN}` (e.g., `WR20250816_01`)
- Sequential `report_number` field tracks report sequence (#1, #2, #3, etc.)
- Removed `UNIQUE` constraint on `week_start_date` in `weekly_income_reports` table

### 2. User-Level Duplicate Prevention ✅
- **Database Constraint**: `UNIQUE KEY unique_user_week (user_id, week_start_date, week_end_date)`
- **Application Logic**: Checks for existing user processing before creating new records
- **Benefits**:
  - Prevents accidental double payments
  - Maintains data integrity
  - Graceful handling of duplicate attempts

### 3. Enhanced Reporting Features ✅
- **Detailed Breakdown**: Gross income, service charges, TDS, net income
- **User Statistics**: Total processed, users with income, users skipped
- **Report Tracking**: Processing time, generation timestamps, status tracking
- **Multiple Report Display**: Shows all reports for a week with clear numbering

### 4. Automatic Payment System Integration ✅
- **Payment Status Tracking**: pending, processing, paid, failed
- **Batch Processing**: Organized payment batches with detailed tracking
- **Transaction Records**: Complete audit trail for all payment attempts
- **Configurable Thresholds**: Minimum payment amounts and processing schedules

## Database Schema Changes

### Enhanced weekly_income_reports Table
```sql
ALTER TABLE weekly_income_reports 
ADD COLUMN report_id VARCHAR(30) UNIQUE NOT NULL,
ADD COLUMN report_number INT NOT NULL DEFAULT 1,
ADD COLUMN total_users_processed INT NOT NULL DEFAULT 0,
ADD COLUMN total_users_skipped INT NOT NULL DEFAULT 0,
ADD COLUMN total_gross_income DECIMAL(12,2) NOT NULL DEFAULT 0.00,
ADD COLUMN total_service_charge DECIMAL(12,2) NOT NULL DEFAULT 0.00,
ADD COLUMN total_tds_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
ADD COLUMN processing_time_seconds INT DEFAULT 0,
ADD COLUMN report_file_path VARCHAR(255) NULL,
ADD COLUMN generated_by INT NULL;
```

### Enhanced weekly_income_logs Table
```sql
ALTER TABLE weekly_income_logs 
ADD COLUMN processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
ADD COLUMN processed_at TIMESTAMP NULL,
ADD COLUMN processing_time_seconds INT DEFAULT 0,
ADD COLUMN payment_status ENUM('pending', 'paid', 'failed', 'processing') DEFAULT 'pending',
ADD COLUMN payment_date TIMESTAMP NULL,
ADD COLUMN payment_reference VARCHAR(100) NULL,
ADD COLUMN payment_method ENUM('bank_transfer', 'upi', 'wallet', 'manual') DEFAULT 'bank_transfer',
ADD COLUMN payment_notes TEXT NULL,
ADD COLUMN processed_by INT NULL;
```

### New Payment Tables
```sql
-- Payment Batches
CREATE TABLE payment_batches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id VARCHAR(50) UNIQUE NOT NULL,
    week_start_date DATE NOT NULL,
    week_end_date DATE NOT NULL,
    total_payments INT NOT NULL DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    successful_payments INT NOT NULL DEFAULT 0,
    failed_payments INT NOT NULL DEFAULT 0,
    batch_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payment Transactions
CREATE TABLE payment_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    batch_id VARCHAR(50) NULL,
    income_log_id INT NOT NULL,
    user_id VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    payment_reference VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (income_log_id) REFERENCES weekly_income_logs(id) ON DELETE CASCADE
);
```

## Usage Guide

### Generating Multiple Reports
1. Navigate to **Admin Panel > Weekly Income Reports**
2. Click **Generate Report** button
3. Select week dates and click **Generate**
4. System automatically assigns next report number
5. View all reports for a week in the reports table

### Payment Processing
1. Navigate to **Admin Panel > Payment Management**
2. Configure payment settings (threshold, schedule, etc.)
3. Process payments manually or set up automatic processing
4. Monitor payment status and batch processing results

### Monitoring and Analytics
- **Weekly Income Details**: View comprehensive breakdown of each week's processing
- **Payment Statistics**: Track payment success rates and pending amounts
- **Multiple Reports View**: See all reports generated for any week
- **User-Level Tracking**: Monitor individual user processing and payment status

## Configuration Options

### Payment System Configuration
```php
// Automatic payment settings
'auto_payment_enabled' => true/false,
'auto_payment_threshold' => 500.00,  // Minimum amount for auto payment
'payment_processing_day' => 6,       // Day of week (6 = Saturday)
'payment_processing_time' => '10:00', // Processing time
'payment_batch_size' => 100,         // Payments per batch
'payment_retry_attempts' => 3        // Max retry attempts
```

## API Endpoints

### Core Processing
- `PVSystem::runWeeklyMatching($weekStart, $weekEnd, $incremental = false)`
- `PVSystem::processWeeklyPVMatching($userId, $weekStart, $weekEnd, $allowReprocessing = false)`

### Payment Processing
- `PaymentProcessor::processWeeklyPayments($weekStart, $weekEnd = null)`
- `PaymentProcessor::getWeeklyPaymentStats($weekStart)`

### Report Generation
- `PVSystem::createWeeklyReport($reportId, $weekStart, $weekEnd, $reportNumber, ...)`
- `generateReportId($weekStartDate, $reportNumber = 1)`

## Cron Jobs

### Weekly Income Processing
```bash
# Run every Monday at 9:00 AM
0 9 * * 1 /usr/bin/php /path/to/cron/weekly-matching.php
```

### Payment Processing
```bash
# Run every Saturday at 10:00 AM
0 10 * * 6 /usr/bin/php /path/to/cron/weekly-payment-processor.php
```

## Testing and Validation

### Test Script
Run `/admin/test-enhanced-weekly-system.php` to verify:
- Database structure integrity
- Multiple reports functionality
- Duplicate prevention
- Payment system integration
- Data consistency

### Manual Testing Checklist
- [ ] Generate multiple reports for same week
- [ ] Verify unique report IDs and numbering
- [ ] Test duplicate user processing prevention
- [ ] Process payments for a week
- [ ] Verify payment status updates
- [ ] Check admin interface displays

## Migration

To upgrade existing systems:
1. Run `migrate-automatic-payments.php`
2. Verify database structure with test script
3. Update admin interfaces
4. Configure payment settings
5. Test with sample data

## Security Considerations

- All payment processing includes transaction logging
- User-level duplicate prevention prevents data corruption
- Payment references are unique and trackable
- Admin authentication required for all operations
- Input validation and sanitization throughout

## Performance Optimizations

- Proper indexing on new fields
- Batch processing for payments
- Efficient queries for large datasets
- Memory management in processing loops
- Configurable batch sizes

## Support and Troubleshooting

### Common Issues
1. **Duplicate User Processing**: Check unique constraint on weekly_income_logs
2. **Payment Failures**: Review payment_transactions table for error details
3. **Missing Report Numbers**: Verify report_id generation logic
4. **Performance Issues**: Check database indexes and batch sizes

### Logs and Monitoring
- System logs in `/logs/` directory
- Payment processing logs
- Weekly income generation logs
- Error tracking and reporting

---

**Version**: 2.0.0  
**Last Updated**: 2025-08-16  
**Compatibility**: PHP 7.4+, MySQL 5.7+  
**Dependencies**: PDO, Bootstrap 5, Font Awesome 6
