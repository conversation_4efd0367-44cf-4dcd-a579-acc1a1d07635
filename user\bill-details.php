<?php
/**
 * Bill Details & Download Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

// Get order ID from URL
$orderId = $_GET['order_id'] ?? '';
$download = isset($_GET['download']);

if (!$orderId) {
    Response::redirect('purchase-bills.php');
}

// Get order details (only franchise-assigned and admin-approved products)
$db = Database::getInstance();
$orderStmt = $db->prepare("
    SELECT po.*, p.name as product_name, p.description as product_description,
           u.full_name, u.email, u.phone, u.address, u.franchise_id,
           f.full_name as franchise_name, f.franchise_code, f.email as franchise_email
    FROM purchase_orders po
    JOIN products p ON po.product_id = p.id
    JOIN users u ON po.user_id = u.user_id
    LEFT JOIN franchise f ON u.franchise_id = f.id
    WHERE po.order_id = ? AND po.user_id = ?
    AND po.payment_method = 'manual'
    AND po.payment_status = 'completed'
    AND po.order_status = 'confirmed'
");
$orderStmt->execute([$orderId, $userId]);
$orderDetails = $orderStmt->fetch();

if (!$orderDetails) {
    Response::redirect('purchase-bills.php?error=' . urlencode('Order not found'));
}

// Calculate tax (assuming 18% GST)
$taxRate = 0.18;
$subtotal = $orderDetails['total_amount'];
$taxAmount = $subtotal * $taxRate;
$totalWithTax = $subtotal + $taxAmount;

// If download is requested, set headers for PDF download
if ($download) {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="bill-' . $orderId . '.pdf"');
    // Note: For actual PDF generation, you would use a library like TCPDF or mPDF
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill <?php echo htmlspecialchars($orderId); ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .container { max-width: none !important; }
            body { background: white !important; }
        }
        .bill-header {
            border-bottom: 3px solid #007bff;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        .bill-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .bill-table th {
            background-color: #007bff;
            color: white;
        }
        .total-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- Navigation (hidden when printing) -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark no-print">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <img src="../assets/images/logo.png" alt="" style="height:50px; width:auto;">
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="purchase-bills.php">
                    <i class="fas fa-arrow-left me-1"></i>Back to Bills
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Action Buttons -->
        <div class="row no-print mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <a href="purchase-bills.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Bills
                    </a>
                    <div>
                        <button onclick="window.print()" class="btn btn-primary me-2">
                            <i class="fas fa-print me-1"></i>Print Bill
                        </button>
                        <a href="?order_id=<?php echo urlencode($orderId); ?>&download=1" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bill Content -->
        <div class="row">
            <div class="col-12">
                <!-- Bill Header -->
                <div class="bill-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h2 class="text-primary"><?php echo SITE_NAME; ?></h2>
                            <p class="mb-0">MLM Binary Plan System</p>
                            <p class="mb-0">Franchise Assignment Bill</p>
                            <?php if ($orderDetails['franchise_name']): ?>
                                <p class="mb-0 text-muted">
                                    <small>Assigned by: <?php echo htmlspecialchars($orderDetails['franchise_name']); ?> (<?php echo htmlspecialchars($orderDetails['franchise_code']); ?>)</small>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <h4>Bill #<?php echo htmlspecialchars($orderId); ?></h4>
                            <p class="mb-0">Date: <?php echo date('d M Y', strtotime($orderDetails['created_at'])); ?></p>
                            <p class="mb-0">Time: <?php echo date('h:i A', strtotime($orderDetails['created_at'])); ?></p>
                            <p class="mb-0">
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>Admin Approved
                                </span>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Customer Details -->
                <div class="bill-details">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Bill To:</h5>
                            <p class="mb-1"><strong><?php echo htmlspecialchars($orderDetails['full_name']); ?></strong></p>
                            <p class="mb-1">User ID: <?php echo htmlspecialchars($userId); ?></p>
                            <p class="mb-1">Email: <?php echo htmlspecialchars($orderDetails['email']); ?></p>
                            <p class="mb-1">Phone: <?php echo htmlspecialchars($orderDetails['phone']); ?></p>
                            <?php if ($orderDetails['address']): ?>
                                <p class="mb-0">Address: <?php echo htmlspecialchars($orderDetails['address']); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <h5>Order Details:</h5>
                            <p class="mb-1">Order ID: <?php echo htmlspecialchars($orderDetails['order_id']); ?></p>
                            <p class="mb-1">Status:
                                <span class="badge bg-<?php
                                    echo match($orderDetails['order_status']) {
                                        'confirmed' => 'success',
                                        'pending' => 'warning',
                                        'cancelled' => 'danger',
                                        default => 'secondary'
                                    };
                                ?>">
                                    <?php echo ucfirst($orderDetails['order_status']); ?>
                                </span>
                            </p>
                            <p class="mb-1">Assignment Type:
                                <span class="badge bg-info">
                                    <i class="fas fa-building me-1"></i>Franchise Assignment
                                </span>
                            </p>
                            <p class="mb-1">Approval Status:
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>Admin Approved
                                </span>
                            </p>
                            <p class="mb-1">Placement Side:
                                <span class="badge bg-<?php echo $orderDetails['placement_side'] === 'left' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($orderDetails['placement_side']); ?>
                                </span>
                            </p>
                            <?php if ($orderDetails['franchise_name']): ?>
                                <p class="mb-1">Assigned By:
                                    <strong><?php echo htmlspecialchars($orderDetails['franchise_name']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($orderDetails['franchise_code']); ?></small>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Product Details -->
                <div class="table-responsive mb-4">
                    <table class="table bill-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Description</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>PV Value</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?php echo htmlspecialchars($orderDetails['product_name']); ?></td>
                                <td><?php echo htmlspecialchars($orderDetails['product_description']); ?></td>
                                <td><?php echo $orderDetails['quantity']; ?></td>
                                <td><?php echo formatCurrency($orderDetails['unit_price']); ?></td>
                                <td><?php echo formatPV($orderDetails['pv_amount']); ?></td>
                                <td><?php echo formatCurrency($orderDetails['total_amount']); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Total Section -->
                <div class="row">
                    <div class="col-md-6 offset-md-6">
                        <div class="total-section">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span><?php echo formatCurrency($subtotal); ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Discount:</span>
                                <span><?php echo formatCurrency($orderDetails['discount_amount']); ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax (18% GST):</span>
                                <span><?php echo formatCurrency($orderDetails['tax_amount'] ?? $taxAmount); ?></span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>Total Amount:</strong>
                                <strong class="text-success"><?php echo formatCurrency($orderDetails['total_amount']); ?></strong>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <strong>PV Earned:</strong>
                                <strong class="text-info"><?php echo formatPV($orderDetails['pv_amount']); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center mt-5 pt-4" style="border-top: 1px solid #dee2e6;">
                    <p class="text-muted mb-0">Thank you for your purchase!</p>
                    <p class="text-muted">For any queries, please contact our support team.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
