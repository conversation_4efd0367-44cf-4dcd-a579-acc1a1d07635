<?php
/**
 * Test Form Submission
 * Simple test to verify form submission is working
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🧪 Form Submission Test</h2>\n";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .test-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
    .test-header { background-color: #f0f0f0; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 5px 5px 0 0; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Check if form was submitted
$action = $_POST['action'] ?? $_GET['action'] ?? '';

echo "<div class='test-section'>";
echo "<div class='test-header'><h3>📋 Form Submission Status</h3></div>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<p class='success'>✅ POST request received!</p>";
    echo "<p><strong>POST Data:</strong></p>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    if ($action === 'generate_report') {
        echo "<p class='success'>✅ Action 'generate_report' detected!</p>";
        
        $weekStart = $_POST['week_start'] ?? '';
        $weekEnd = $_POST['week_end'] ?? '';
        
        if ($weekStart && $weekEnd) {
            echo "<p class='success'>✅ Week dates received: {$weekStart} to {$weekEnd}</p>";
            
            // Simulate report generation
            echo "<p class='info'>🔄 Simulating report generation...</p>";
            sleep(2); // Simulate processing time
            echo "<p class='success'>✅ Report generation simulation completed!</p>";
            
        } else {
            echo "<p class='error'>❌ Week dates not received properly</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ Action '{$action}' not recognized</p>";
    }
} else {
    echo "<p class='info'>ℹ️ No POST request received yet</p>";
}

echo "</div>";

// Include required files for week calculation
try {
    require_once '../includes/WeeklyDateHelper.php';
    $previousWeek = WeeklyDateHelper::getPreviousWeek();
    $defaultStart = $previousWeek['start'];
    $defaultEnd = $previousWeek['end'];
} catch (Exception $e) {
    $defaultStart = date('Y-m-d', strtotime('-7 days'));
    $defaultEnd = date('Y-m-d', strtotime('-1 day'));
}
?>

<div class='test-section'>
    <div class='test-header'><h3>📝 Test Form</h3></div>
    
    <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>" id="testForm">
        <input type="hidden" name="action" value="generate_report">
        
        <div style="margin-bottom: 15px;">
            <label for="week_start" style="display: block; margin-bottom: 5px;">Week Start Date:</label>
            <input type="date" id="week_start" name="week_start" value="<?php echo $defaultStart; ?>" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label for="week_end" style="display: block; margin-bottom: 5px;">Week End Date:</label>
            <input type="date" id="week_end" name="week_end" value="<?php echo $defaultEnd; ?>" required style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div style="margin-bottom: 15px;">
            <button type="submit" id="submitBtn" style="background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                Generate Test Report
            </button>
        </div>
        
        <div id="status" style="margin-top: 15px;"></div>
    </form>
</div>

<div class='test-section'>
    <div class='test-header'><h3>🔧 JavaScript Test</h3></div>
    <p>Click the button below to test JavaScript functionality:</p>
    <button onclick="testJavaScript()" style="background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
        Test JavaScript
    </button>
    <div id="jsResult" style="margin-top: 10px;"></div>
</div>

<script>
function testJavaScript() {
    const resultDiv = document.getElementById('jsResult');
    resultDiv.innerHTML = '<span style="color: green; font-weight: bold;">✅ JavaScript is working!</span>';
    
    // Test form elements
    const form = document.getElementById('testForm');
    const weekStart = document.getElementById('week_start');
    const weekEnd = document.getElementById('week_end');
    
    if (form && weekStart && weekEnd) {
        resultDiv.innerHTML += '<br><span style="color: green;">✅ Form elements found</span>';
        resultDiv.innerHTML += '<br>Week Start: ' + weekStart.value;
        resultDiv.innerHTML += '<br>Week End: ' + weekEnd.value;
    } else {
        resultDiv.innerHTML += '<br><span style="color: red;">❌ Form elements not found</span>';
    }
}

// Add form submission handler
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('testForm');
    const submitBtn = document.getElementById('submitBtn');
    const statusDiv = document.getElementById('status');
    
    form.addEventListener('submit', function(e) {
        console.log('Form submit event triggered');
        statusDiv.innerHTML = '<span style="color: blue;">🔄 Submitting form...</span>';
        
        // Disable button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.innerHTML = 'Processing...';
        
        // Allow form to submit normally
        return true;
    });
    
    console.log('Form submission handler attached');
});
</script>

<?php
echo "<div class='test-section'>";
echo "<div class='test-header'><h3>📊 Server Information</h3></div>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>Request Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</li>";
echo "<li><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</li>";
echo "<li><strong>Query String:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'None') . "</li>";
echo "<li><strong>User Agent:</strong> " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "</li>";
echo "</ul>";
echo "</div>";
?>
