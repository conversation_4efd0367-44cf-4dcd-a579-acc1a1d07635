{"timestamp": "2025-08-09 21:52:53", "summary": {"total": 16, "passed": 16, "failed": 0, "errors": 0, "success_rate": 100, "total_duration_ms": 765}, "tests": [{"name": "Add PV Transaction", "result": "PASS", "duration": 10.53}, {"name": "Get Available PV", "result": "PASS", "duration": 2.48}, {"name": "PV Usage Tracking", "result": "PASS", "duration": 0.68}, {"name": "Input Validation", "result": "PASS", "duration": 2.92}, {"name": "Prevent Duplicate Income Processing", "result": "PASS", "duration": 17.36}, {"name": "PV Usage Tracking Consistency", "result": "PASS", "duration": 23.09}, {"name": "FIFO PV Usage", "result": "PASS", "duration": 43.82}, {"name": "System Data Integrity", "result": "PASS", "duration": 5.12}, {"name": "Transaction Consistency", "result": "PASS", "duration": 1.09}, {"name": "No Negative Values", "result": "PASS", "duration": 0.72}, {"name": "Bulk PV Addition Performance", "result": "PASS", "duration": 575.88}, {"name": "Income Processing Performance", "result": "PASS", "duration": 10.06}, {"name": "Zero PV Amount Handling", "result": "PASS", "duration": 2}, {"name": "Large PV Amount Handling", "result": "PASS", "duration": 6.3}, {"name": "Invalid User ID Handling", "result": "PASS", "duration": 2.74}, {"name": "Complete Income Generation Workflow", "result": "PASS", "duration": 60.21}]}