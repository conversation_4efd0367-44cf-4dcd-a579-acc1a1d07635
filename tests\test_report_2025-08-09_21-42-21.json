{"timestamp": "2025-08-09 21:42:21", "summary": {"total": 16, "passed": 4, "failed": 2, "errors": 10, "success_rate": 25, "total_duration_ms": 20.11}, "tests": [{"name": "Add PV Transaction", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list'", "duration": 0}, {"name": "Get Available PV", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause'", "duration": 0}, {"name": "PV Usage Tracking", "result": "FAIL", "duration": 0.96}, {"name": "Input Validation", "result": "PASS", "duration": 2.91}, {"name": "Prevent Duplicate Income Processing", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause'", "duration": 0}, {"name": "PV Usage Tracking Consistency", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list'", "duration": 0}, {"name": "FIFO PV Usage", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list'", "duration": 0}, {"name": "System Data Integrity", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pt.processing_status' in 'where clause'", "duration": 0}, {"name": "Transaction Consistency", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pt.processing_status' in 'where clause'", "duration": 0}, {"name": "No Negative Values", "result": "PASS", "duration": 0.92}, {"name": "Bulk PV Addition Performance", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list'", "duration": 0}, {"name": "Income Processing Performance", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547559402035", "duration": 0}, {"name": "Zero PV Amount Handling", "result": "PASS", "duration": 5.35}, {"name": "Large PV Amount Handling", "result": "FAIL", "duration": 6.57}, {"name": "Invalid User ID Handling", "result": "PASS", "duration": 3.4}, {"name": "Complete Income Generation Workflow", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list'", "duration": 0}]}