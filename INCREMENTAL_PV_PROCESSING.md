# Incremental PV Processing System

## Overview

The system has been enhanced to support **incremental PV processing**, allowing users to be processed multiple times within the same week while ensuring that only unused PVs generate income. This prevents duplicate income generation while enabling flexible report generation.

## Key Features

### ✅ **Multiple Processing Per Week**
- Users can be processed multiple times within the same week
- Each processing creates a separate `weekly_income_logs` entry
- No more blocking of same-user same-week processing

### ✅ **Unused PV Only Processing**
- Only PVs that haven't been used in previous reports are processed
- Previously used PVs are excluded from subsequent reports
- Prevents duplicate income generation from the same PV

### ✅ **Incremental Income Generation**
- Income is generated only from unused PVs in each report
- Total income across all reports reflects actual PV usage
- No overpayment or duplicate income issues

## Database Changes

### New Columns in `weekly_income_logs`

| Column | Type | Description |
|--------|------|-------------|
| `report_sequence` | INT | Tracks the order of processing (1, 2, 3, etc.) |
| `processing_type` | ENUM | 'initial' or 'incremental' processing |
| `report_id` | VARCHAR(30) | Links to specific report generation |

### Removed Constraints
- **Removed**: `UNIQUE KEY unique_user_week (user_id, week_start_date, week_end_date)`
- **Reason**: To allow multiple entries per user per week

### New Indexes
- `idx_user_week_sequence` - Efficient querying by user, week, and sequence
- `idx_processing_type` - Filter by processing type
- `idx_report_id` - Link to specific reports

## How It Works

### Scenario 1: First Processing (Initial)
```
Week: Saturday Aug 16 - Friday Aug 22, 2025
User: John Doe (ID: 123)

Available PV:
- Left: 1000 PV
- Right: 800 PV

Processing Result:
- Matched PV: 800 (min of left and right)
- Income Generated: ₹400 (based on 800 matched PV)
- Report Sequence: 1
- Processing Type: 'initial'

PV Status After:
- Left: 200 PV (1000 - 800 used)
- Right: 0 PV (800 - 800 used)
```

### Scenario 2: Second Processing (Incremental)
```
Same Week: Saturday Aug 16 - Friday Aug 22, 2025
User: John Doe (ID: 123)

New PV Added:
- Right: 300 PV (new transactions)

Available PV for Incremental Processing:
- Left: 200 PV (remaining from before)
- Right: 300 PV (newly added)

Processing Result:
- Matched PV: 200 (min of available left and right)
- Income Generated: ₹100 (based on 200 matched PV)
- Report Sequence: 2
- Processing Type: 'incremental'

Total for Week:
- Total Matched PV: 1000 (800 + 200)
- Total Income: ₹500 (₹400 + ₹100)
```

### Scenario 3: Third Processing (No Unused PV)
```
Same Week: Saturday Aug 16 - Friday Aug 22, 2025
User: John Doe (ID: 123)

Available PV for Incremental Processing:
- Left: 0 PV (all used)
- Right: 100 PV (remaining)

Processing Result:
- Status: Skipped (no matching PV available)
- Income Generated: ₹0
- Reason: Cannot match left=0 with right=100
```

## Code Changes

### New Methods in PVSystem

#### `getAvailablePVForIncrementalProcessing($userId, $weekStartDate)`
```php
// Returns only PVs that haven't been used for the specific week
// More restrictive than getAvailablePVForWeek()
```

#### `recordWeeklyIncomeLogWithSequence(...)`
```php
// Records income log with sequence and processing type support
// Supports multiple entries per user per week
```

#### `getWeeklyProcessingSummary($userId, $weekStartDate)`
```php
// Returns summary of all processing for a user in a specific week
// Shows total reports, income, matched PV, etc.
```

#### `hasUnusedPVForWeek($userId, $weekStartDate)`
```php
// Checks if user has any unused PVs available for processing
// Used to determine if incremental processing should proceed
```

### Updated Processing Logic

#### `processWeeklyPVMatching()` Enhanced
- Supports incremental processing with `$reportId` parameter
- Automatically detects if processing is initial or incremental
- Uses appropriate PV availability calculation method
- Creates separate log entries for each processing

#### `runWeeklyMatching()` Enhanced
- Supports `$reportId` parameter for linking to specific reports
- Handles incremental processing across all users
- Provides detailed statistics on processing results

## Configuration

### New Configuration Options
```php
'incremental_processing_enabled' => true  // Enable/disable incremental processing
```

## Benefits

### 🎯 **For Administrators**
- **Flexible Reporting**: Generate multiple reports per week as needed
- **Accurate Tracking**: Each report shows only new/unused PV processing
- **Audit Trail**: Complete history of all processing with sequences
- **No Duplicates**: Guaranteed prevention of duplicate income generation

### 💰 **For Users**
- **Fair Income**: Only unused PVs generate income
- **Multiple Opportunities**: New PV can be processed immediately
- **Transparent History**: Clear record of all income generation
- **No Overpayment**: System prevents duplicate income from same PV

### 🔧 **For System**
- **Data Integrity**: Maintains accurate PV usage tracking
- **Performance**: Efficient querying with new indexes
- **Scalability**: Supports high-frequency report generation
- **Reliability**: Robust validation prevents edge cases

## Migration Process

### 1. Run Migration
```bash
php migrate-incremental-processing.php
```

### 2. Test the System
```bash
# Test incremental processing
php admin/test-incremental-pv-processing.php
```

### 3. Update Report Generation
- Modify report generation scripts to use new logic
- Update admin interfaces to show processing sequences
- Add support for report IDs in processing calls

## Usage Examples

### Generate Multiple Reports for Same Week
```php
// First report
$result1 = $pvSystem->runWeeklyMatching($weekStart, $weekEnd, false, 'REPORT_001');

// Second report (incremental)
$result2 = $pvSystem->runWeeklyMatching($weekStart, $weekEnd, false, 'REPORT_002');

// Third report (incremental)
$result3 = $pvSystem->runWeeklyMatching($weekStart, $weekEnd, false, 'REPORT_003');
```

### Check Processing Summary
```php
$summary = $pvSystem->getWeeklyProcessingSummary($userId, $weekStart);
echo "Total Reports: " . $summary['total_reports'];
echo "Total Income: ₹" . $summary['total_income'];
echo "Processing Types: " . $summary['processing_types'];
```

### Verify Unused PV Availability
```php
$hasUnusedPV = $pvSystem->hasUnusedPVForWeek($userId, $weekStart);
if ($hasUnusedPV) {
    // Process incremental PVs
    $result = $pvSystem->processWeeklyPVMatching($userId, $weekStart, $weekEnd, false, $reportId);
}
```

## Testing and Validation

### Test Scenarios Covered
1. ✅ First processing (initial) - processes available PVs
2. ✅ Second processing (incremental) - processes only unused PVs
3. ✅ Third processing (no unused PV) - correctly skips processing
4. ✅ Multiple reports per week - each creates separate log entry
5. ✅ PV usage tracking - prevents reuse of already-used PVs
6. ✅ Income calculation - accurate amounts based on unused PVs only

### Validation Points
- No duplicate income generation
- Accurate PV usage tracking
- Proper sequence numbering
- Correct processing type assignment
- Efficient database queries
- Complete audit trail

## Troubleshooting

### Common Issues
1. **No Incremental Processing**: Check if `incremental_processing_enabled` is true
2. **Duplicate Income**: Verify PV usage tracking is working correctly
3. **Missing Sequences**: Ensure migration was run successfully
4. **Performance Issues**: Check if new indexes are created properly

### Debug Commands
```php
// Check available PV for incremental processing
$availablePV = $pvSystem->getAvailablePVForIncrementalProcessing($userId, $weekStart);

// Get processing summary
$summary = $pvSystem->getWeeklyProcessingSummary($userId, $weekStart);

// Check if user has unused PV
$hasUnused = $pvSystem->hasUnusedPVForWeek($userId, $weekStart);
```

## Conclusion

The incremental PV processing system provides the flexibility to generate multiple reports per week while maintaining strict financial integrity. Users can only receive income from unused PVs, preventing any duplicate payments while allowing administrators to generate reports as needed for business operations.
