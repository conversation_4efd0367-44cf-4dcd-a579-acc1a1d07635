<?php
/**
 * Admin Product Assignment Approval Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Validator.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$pvSystem = new PVSystem();
$db = Database::getInstance();

// Handle approval/rejection actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    verifyCsrfToken();
    
    $requestId = (int) $_POST['request_id'];
    $action = $_POST['action'];
    $adminNotes = sanitizeInput($_POST['admin_notes'] ?? '');
    
    try {
        // Get request details
        $requestStmt = $db->prepare("
            SELECT par.*, u.full_name as user_name, f.full_name as franchise_name, p.name as product_name, p.pv_value
            FROM product_assignment_requests par
            JOIN users u ON par.user_id = u.user_id
            JOIN franchise f ON par.franchise_id = f.id
            JOIN products p ON par.product_id = p.id
            WHERE par.id = ? AND par.status = 'pending'
        ");
        $requestStmt->execute([$requestId]);
        $request = $requestStmt->fetch();

        if (!$request) {
            throw new Exception("Request not found or already processed");
        }

        if ($action === 'approve') {
            try {
                // Get product details for pricing
                $productStmt = $db->prepare("SELECT * FROM products WHERE id = ?");
                $productStmt->execute([$request['product_id']]);
                $product = $productStmt->fetch();

                if (!$product) {
                    throw new Exception("Product not found");
                }

                // Calculate totals
                $totalPV = $product['pv_value'] * $request['quantity'];
                $totalAmount = $product['price'] * $request['quantity'];

                // Generate unique order ID
                $orderId = 'ORD' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

                // Check if order ID already exists
                $orderCheckStmt = $db->prepare("SELECT id FROM purchase_orders WHERE order_id = ?");
                $orderCheckStmt->execute([$orderId]);
                while ($orderCheckStmt->fetch()) {
                    $orderId = 'ORD' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
                    $orderCheckStmt->execute([$orderId]);
                }

                // Create purchase order record
                $orderStmt = $db->prepare("INSERT INTO purchase_orders (order_id, user_id, product_id, quantity, total_amount, pv_amount, placement_side, payment_method, payment_status, order_status) VALUES (?, ?, ?, ?, ?, ?, ?, 'manual', 'completed', 'confirmed')");
                $orderResult = $orderStmt->execute([
                    $orderId,
                    $request['user_id'],
                    $request['product_id'],
                    $request['quantity'],
                    $totalAmount,
                    $totalPV,
                    $request['pv_side']
                ]);

                if (!$orderResult) {
                    throw new Exception("Failed to create purchase order");
                }

                // Add PV to user's self PV and propagate to upline
                error_log("Attempting to add self PV: userId={$request['user_id']}, totalPV={$totalPV}, productId={$request['product_id']}, orderId={$orderId}");

                $result = $pvSystem->addSelfPV(
                    $request['user_id'],
                    $totalPV,
                    'purchase',
                    $request['product_id'],
                    $orderId,
                    $request['description'] . " (Admin Approved - Order: {$orderId})",
                    'admin',
                    $adminId
                );

                if (!$result) {
                    error_log("Self PV addition failed for user {$request['user_id']} with {$totalPV} PV");
                    throw new Exception("Failed to add PV transaction. Please check the error logs for details.");
                }

                // Update request status
                $updateStmt = $db->prepare("UPDATE product_assignment_requests SET status = 'approved', processed_at = NOW(), processed_by = ?, admin_notes = ? WHERE id = ?");
                $updateResult = $updateStmt->execute([$adminId, $adminNotes, $requestId]);

                if (!$updateResult) {
                    throw new Exception("Failed to update request status");
                }

                // Check if user was activated due to this PV addition
                require_once '../models/User.php';
                $userModel = new User();
                $userAfterPV = $userModel->findByUserId($request['user_id']);
                $activationMessage = '';

                if ($userAfterPV && $userAfterPV['status'] === 'active') {
                    // Check if user was previously inactive
                    $userBeforePV = $userModel->findByUserId($request['user_id']);
                    $totalPVAfter = floatval($userAfterPV['self_pv'] ?? 0);

                    if ($totalPVAfter >= 500) {
                        $activationMessage = " 🎉 User has been automatically ACTIVATED (reached {$totalPVAfter} PV)!";
                    }
                }

                setSuccessMessage("Product assignment request approved successfully! Order {$orderId} created, {$totalPV} PV added to personal balance for {$request['user_name']} and propagated to upline.{$activationMessage}");
                Response::redirect('product-approvals.php');

            } catch (Exception $e) {
                throw $e;
            }

        } elseif ($action === 'reject') {
            // Update request status
            $updateStmt = $db->prepare("UPDATE product_assignment_requests SET status = 'rejected', processed_at = NOW(), processed_by = ?, admin_notes = ? WHERE id = ?");
            $updateStmt->execute([$adminId, $adminNotes, $requestId]);

            setSuccessMessage("Product assignment request rejected successfully.");
            Response::redirect('product-approvals.php');
        }

    } catch (Exception $e) {
        setErrorMessage($e->getMessage());
        Response::redirect('product-approvals.php');
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'pending';
$franchise = $_GET['franchise'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

// Build query conditions
$whereConditions = [];
$params = [];

if ($status && $status !== 'all') {
    $whereConditions[] = "par.status = ?";
    $params[] = $status;
}

if ($franchise) {
    $whereConditions[] = "f.id = ?";
    $params[] = $franchise;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(par.requested_at) >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(par.requested_at) <= ?";
    $params[] = $dateTo;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get requests
$requestsStmt = $db->prepare("
    SELECT par.*, 
           u.full_name as user_name, u.user_id as user_code,
           f.full_name as franchise_name, f.franchise_code,
           p.name as product_name, p.price, p.pv_value,
           a.full_name as processed_by_name
    FROM product_assignment_requests par
    JOIN users u ON par.user_id = u.user_id
    JOIN franchise f ON par.franchise_id = f.id
    JOIN products p ON par.product_id = p.id
    LEFT JOIN admin a ON par.processed_by = a.id
    {$whereClause}
    ORDER BY par.requested_at DESC
");
$requestsStmt->execute($params);
$requests = $requestsStmt->fetchAll();

// Get franchises for filter
$franchisesStmt = $db->query("SELECT id, franchise_code, full_name FROM franchise WHERE status = 'active' ORDER BY full_name");
$franchises = $franchisesStmt->fetchAll();

// Get statistics
$statsStmt = $db->query("
    SELECT 
        COUNT(*) as total_requests,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests
    FROM product_assignment_requests
");
$stats = $statsStmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Assignment Approvals - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-check-circle me-2"></i>Product Assignment Approvals</h2>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['pending_requests']); ?></h4>
                            <small class="text-muted">Pending Requests</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['approved_requests']); ?></h4>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon danger me-3">
                            <i class="fas fa-times"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['rejected_requests']); ?></h4>
                            <small class="text-muted">Rejected</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-list"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_requests']); ?></h4>
                            <small class="text-muted">Total Requests</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="franchise" class="form-label">Franchise</label>
                                <select class="form-select" id="franchise" name="franchise">
                                    <option value="">All Franchises</option>
                                    <?php foreach ($franchises as $f): ?>
                                        <option value="<?php echo $f['id']; ?>" <?php echo $franchise == $f['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($f['franchise_code'] . ' - ' . $f['full_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requests Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Product Assignment Requests</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($requests)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Request ID</th>
                                            <th>Franchise</th>
                                            <th>User</th>
                                            <th>Product</th>
                                            <th>Quantity</th>
                                            <th>PV Type</th>
                                            <th>Total PV</th>
                                            <th>Status</th>
                                            <th>Requested</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($requests as $request): ?>
                                            <tr>
                                                <td><strong>#<?php echo $request['id']; ?></strong></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['franchise_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($request['franchise_code']); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['user_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($request['user_code']); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($request['product_name']); ?></strong><br>
                                                    <small class="text-muted">₹<?php echo number_format($request['price'], 2); ?></small>
                                                </td>
                                                <td><span class="badge bg-primary"><?php echo $request['quantity']; ?></span></td>
                                                <td>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-user me-1"></i>Personal PV
                                                    </span>
                                                </td>
                                                <td><strong><?php echo formatPV($request['pv_value'] * $request['quantity']); ?></strong></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch ($request['status']) {
                                                        case 'pending': $statusClass = 'warning'; break;
                                                        case 'approved': $statusClass = 'success'; break;
                                                        case 'rejected': $statusClass = 'danger'; break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                                        <?php echo ucfirst($request['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M d, Y H:i', strtotime($request['requested_at'])); ?></td>
                                                <td>
                                                    <?php if ($request['status'] === 'pending'): ?>
                                                        <button class="btn btn-sm btn-success me-1" onclick="showApprovalModal(<?php echo $request['id']; ?>, 'approve', '<?php echo htmlspecialchars($request['user_name']); ?>', '<?php echo htmlspecialchars($request['product_name']); ?>')">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-danger" onclick="showApprovalModal(<?php echo $request['id']; ?>, 'reject', '<?php echo htmlspecialchars($request['user_name']); ?>', '<?php echo htmlspecialchars($request['product_name']); ?>')">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <small class="text-muted">
                                                            <?php if ($request['processed_by_name']): ?>
                                                                By: <?php echo htmlspecialchars($request['processed_by_name']); ?><br>
                                                            <?php endif; ?>
                                                            <?php echo date('M d, Y H:i', strtotime($request['processed_at'])); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No product assignment requests found</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Approval Modal -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" id="modal_request_id" name="request_id">
                    <input type="hidden" id="modal_action" name="action">

                    <div class="modal-header">
                        <h5 class="modal-title" id="modalTitle">Confirm Action</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="modalContent"></div>
                        <div class="mt-3">
                            <label for="admin_notes" class="form-label">Admin Notes (Optional)</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" placeholder="Add any notes about this decision..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn" id="modalSubmitBtn">Confirm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showApprovalModal(requestId, action, userName, productName) {
            document.getElementById('modal_request_id').value = requestId;
            document.getElementById('modal_action').value = action;

            const modal = document.getElementById('approvalModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');
            const submitBtn = document.getElementById('modalSubmitBtn');

            if (action === 'approve') {
                title.textContent = 'Approve Product Assignment';
                content.innerHTML = `<div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Approve assignment of "${productName}" to "${userName}"?</strong>
                    <p class="mb-0 mt-2">This will add the PV to the user's account and mark the request as approved.</p>
                </div>`;
                submitBtn.className = 'btn btn-success';
                submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>Approve';
            } else {
                title.textContent = 'Reject Product Assignment';
                content.innerHTML = `<div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Reject assignment of "${productName}" to "${userName}"?</strong>
                    <p class="mb-0 mt-2">This will mark the request as rejected and no PV will be added.</p>
                </div>`;
                submitBtn.className = 'btn btn-danger';
                submitBtn.innerHTML = '<i class="fas fa-times me-1"></i>Reject';
            }

            new bootstrap.Modal(modal).show();
        }
    </script>
</body>
</html>
