{"timestamp": "2025-08-09 21:46:32", "summary": {"total": 16, "passed": 11, "failed": 0, "errors": 5, "success_rate": 68.75, "total_duration_ms": 594.21}, "tests": [{"name": "Add PV Transaction", "result": "PASS", "duration": 8.84}, {"name": "Get Available PV", "result": "PASS", "duration": 1.79}, {"name": "PV Usage Tracking", "result": "PASS", "duration": 0.71}, {"name": "Input Validation", "result": "PASS", "duration": 2.47}, {"name": "Prevent Duplicate Income Processing", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547561919286", "duration": 0}, {"name": "PV Usage Tracking Consistency", "result": "PASS", "duration": 18.19}, {"name": "FIFO PV Usage", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547561919286", "duration": 0}, {"name": "System Data Integrity", "result": "ERROR", "error": "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'pt.user_id' in 'where clause'", "duration": 0}, {"name": "Transaction Consistency", "result": "PASS", "duration": 0.61}, {"name": "No Negative Values", "result": "PASS", "duration": 0.54}, {"name": "Bulk PV Addition Performance", "result": "PASS", "duration": 549.94}, {"name": "Income Processing Performance", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547561919286", "duration": 0}, {"name": "Zero PV Amount Handling", "result": "PASS", "duration": 3.06}, {"name": "Large PV Amount Handling", "result": "PASS", "duration": 5.72}, {"name": "Invalid User ID Handling", "result": "PASS", "duration": 2.34}, {"name": "Complete Income Generation Workflow", "result": "ERROR", "error": "Unable to acquire processing lock for user TEST17547561919286", "duration": 0}]}