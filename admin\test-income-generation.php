<?php
/**
 * Test Income Generation
 * Comprehensive test to verify income generation is working properly
 */

require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';
require_once '../config/config.php';
require_once '../includes/WeeklyDateHelper.php';

// Initialize classes
$db = Database::getInstance();
$pvSystem = new PVSystem();
$wallet = new Wallet();
$config = Config::getInstance();

echo "<h2>Income Generation Test</h2>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// Test parameters (Saturday-Friday weeks)
$currentWeek = WeeklyDateHelper::getCurrentWeek();
$testWeekStart = $currentWeek['start'];
$testWeekEnd = $currentWeek['end'];

echo "<h3>Test Parameters:</h3>";
echo "<ul>";
echo "<li>Test Week: {$testWeekStart} to {$testWeekEnd}</li>";
echo "<li>PV Rate: ₹" . $config->getPVRate() . "</li>";
echo "<li>Weekly Capping: ₹" . number_format($config->get('weekly_capping', 130000)) . "</li>";
echo "</ul>";

// Step 1: Find users with available PV
echo "<h3>Step 1: Finding Users with Available PV</h3>";
try {
    $availableUsersStmt = $db->query("
        SELECT u.user_id, u.full_name, u.email,
               SUM(CASE WHEN put.side = 'left' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) as left_pv,
               SUM(CASE WHEN put.side = 'right' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) as right_pv
        FROM users u
        JOIN pv_usage_tracking put ON u.user_id = put.user_id
        WHERE u.status = 'active' AND put.remaining_amount > 0
        GROUP BY u.user_id, u.full_name, u.email
        HAVING SUM(CASE WHEN put.side = 'left' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) > 0
           AND SUM(CASE WHEN put.side = 'right' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) > 0
        ORDER BY LEAST(SUM(CASE WHEN put.side = 'left' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END),
                      SUM(CASE WHEN put.side = 'right' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END)) DESC
        LIMIT 5
    ");
    $availableUsers = $availableUsersStmt->fetchAll();
    
    if (!empty($availableUsers)) {
        echo "<p class='success'>✓ Found " . count($availableUsers) . " users with available PV for matching</p>";
        
        echo "<table>";
        echo "<tr><th>User ID</th><th>Name</th><th>Left PV</th><th>Right PV</th><th>Potential Match</th></tr>";
        foreach ($availableUsers as $user) {
            $potentialMatch = min($user['left_pv'], $user['right_pv']);
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
            echo "<td>" . number_format($user['left_pv'], 2) . "</td>";
            echo "<td>" . number_format($user['right_pv'], 2) . "</td>";
            echo "<td>" . number_format($potentialMatch, 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p class='warning'>⚠ No users found with available PV for matching</p>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>No PV transactions exist</li>";
        echo "<li>All PV has been used</li>";
        echo "<li>PV usage tracking is not initialized</li>";
        echo "</ul>";
        echo "<p><a href='fix-pv-tracking.php'>Run PV Tracking Fix</a></p>";
        exit;
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error finding available users: " . $e->getMessage() . "</p>";
    exit;
}

// Step 2: Test individual user processing
echo "<h3>Step 2: Testing Individual User Processing</h3>";
$testUser = $availableUsers[0];
echo "<p>Testing user: <strong>{$testUser['full_name']} ({$testUser['user_id']})</strong></p>";

try {
    // Check if already processed this week
    $existingStmt = $db->prepare("SELECT id FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
    $existingStmt->execute([$testUser['user_id'], $testWeekStart]);
    $existing = $existingStmt->fetch();
    
    if ($existing) {
        echo "<p class='info'>ℹ User already processed for this week. Skipping to avoid duplicate processing.</p>";
    } else {
        // Get wallet balance before processing
        $walletBefore = $wallet->getWallet($testUser['user_id']);
        $balanceBefore = $walletBefore['balance'];
        
        echo "<p>Wallet balance before: ₹" . number_format($balanceBefore, 2) . "</p>";
        
        // Process the user
        $result = $pvSystem->processWeeklyPVMatching($testUser['user_id'], $testWeekStart, $testWeekEnd);
        
        if ($result && is_array($result)) {
            echo "<p class='success'>✓ User processed successfully!</p>";
            
            echo "<h4>Processing Results:</h4>";
            echo "<ul>";
            echo "<li>Gross Income: ₹" . number_format($result['gross_income_amount'], 2) . "</li>";
            echo "<li>Service Charge (10%): ₹" . number_format($result['service_charge'], 2) . "</li>";
            echo "<li>TDS (5%): ₹" . number_format($result['tds_amount'], 2) . "</li>";
            echo "<li>Net Income: ₹" . number_format($result['income_amount'], 2) . "</li>";
            echo "<li>Capping Applied: ₹" . number_format($result['capping_applied'], 2) . "</li>";
            echo "<li>Matched PV: " . number_format($result['matched_pv'], 2) . "</li>";
            echo "</ul>";
            
            // Check wallet balance after processing
            $walletAfter = $wallet->getWallet($testUser['user_id']);
            $balanceAfter = $walletAfter['balance'];
            $balanceIncrease = $balanceAfter - $balanceBefore;
            
            echo "<p>Wallet balance after: ₹" . number_format($balanceAfter, 2) . "</p>";
            echo "<p>Balance increase: ₹" . number_format($balanceIncrease, 2) . "</p>";
            
            if (abs($balanceIncrease - $result['income_amount']) < 0.01) {
                echo "<p class='success'>✓ Wallet credited correctly!</p>";
            } else {
                echo "<p class='error'>✗ Wallet credit mismatch! Expected: ₹" . number_format($result['income_amount'], 2) . ", Actual: ₹" . number_format($balanceIncrease, 2) . "</p>";
            }
            
            // Check if income log was created
            $logStmt = $db->prepare("SELECT * FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
            $logStmt->execute([$testUser['user_id'], $testWeekStart]);
            $log = $logStmt->fetch();
            
            if ($log) {
                echo "<p class='success'>✓ Income log created successfully!</p>";
            } else {
                echo "<p class='error'>✗ Income log not created!</p>";
            }
            
        } else {
            echo "<p class='error'>✗ User processing failed!</p>";
            if (isset($result['error'])) {
                echo "<p>Error: " . $result['error'] . "</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error processing user: " . $e->getMessage() . "</p>";
}

// Step 3: Test full report generation
echo "<h3>Step 3: Testing Full Report Generation</h3>";
try {
    echo "<p>Running full weekly matching for test week...</p>";
    
    $reportResult = $pvSystem->runWeeklyMatching($testWeekStart, $testWeekEnd);
    
    if ($reportResult && is_array($reportResult)) {
        echo "<p class='success'>✓ Report generation completed!</p>";
        
        echo "<h4>Report Results:</h4>";
        echo "<ul>";
        echo "<li>Processed Users: {$reportResult['processed']}</li>";
        echo "<li>Users with Income: {$reportResult['users_with_income']}</li>";
        echo "<li>Total Gross Income: ₹" . number_format($reportResult['total_gross_income'], 2) . "</li>";
        echo "<li>Total Service Charge: ₹" . number_format($reportResult['total_service_charge'], 2) . "</li>";
        echo "<li>Total TDS: ₹" . number_format($reportResult['total_tds_amount'], 2) . "</li>";
        echo "<li>Total Net Income: ₹" . number_format($reportResult['total_income'], 2) . "</li>";
        echo "<li>Total Capping Applied: ₹" . number_format($reportResult['total_capping'], 2) . "</li>";
        
        if (isset($reportResult['error_count'])) {
            echo "<li>Processing Errors: {$reportResult['error_count']}</li>";
        }
        echo "</ul>";
        
        // Check if report was saved
        $savedReportStmt = $db->prepare("SELECT * FROM weekly_income_reports WHERE week_start_date = ?");
        $savedReportStmt->execute([$testWeekStart]);
        $savedReport = $savedReportStmt->fetch();
        
        if ($savedReport) {
            echo "<p class='success'>✓ Report saved to database!</p>";
            echo "<p>Report Status: " . ucfirst($savedReport['report_status']) . "</p>";
        } else {
            echo "<p class='error'>✗ Report not saved to database!</p>";
        }
        
    } else {
        echo "<p class='error'>✗ Report generation failed!</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error generating report: " . $e->getMessage() . "</p>";
}

// Summary
echo "<h3>Test Summary</h3>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
echo "<h4>What This Test Verified:</h4>";
echo "<ul>";
echo "<li>✓ Users with available PV are identified correctly</li>";
echo "<li>✓ Individual user income processing works</li>";
echo "<li>✓ Income calculations are accurate (gross, deductions, net)</li>";
echo "<li>✓ Wallet crediting functions properly</li>";
echo "<li>✓ Income logs are created</li>";
echo "<li>✓ Full report generation completes</li>";
echo "<li>✓ Reports are saved to database</li>";
echo "</ul>";

echo "<h4>If All Tests Pass:</h4>";
echo "<p>Your income generation system is working correctly! You can now:</p>";
echo "<ol>";
echo "<li>Go to <strong>Admin → Weekly Income Reports</strong></li>";
echo "<li>Generate reports for any desired week</li>";
echo "<li>Income will be automatically calculated and credited to user wallets</li>";
echo "</ol>";
echo "</div>";

echo "<p class='success'><strong>Income Generation Test Complete!</strong></p>";
?>
