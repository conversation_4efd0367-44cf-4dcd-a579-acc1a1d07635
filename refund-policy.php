<?php
/**
 * Refund Policy Page
 * ShaktiPure Industries Pvt Ltd
 */

require_once 'includes/header.php';
renderHeader('Refund Policy - ShaktiPure Industries Pvt Ltd');
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="page-header-content">
            <h1>Refund Policy</h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Refund Policy</span>
            </nav>
        </div>
    </div>
</section>

<!-- Refund Policy Content -->
<section class="policy-section">
    <div class="container">
        <div class="policy-content">
            <div class="policy-intro">
                <p class="last-updated">Last updated: January 1, 2025</p>
                <p>At ShaktiPure Industries, customer satisfaction is our priority. This refund policy outlines the terms and conditions for returns, exchanges, and refunds on our water purification products.</p>
            </div>

            <div class="policy-item">
                <h2>1. Return Eligibility</h2>
                <p>Products are eligible for return under the following conditions:</p>
                <ul>
                    <li>Return request made within 30 days of delivery</li>
                    <li>Product is in original condition and packaging</li>
                    <li>All accessories and documentation included</li>
                    <li>No physical damage or signs of use</li>
                    <li>Original purchase receipt available</li>
                </ul>
            </div>

            <div class="policy-item">
                <h2>2. Non-Returnable Items</h2>
                <p>The following items cannot be returned:</p>
                <ul>
                    <li>Installed water purifiers (unless defective)</li>
                    <li>Customized or personalized products</li>
                    <li>Consumable items (filters, cartridges) once opened</li>
                    <li>Products damaged due to misuse</li>
                    <li>Items purchased during clearance sales</li>
                </ul>
            </div>

            <div class="policy-item">
                <h2>3. Return Process</h2>
                <div class="process-steps">
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Contact Us</h4>
                            <p>Call our customer service at +91-8460203679 <NAME_EMAIL></p>
                        </div>
                    </div>
                    <div class="step-item">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Return Authorization</h4>
                            <p>Receive a Return Authorization Number (RAN) and return instructions</p>
                        </div>
                    </div>
                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Package & Ship</h4>
                            <p>Pack the item securely and ship using the provided return label</p>
                        </div>
                    </div>
                    <div class="step-item">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>Processing</h4>
                            <p>We'll inspect the item and process your refund within 5-7 business days</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="policy-item">
                <h2>4. Refund Methods & Timeline</h2>
                <div class="refund-methods">
                    <div class="method-card">
                        <h4>Credit/Debit Card</h4>
                        <div class="timeline">5-7 Business Days</div>
                        <p>Refund processed to original payment method</p>
                    </div>
                    <div class="method-card">
                        <h4>Net Banking</h4>
                        <div class="timeline">3-5 Business Days</div>
                        <p>Direct credit to your bank account</p>
                    </div>
                    <div class="method-card">
                        <h4>UPI/Wallet</h4>
                        <div class="timeline">1-3 Business Days</div>
                        <p>Instant refund to digital wallet</p>
                    </div>
                    <div class="method-card">
                        <h4>Cash on Delivery</h4>
                        <div class="timeline">7-10 Business Days</div>
                        <p>Bank transfer after verification</p>
                    </div>
                </div>
            </div>

            <div class="policy-item">
                <h2>5. Refund Deductions</h2>
                <div class="deduction-table">
                    <div class="table-header">
                        <span>Scenario</span>
                        <span>Deduction</span>
                    </div>
                    <div class="table-row">
                        <span>Defective product</span>
                        <span class="no-charge">No Deduction</span>
                    </div>
                    <div class="table-row">
                        <span>Change of mind (unused)</span>
                        <span>Return shipping cost</span>
                    </div>
                    <div class="table-row">
                        <span>Damaged packaging</span>
                        <span>10% of product value</span>
                    </div>
                    <div class="table-row">
                        <span>Missing accessories</span>
                        <span>Cost of missing items</span>
                    </div>
                </div>
            </div>

            <div class="policy-item">
                <h2>6. Exchange Policy</h2>
                <p>We offer exchanges for:</p>
                <ul>
                    <li>Defective products within warranty period</li>
                    <li>Wrong product delivered</li>
                    <li>Size or model variations (subject to availability)</li>
                </ul>
                <p>Exchange requests must be made within 15 days of delivery.</p>
            </div>

            <div class="policy-item">
                <h2>7. Warranty Claims</h2>
                <p>For products under warranty:</p>
                <ul>
                    <li>Manufacturing defects covered for full warranty period</li>
                    <li>Free repair or replacement as per warranty terms</li>
                    <li>On-site service available for installed products</li>
                    <li>Warranty void if tampered or misused</li>
                </ul>
            </div>

            <div class="policy-item">
                <h2>8. Cancellation Policy</h2>
                <div class="cancellation-info">
                    <div class="cancel-item">
                        <h4>Before Dispatch</h4>
                        <p>100% refund within 24 hours</p>
                    </div>
                    <div class="cancel-item">
                        <h4>After Dispatch</h4>
                        <p>Return process applies</p>
                    </div>
                    <div class="cancel-item">
                        <h4>Custom Orders</h4>
                        <p>No cancellation after production starts</p>
                    </div>
                </div>
            </div>

            <div class="policy-item">
                <h2>9. Contact for Returns</h2>
                <div class="contact-info">
                    <div class="contact-method">
                        <i class="fas fa-phone"></i>
                        <div>
                            <strong>Phone Support</strong>
                            <p>+91-8460203679</p>
                            <small>Mon-Sat: 9:00 AM - 6:00 PM</small>
                        </div>
                    </div>
                    <div class="contact-method">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <strong>Email Support</strong>
                            <p><EMAIL></p>
                            <small>Response within 24 hours</small>
                        </div>
                    </div>
                    <div class="contact-method">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <strong>Return Address</strong>
                            <p>D-224, Udhana Complex, Udhana, Surat-394210, Gujarat</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php renderFooter(); ?>

<style>
/* Page Header */
.page-header {
    background: var(--gradient-primary);
    padding: 60px 0;
    color: white;
}

.page-header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

/* Policy Section */
.policy-section {
    padding: 80px 0;
}

.policy-content {
    max-width: 800px;
    margin: 0 auto;
}

.policy-intro {
    margin-bottom: 40px;
    padding: 30px;
    background: var(--background-card);
    border-radius: var(--card-border-radius);
    border-left: 4px solid var(--primary-green);
}

.last-updated {
    font-size: 14px;
    color: var(--text-gray-light);
    margin-bottom: 15px;
    font-style: italic;
}

.policy-item {
    margin-bottom: 40px;
}

.policy-item h2 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-green);
}

.policy-item p {
    color: var(--text-gray-light);
    line-height: 1.8;
    margin-bottom: 15px;
}

.policy-item ul {
    margin: 15px 0;
    padding-left: 30px;
}

.policy-item li {
    color: var(--text-gray-light);
    line-height: 1.6;
    margin-bottom: 8px;
}

/* Process Steps */
.process-steps {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: var(--background-light);
    border-radius: var(--card-border-radius);
}

.step-number {
    background: var(--primary-green);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h4 {
    color: var(--text-dark);
    margin-bottom: 8px;
}

.step-content p {
    margin: 0;
    font-size: 14px;
}

/* Refund Methods */
.refund-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.method-card {
    background: white;
    padding: 25px;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.method-card h4 {
    color: var(--text-dark);
    margin-bottom: 15px;
}

.timeline {
    background: var(--primary-green);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 15px;
    display: inline-block;
}

.method-card p {
    font-size: 14px;
    margin: 0;
}

/* Deduction Table */
.deduction-table {
    background: white;
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
}

.table-header {
    background: var(--primary-green);
    color: white;
    padding: 15px 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    font-weight: 600;
}

.table-row {
    padding: 15px 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    border-bottom: 1px solid var(--border-color);
    align-items: center;
}

.table-row:last-child {
    border-bottom: none;
}

.no-charge {
    color: var(--primary-green);
    font-weight: 600;
}

/* Cancellation Info */
.cancellation-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.cancel-item {
    background: var(--background-light);
    padding: 20px;
    border-radius: var(--card-border-radius);
    text-align: center;
}

.cancel-item h4 {
    color: var(--text-dark);
    margin-bottom: 10px;
}

.cancel-item p {
    margin: 0;
    font-size: 14px;
}

/* Contact Info */
.contact-info {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: var(--background-light);
    border-radius: var(--card-border-radius);
}

.contact-method i {
    color: var(--primary-green);
    font-size: 1.5rem;
    margin-top: 5px;
}

.contact-method strong {
    color: var(--text-dark);
    display: block;
    margin-bottom: 5px;
}

.contact-method p {
    margin: 0 0 5px 0;
}

.contact-method small {
    color: var(--text-gray-light);
    font-size: 12px;
}

/* Responsive */
@media (max-width: 768px) {
    .refund-methods {
        grid-template-columns: 1fr;
    }
    
    .table-header, .table-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .cancellation-info {
        grid-template-columns: 1fr;
    }
}
</style>
