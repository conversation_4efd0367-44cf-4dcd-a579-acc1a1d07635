<?php
/**
 * Test Report Generation
 * Simple test script to verify report generation functionality
 */

require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../includes/ReportLogger.php';
require_once '../config/config.php';

// Initialize classes
$db = Database::getInstance();
$pvSystem = new PVSystem();
$logger = new ReportLogger('test_report_generation');

echo "<h2>Report Generation Test</h2>";
echo "<p>Testing the weekly report generation functionality...</p>";

// Test dates (last week)
$weekStart = date('Y-m-d', strtotime('monday last week'));
$weekEnd = date('Y-m-d', strtotime('sunday last week'));

echo "<h3>Test Parameters:</h3>";
echo "<ul>";
echo "<li>Week Start: {$weekStart}</li>";
echo "<li>Week End: {$weekEnd}</li>";
echo "</ul>";

// Check database connectivity
echo "<h3>Database Connectivity Test:</h3>";
try {
    $testStmt = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $testResult = $testStmt->fetch();
    echo "<p style='color: green;'>✓ Database connected successfully. Found {$testResult['count']} active users.</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Check required tables
echo "<h3>Database Tables Test:</h3>";
$requiredTables = ['users', 'pv_transactions', 'weekly_income_logs', 'weekly_income_reports', 'wallet'];
foreach ($requiredTables as $table) {
    try {
        $stmt = $db->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->fetch()) {
            echo "<p style='color: green;'>✓ Table '{$table}' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '{$table}' missing</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error checking table '{$table}': " . $e->getMessage() . "</p>";
    }
}

// Test PV System initialization
echo "<h3>PV System Test:</h3>";
try {
    $config = Config::getInstance();
    $pvRate = $config->getPVRate();
    $weeklyCapping = $config->get('weekly_capping', 130000);
    
    echo "<p style='color: green;'>✓ PV System initialized successfully</p>";
    echo "<ul>";
    echo "<li>PV Rate: ₹{$pvRate}</li>";
    echo "<li>Weekly Capping: ₹" . number_format($weeklyCapping) . "</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ PV System initialization failed: " . $e->getMessage() . "</p>";
}

// Test report generation (dry run)
echo "<h3>Report Generation Test:</h3>";
try {
    $logger->info("Starting test report generation");
    
    // Get user count for processing
    $userStmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $userStmt->execute();
    $userCount = $userStmt->fetch()['count'];
    
    echo "<p>Processing {$userCount} active users...</p>";
    
    if ($userCount > 0) {
        // Test the report generation
        $result = $pvSystem->runWeeklyMatching($weekStart, $weekEnd);
        
        if ($result !== false && is_array($result)) {
            echo "<p style='color: green;'>✓ Report generation completed successfully!</p>";
            echo "<h4>Results:</h4>";
            echo "<ul>";
            echo "<li>Processed Users: {$result['processed']}</li>";
            echo "<li>Users with Income: {$result['users_with_income']}</li>";
            echo "<li>Total Gross Income: ₹" . number_format($result['total_gross_income'], 2) . "</li>";
            echo "<li>Total Service Charge: ₹" . number_format($result['total_service_charge'], 2) . "</li>";
            echo "<li>Total TDS: ₹" . number_format($result['total_tds_amount'], 2) . "</li>";
            echo "<li>Total Net Income: ₹" . number_format($result['total_income'], 2) . "</li>";
            echo "<li>Total Capping Applied: ₹" . number_format($result['total_capping'], 2) . "</li>";
            
            if (isset($result['error_count'])) {
                echo "<li>Errors: {$result['error_count']}</li>";
            }
            echo "</ul>";
            
            // Check if report was saved to database
            $reportStmt = $db->prepare("SELECT * FROM weekly_income_reports WHERE week_start_date = ?");
            $reportStmt->execute([$weekStart]);
            $savedReport = $reportStmt->fetch();
            
            if ($savedReport) {
                echo "<p style='color: green;'>✓ Report saved to database successfully</p>";
                echo "<p>Report Status: " . ucfirst($savedReport['report_status']) . "</p>";
            } else {
                echo "<p style='color: orange;'>⚠ Report not found in database</p>";
            }
            
        } else {
            echo "<p style='color: red;'>✗ Report generation failed</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ No active users found for processing</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Report generation test failed: " . $e->getMessage() . "</p>";
    $logger->error("Test report generation failed", ['error' => $e->getMessage()]);
}

echo "<h3>Test Completed</h3>";
echo "<p>Check the logs directory for detailed logging information.</p>";
?>
