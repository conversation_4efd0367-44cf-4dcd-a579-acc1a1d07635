<?php
/**
 * Migration: Change Weekly Processing to Saturday-Friday Weeks
 * 
 * This migration updates the system to use Saturday-Friday weeks instead of Monday-Sunday weeks
 * for all income generation and processing logic.
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "=== Migrating to Saturday-Friday Weekly Processing ===\n";
    echo "Start time: " . date('Y-m-d H:i:s') . "\n\n";
    
    // Update weekly processing day to Friday (5)
    echo "1. Updating weekly processing day to Friday...\n";
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('weekly_processing_day', '5', 'number', 'business', 'Weekly processing day (5=Friday for Saturday-Friday weeks)', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = '5',
        description = 'Weekly processing day (5=Friday for Saturday-Friday weeks)'
    ");
    $stmt->execute();
    echo "✓ Updated weekly processing day to Friday (5)\n";

    // Update weekly matching day for backward compatibility
    echo "2. Updating weekly matching day...\n";
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('weekly_matching_day', '5', 'number', 'business', 'Weekly matching day (5=Friday for Saturday-Friday weeks)', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = '5',
        description = 'Weekly matching day (5=Friday for Saturday-Friday weeks)'
    ");
    $stmt->execute();
    echo "✓ Updated weekly matching day to Friday (5)\n";

    // Update weekly matching time to 11:59 PM
    echo "3. Updating weekly matching time to 11:59 PM...\n";
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('weekly_matching_time', '23:59', 'string', 'business', 'Weekly matching time (23:59 = 11:59 PM)', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = '23:59',
        description = 'Weekly matching time (23:59 = 11:59 PM)'
    ");
    $stmt->execute();
    echo "✓ Updated weekly matching time to 23:59 (11:59 PM)\n";

    // Add new configuration for week definition
    echo "4. Adding week definition configuration...\n";
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('week_definition', 'saturday_friday', 'string', 'business', 'Week definition: saturday_friday (Saturday 12:00 AM to Friday 11:59 PM)', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = 'saturday_friday',
        description = 'Week definition: saturday_friday (Saturday 12:00 AM to Friday 11:59 PM)'
    ");
    $stmt->execute();
    echo "✓ Added week definition configuration\n";

    // Update payment processing day to Saturday (6) - day after week ends
    echo "5. Updating payment processing day to Saturday...\n";
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('payment_processing_day', '6', 'number', 'payment', 'Payment processing day (6=Saturday, day after week ends)', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = '6',
        description = 'Payment processing day (6=Saturday, day after week ends)'
    ");
    $stmt->execute();
    echo "✓ Updated payment processing day to Saturday (6)\n";

    // Add documentation configuration
    echo "6. Adding documentation for new schedule...\n";
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('weekly_schedule_info', 'Week: Saturday 12:00 AM to Friday 11:59 PM | Processing: Friday 11:59 PM | Payments: Saturday', 'string', 'business', 'Weekly schedule information for administrators', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = 'Week: Saturday 12:00 AM to Friday 11:59 PM | Processing: Friday 11:59 PM | Payments: Saturday',
        description = 'Weekly schedule information for administrators'
    ");
    $stmt->execute();
    echo "✓ Added weekly schedule documentation\n";

    // Update cron schedule recommendations
    echo "7. Adding cron schedule recommendations...\n";
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('cron_weekly_matching', '59 23 * * 5', 'string', 'system', 'Recommended cron schedule for weekly matching (Friday 11:59 PM)', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = '59 23 * * 5',
        description = 'Recommended cron schedule for weekly matching (Friday 11:59 PM)'
    ");
    $stmt->execute();
    
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('cron_payment_processing', '0 10 * * 6', 'string', 'system', 'Recommended cron schedule for payment processing (Saturday 10:00 AM)', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = '0 10 * * 6',
        description = 'Recommended cron schedule for payment processing (Saturday 10:00 AM)'
    ");
    $stmt->execute();
    echo "✓ Added cron schedule recommendations\n";

    // Log the migration
    echo "8. Logging migration...\n";
    $stmt = $db->prepare("
        INSERT INTO system_logs (log_type, category, message, context, created_at) 
        VALUES ('info', 'migration', 'Migrated to Saturday-Friday weekly processing', ?, NOW())
    ");
    $context = json_encode([
        'migration' => 'saturday-friday-weeks',
        'changes' => [
            'weekly_processing_day' => '5 (Friday)',
            'weekly_matching_time' => '23:59 (11:59 PM)',
            'payment_processing_day' => '6 (Saturday)',
            'week_definition' => 'saturday_friday'
        ],
        'cron_schedules' => [
            'weekly_matching' => '59 23 * * 5 (Friday 11:59 PM)',
            'payment_processing' => '0 10 * * 6 (Saturday 10:00 AM)'
        ]
    ]);
    $stmt->execute([$context]);
    echo "✓ Migration logged to system_logs\n";

    echo "\n=== Migration Completed Successfully ===\n";
    echo "End time: " . date('Y-m-d H:i:s') . "\n\n";
    
    echo "IMPORTANT NOTES:\n";
    echo "1. Week Definition: Saturday 12:00 AM to Friday 11:59 PM\n";
    echo "2. Income Processing: Friday at 11:59 PM\n";
    echo "3. Payment Processing: Saturday at 10:00 AM\n";
    echo "4. Update your cron jobs:\n";
    echo "   - Weekly Matching: 59 23 * * 5 (Friday 11:59 PM)\n";
    echo "   - Payment Processing: 0 10 * * 6 (Saturday 10:00 AM)\n";
    echo "5. All existing data remains valid - only future processing uses new schedule\n";
    echo "6. Test the new schedule before deploying to production\n\n";

} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
