<?php
/**
 * PV Income Management Dashboard
 * Admin interface for monitoring and managing PV income generation system
 */

session_start();
require_once '../config/Connection.php';
require_once '../config/config.php';
require_once '../includes/EnhancedPVSystem.php';
require_once '../includes/IncomeGenerationEngine.php';
require_once '../includes/PVValidationSystem.php';

// Check admin authentication
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$pvSystem = new EnhancedPVSystem();
$incomeEngine = new IncomeGenerationEngine();
$validator = new PVValidationSystem();

$message = '';
$messageType = '';

// Handle actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'run_weekly_processing':
            try {
                $processingPeriod = $_POST['processing_period'] ?? date('Y-m-d', strtotime('monday last week'));
                $result = $incomeEngine->processAllUsersIncome($processingPeriod, 'weekly');
                
                $message = "Weekly processing completed successfully! Processed {$result['processed_users']} users, distributed ₹" . number_format($result['total_income_distributed'], 2);
                $messageType = 'success';
            } catch (Exception $e) {
                $message = "Weekly processing failed: " . $e->getMessage();
                $messageType = 'danger';
            }
            break;
            
        case 'validate_system':
            try {
                $validation = $validator->validateDataIntegrity();
                if ($validation['healthy']) {
                    $message = "System validation passed - all data integrity checks successful";
                    $messageType = 'success';
                } else {
                    $message = "System validation found issues: " . json_encode($validation['issues']);
                    $messageType = 'warning';
                }
            } catch (Exception $e) {
                $message = "System validation failed: " . $e->getMessage();
                $messageType = 'danger';
            }
            break;
            
        case 'clear_expired_locks':
            try {
                $stmt = $db->prepare("UPDATE pv_processing_locks SET status = 'expired' WHERE expires_at < NOW() AND status = 'active'");
                $stmt->execute();
                $clearedLocks = $stmt->rowCount();
                
                $message = "Cleared {$clearedLocks} expired processing locks";
                $messageType = 'success';
            } catch (Exception $e) {
                $message = "Failed to clear expired locks: " . $e->getMessage();
                $messageType = 'danger';
            }
            break;
    }
}

// Get system statistics
$stats = [];

// Processing status
$stmt = $db->prepare("
    SELECT processing_type, status, COUNT(*) as count
    FROM income_processing_status
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY processing_type, status
");
$stmt->execute();
$processingStats = $stmt->fetchAll();

// Recent income distribution
$stmt = $db->prepare("
    SELECT DATE(created_at) as date, COUNT(*) as users, SUM(income_amount) as total_income
    FROM weekly_income_logs
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY DATE(created_at)
    ORDER BY date DESC
");
$stmt->execute();
$recentIncome = $stmt->fetchAll();

// Active processing locks
$stmt = $db->prepare("
    SELECT lock_type, lock_key, locked_by, locked_at, expires_at
    FROM pv_processing_locks
    WHERE status = 'active'
    ORDER BY locked_at DESC
");
$stmt->execute();
$activeLocks = $stmt->fetchAll();

// System health metrics
$stmt = $db->prepare("
    SELECT 
        (SELECT COUNT(*) FROM pv_transactions WHERE processing_status = 'pending') as pending_transactions,
        (SELECT COUNT(*) FROM pv_usage_tracking WHERE remaining_amount < 0) as negative_remaining,
        (SELECT COUNT(*) FROM income_logs WHERE income_amount < 0) as negative_income,
        (SELECT COUNT(*) FROM system_logs WHERE log_type = 'error' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)) as recent_errors
");
$stmt->execute();
$healthMetrics = $stmt->fetch();

// Performance metrics
$stmt = $db->prepare("
    SELECT metric_type, AVG(metric_value) as avg_value, metric_unit
    FROM pv_performance_metrics
    WHERE recorded_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY metric_type, metric_unit
");
$stmt->execute();
$performanceMetrics = $stmt->fetchAll();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PV Income Management - ShaktiPure Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .health-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .health-good { background-color: #28a745; }
        .health-warning { background-color: #ffc107; }
        .health-danger { background-color: #dc3545; }
        .processing-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-processing { background-color: #d1ecf1; color: #0c5460; }
        .status-failed { background-color: #f8d7da; color: #721c24; }
        .status-pending { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>PV Income System</span>
                    </h6>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#overview">
                                <i class="fas fa-tachometer-alt"></i> Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#processing">
                                <i class="fas fa-cogs"></i> Processing Control
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#monitoring">
                                <i class="fas fa-chart-line"></i> Monitoring
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#validation">
                                <i class="fas fa-shield-alt"></i> System Health
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">PV Income Management Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- System Overview -->
                <section id="overview" class="mb-5">
                    <h3>System Overview</h3>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h5><i class="fas fa-users"></i> Recent Income</h5>
                                <h3>₹<?php echo number_format(array_sum(array_column($recentIncome, 'total_income')), 2); ?></h3>
                                <small>Last 7 days</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h5><i class="fas fa-clock"></i> Pending Transactions</h5>
                                <h3><?php echo number_format($healthMetrics['pending_transactions']); ?></h3>
                                <small>Awaiting processing</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h5><i class="fas fa-exclamation-triangle"></i> Recent Errors</h5>
                                <h3><?php echo $healthMetrics['recent_errors']; ?></h3>
                                <small>Last hour</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h5><i class="fas fa-lock"></i> Active Locks</h5>
                                <h3><?php echo count($activeLocks); ?></h3>
                                <small>Processing locks</small>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Processing Control -->
                <section id="processing" class="mb-5">
                    <h3>Processing Control</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Manual Processing</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST">
                                        <input type="hidden" name="action" value="run_weekly_processing">
                                        <div class="mb-3">
                                            <label for="processing_period" class="form-label">Processing Period (Week Start)</label>
                                            <input type="date" class="form-control" id="processing_period" name="processing_period" 
                                                   value="<?php echo date('Y-m-d', strtotime('monday last week')); ?>">
                                        </div>
                                        <button type="submit" class="btn btn-primary" onclick="return confirm('Are you sure you want to run weekly processing?')">
                                            <i class="fas fa-play"></i> Run Weekly Processing
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>System Maintenance</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" class="mb-3">
                                        <input type="hidden" name="action" value="validate_system">
                                        <button type="submit" class="btn btn-info">
                                            <i class="fas fa-check-circle"></i> Validate System
                                        </button>
                                    </form>
                                    <form method="POST">
                                        <input type="hidden" name="action" value="clear_expired_locks">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-unlock"></i> Clear Expired Locks
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Monitoring Section -->
                <section id="monitoring" class="mb-5">
                    <h3>System Monitoring</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Processing Status</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Type</th>
                                                    <th>Status</th>
                                                    <th>Count</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($processingStats as $stat): ?>
                                                <tr>
                                                    <td><?php echo ucfirst($stat['processing_type']); ?></td>
                                                    <td>
                                                        <span class="processing-status status-<?php echo $stat['status']; ?>">
                                                            <?php echo ucfirst($stat['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo $stat['count']; ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Recent Income Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Users</th>
                                                    <th>Total Income</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recentIncome as $income): ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($income['date'])); ?></td>
                                                    <td><?php echo $income['users']; ?></td>
                                                    <td>₹<?php echo number_format($income['total_income'], 2); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Active Processing Locks</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($activeLocks)): ?>
                                        <p class="text-muted">No active processing locks</p>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Lock Type</th>
                                                        <th>Lock Key</th>
                                                        <th>Locked By</th>
                                                        <th>Locked At</th>
                                                        <th>Expires At</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($activeLocks as $lock): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($lock['lock_type']); ?></td>
                                                        <td><?php echo htmlspecialchars($lock['lock_key']); ?></td>
                                                        <td><?php echo htmlspecialchars($lock['locked_by']); ?></td>
                                                        <td><?php echo date('M d, Y H:i:s', strtotime($lock['locked_at'])); ?></td>
                                                        <td><?php echo date('M d, Y H:i:s', strtotime($lock['expires_at'])); ?></td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- System Health Section -->
                <section id="validation" class="mb-5">
                    <h3>System Health</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Health Indicators</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <span class="health-indicator <?php echo $healthMetrics['pending_transactions'] > 1000 ? 'health-warning' : 'health-good'; ?>"></span>
                                        <strong>Pending Transactions:</strong> <?php echo number_format($healthMetrics['pending_transactions']); ?>
                                    </div>
                                    <div class="mb-3">
                                        <span class="health-indicator <?php echo $healthMetrics['negative_remaining'] > 0 ? 'health-danger' : 'health-good'; ?>"></span>
                                        <strong>Negative Remaining PV:</strong> <?php echo $healthMetrics['negative_remaining']; ?>
                                    </div>
                                    <div class="mb-3">
                                        <span class="health-indicator <?php echo $healthMetrics['negative_income'] > 0 ? 'health-danger' : 'health-good'; ?>"></span>
                                        <strong>Negative Income Records:</strong> <?php echo $healthMetrics['negative_income']; ?>
                                    </div>
                                    <div class="mb-3">
                                        <span class="health-indicator <?php echo $healthMetrics['recent_errors'] > 10 ? 'health-danger' : ($healthMetrics['recent_errors'] > 0 ? 'health-warning' : 'health-good'); ?>"></span>
                                        <strong>Recent Errors:</strong> <?php echo $healthMetrics['recent_errors']; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Performance Metrics (7 days avg)</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($performanceMetrics)): ?>
                                        <p class="text-muted">No performance metrics available</p>
                                    <?php else: ?>
                                        <?php foreach ($performanceMetrics as $metric): ?>
                                        <div class="mb-3">
                                            <strong><?php echo ucwords(str_replace('_', ' ', $metric['metric_type'])); ?>:</strong>
                                            <?php echo number_format($metric['avg_value'], 2); ?> <?php echo $metric['metric_unit']; ?>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
