<?php
/**
 * Public Products Page
 * MLM Binary Plan System
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/FileUpload.php';

// Get all active products
$db = Database::getInstance();
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? 'all';

$whereConditions = ["status = 'active'"];
$params = [];

if ($search) {
    $whereConditions[] = "(name LIKE ? OR description LIKE ? OR product_code LIKE ?)";
    $searchTerm = '%' . $search . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = implode(' AND ', $whereConditions);

$productsStmt = $db->prepare("SELECT * FROM products WHERE {$whereClause} ORDER BY created_at DESC");
$productsStmt->execute($params);
$products = $productsStmt->fetchAll();

$fileUpload = new FileUpload();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-leaf me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.php">Products</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="user/login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center">
                    <h1 class="display-4 fw-bold">Our Products</h1>
                    <p class="lead text-muted">Discover our premium product range</p>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" name="search" 
                                           placeholder="Search products..." 
                                           value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <?php if (!empty($products)): ?>
            <div class="row">
                <?php foreach ($products as $product): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card product-card h-100 shadow-sm" onclick="viewProduct(<?php echo $product['id']; ?>)" style="cursor: pointer;">
                            <div class="card-img-container" style="height: 250px; overflow: hidden;">
                                <?php if ($product['image']): ?>
                                    <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($product['image'])); ?>"
                                         class="card-img-top"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         style="width: 100%; height: 100%; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light d-flex align-items-center justify-content-center h-100">
                                        <i class="fas fa-image fa-4x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                <p class="card-text text-muted mb-3">
                                    <small><i class="fas fa-tag me-1"></i>Code: <?php echo htmlspecialchars($product['product_code']); ?></small>
                                </p>
                                <div class="row mb-3 mt-auto">
                                    <div class="col-6">
                                        <strong class="text-success h5">₹<?php echo number_format($product['price'], 2); ?></strong>
                                        <br><small class="text-muted">Price</small>
                                    </div>
                                    <div class="col-6 text-end">
                                        <span class="badge bg-primary fs-6"><?php echo formatPV($product['pv_value']); ?> PV</span>
                                        <br><small class="text-muted">Point Value</small>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <small class="text-primary">
                                        <i class="fas fa-mouse-pointer me-1"></i>Click to view details
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                        <h4>No Products Found</h4>
                        <p class="text-muted">
                            <?php if ($search): ?>
                                No products match your search criteria. Try different keywords.
                            <?php else: ?>
                                No products are currently available.
                            <?php endif; ?>
                        </p>
                        <?php if ($search): ?>
                            <a href="products.php" class="btn btn-primary">View All Products</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 <?php echo SITE_NAME; ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>Powered by Advanced Shaktipure Industries</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2) !important;
            border-color: #0d6efd;
        }

        .card-img-container {
            position: relative;
            overflow: hidden;
        }

        .card-img-top {
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.08);
        }

        .product-card:hover .card-title {
            color: #0d6efd;
        }

        .product-card:active {
            transform: translateY(-4px);
        }

        /* Add a subtle overlay effect on hover */
        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(13, 110, 253, 0.05), rgba(13, 110, 253, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 1;
        }

        .product-card:hover::before {
            opacity: 1;
        }

        .product-card .card-body {
            position: relative;
            z-index: 2;
        }
    </style>
    
    <script>
        function viewProduct(productId) {
            // Redirect to product detail page
            window.location.href = 'product-detail.php?id=' + productId;
        }
    </script>
</body>
</html>
