# ShaktiPure MLM - Production Setup Guide

## Overview

The enhanced `setup.php` script provides a comprehensive, production-ready setup for the ShaktiPure MLM system. It includes all migrations, security configurations, performance optimizations, and production-level features.

## Features Included

### 🗄️ Complete Database Schema
- **All Core Tables**: Users, Admin, Franchise, Products, Orders, etc.
- **Enhanced PV System**: PV transactions, usage tracking, audit trails
- **Income Processing**: Weekly income logs, processing status, performance metrics
- **Security Features**: Login logs, security events, API tokens
- **Production Tables**: Email queue, cache management, system logs

### 🔧 Production Configurations
- **Enhanced Security**: Password hashing, session management, rate limiting
- **Performance Optimization**: Database indexing, caching, batch processing
- **Monitoring**: System logs, performance metrics, health checks
- **Email System**: SMTP configuration, email queue management
- **API Support**: Token-based authentication, rate limiting

### 📁 Directory Structure
- **Upload Directories**: Products, documents, profile pictures
- **Log Directories**: System logs, security logs, API logs
- **Security Files**: .htaccess files for directory protection
- **Cache & Temp**: Performance optimization directories

### 👥 Default Accounts
- **Admin Account**: Full system administration
- **Franchise Account**: Franchise management
- **Master User**: Root user in binary tree

## Installation Methods

### Method 1: Fresh Installation (Recommended)
```bash
# For new installations
php setup.php
```
or visit `setup.php` in your browser

### Method 2: Upgrade Existing Installation
```bash
# For existing installations
php run-all-migrations.php
```
or visit `run-all-migrations.php` in your browser

## Default Login Credentials

### Admin Panel (`admin/login.php`)
- **Username**: admin
- **Password**: admin123

### Franchise Panel (`franchise/login.php`)
- **Username**: franchise
- **Password**: franchise123

### User Panel (`user/login.php`)
- **Username**: master
- **Password**: master123

⚠️ **IMPORTANT**: Change all default passwords immediately after first login!

## Configuration Categories

### System Settings
- PV conversion rates
- Income capping limits
- Processing batch sizes
- Session timeouts

### Financial Settings
- Withdrawal limits and charges
- Service charges and TDS rates
- Commission rates
- Payment gateway settings

### Security Settings
- Login attempt limits
- Account lockout durations
- Password requirements
- Email verification settings

### Performance Settings
- Cache configuration
- Database optimization
- Batch processing sizes
- Memory limits

## Production Checklist

### 🔒 Security
- [ ] Change all default passwords
- [ ] Configure SSL certificate
- [ ] Set up firewall rules
- [ ] Enable security logging
- [ ] Configure rate limiting

### ⚙️ Configuration
- [ ] Update company information
- [ ] Configure email settings (SMTP)
- [ ] Set up payment gateway
- [ ] Configure backup settings
- [ ] Set timezone and locale

### 📊 Monitoring
- [ ] Set up log monitoring
- [ ] Configure health checks
- [ ] Set up performance monitoring
- [ ] Configure error alerting
- [ ] Set up backup monitoring

### 🔄 Automation
- [ ] Set up cron jobs for income processing
- [ ] Configure automated backups
- [ ] Set up log rotation
- [ ] Configure cache cleanup
- [ ] Set up system maintenance tasks

## Cron Jobs Setup

Add these cron jobs for automated processing:

```bash
# Weekly income processing (every Friday at 11:59 PM)
59 23 * * 5 /usr/bin/php /path/to/your/site/cron/enhanced-weekly-income-processor.php

# Daily maintenance (every day at 2 AM)
0 2 * * * /usr/bin/php /path/to/your/site/cron/daily-maintenance.php

# System health check (every hour)
0 * * * * /usr/bin/php /path/to/your/site/cron/system-health-check.php
```

## Database Optimization

The setup includes optimized database configurations:
- **Proper Indexing**: All tables have appropriate indexes
- **Foreign Key Constraints**: Data integrity enforcement
- **Storage Engine**: InnoDB for ACID compliance
- **Character Set**: UTF8MB4 for full Unicode support

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check `config/database.php` settings
   - Verify MySQL server is running
   - Check user permissions

2. **Directory Permission Errors**
   - Ensure web server has write permissions
   - Check directory ownership
   - Verify .htaccess files are in place

3. **Migration Failures**
   - Run individual migration scripts
   - Check MySQL error logs
   - Verify table dependencies

### Log Files
- **Setup Log**: `logs/setup.log`
- **System Logs**: `logs/system.log`
- **Security Logs**: `logs/security/`
- **Error Logs**: Check web server error logs

## Support

For technical support or questions:
- Check the troubleshooting section
- Review system logs
- Contact system administrator

## Version Information

- **Setup Version**: Production v2.0
- **Database Schema**: Enhanced with all migrations
- **Security Level**: Production-ready
- **Performance**: Optimized for high-load environments
