<?php
/**
 * Test Enhanced Weekly Income System
 * Comprehensive testing of the new features
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/PaymentProcessor.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Get database instance
$db = Database::getInstance();
$config = Config::getInstance();
$pvSystem = new PVSystem();
$paymentProcessor = new PaymentProcessor();

echo "<h2>Enhanced Weekly Income System Test</h2>";
echo "<p>Testing all new features and improvements...</p>";

// Test 1: Database Structure Verification
echo "<h3>1. Database Structure Verification</h3>";

// Check weekly_income_reports table structure
echo "<h4>Weekly Income Reports Table:</h4>";
$reportsColumns = $db->query("DESCRIBE weekly_income_reports")->fetchAll();
$expectedReportsColumns = ['report_id', 'report_number', 'total_users_processed', 'total_users_skipped', 'total_gross_income', 'total_service_charge', 'total_tds_amount'];

foreach ($expectedReportsColumns as $column) {
    $found = false;
    foreach ($reportsColumns as $col) {
        if ($col['Field'] === $column) {
            $found = true;
            break;
        }
    }
    echo "- {$column}: " . ($found ? "✓ Found" : "❌ Missing") . "<br>";
}

// Check weekly_income_logs table structure
echo "<h4>Weekly Income Logs Table:</h4>";
$logsColumns = $db->query("DESCRIBE weekly_income_logs")->fetchAll();
$expectedLogsColumns = ['processing_status', 'payment_status', 'payment_date', 'payment_reference'];

foreach ($expectedLogsColumns as $column) {
    $found = false;
    foreach ($logsColumns as $col) {
        if ($col['Field'] === $column) {
            $found = true;
            break;
        }
    }
    echo "- {$column}: " . ($found ? "✓ Found" : "❌ Missing") . "<br>";
}

// Check unique constraint
echo "<h4>Unique Constraints:</h4>";
$indexes = $db->query("SHOW INDEX FROM weekly_income_logs WHERE Key_name = 'unique_user_week'")->fetchAll();
echo "- unique_user_week constraint: " . (!empty($indexes) ? "✓ Found" : "❌ Missing") . "<br>";

// Test 2: Multiple Reports Per Week
echo "<h3>2. Multiple Reports Per Week Test</h3>";

$testWeekStart = date('Y-m-d', strtotime('monday last week'));
$testWeekEnd = date('Y-m-d', strtotime('sunday last week'));

echo "Testing week: {$testWeekStart} to {$testWeekEnd}<br>";

// Check existing reports for this week
$existingReportsStmt = $db->prepare("SELECT COUNT(*) as count, MAX(report_number) as max_number FROM weekly_income_reports WHERE week_start_date = ?");
$existingReportsStmt->execute([$testWeekStart]);
$existingReports = $existingReportsStmt->fetch();

echo "Existing reports for this week: {$existingReports['count']}<br>";
echo "Highest report number: " . ($existingReports['max_number'] ?? 0) . "<br>";

// Test report ID generation
require_once '../includes/functions.php';
$nextReportNumber = ($existingReports['max_number'] ?? 0) + 1;
$testReportId = generateReportId($testWeekStart, $nextReportNumber);
echo "Next report ID would be: {$testReportId}<br>";

// Test 3: User-Level Duplicate Prevention
echo "<h3>3. User-Level Duplicate Prevention Test</h3>";

// Get a test user
$testUserStmt = $db->query("SELECT user_id FROM users WHERE status = 'active' LIMIT 1");
$testUser = $testUserStmt->fetch();

if ($testUser) {
    $testUserId = $testUser['user_id'];
    echo "Testing with user: {$testUserId}<br>";
    
    // Check if user already has a log for this week
    $existingLogStmt = $db->prepare("SELECT id FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
    $existingLogStmt->execute([$testUserId, $testWeekStart]);
    $existingLog = $existingLogStmt->fetch();
    
    if ($existingLog) {
        echo "✓ User already has income log for this week (ID: {$existingLog['id']})<br>";
        echo "✓ Duplicate prevention working correctly<br>";
    } else {
        echo "ℹ User does not have income log for this week yet<br>";
    }
} else {
    echo "❌ No test users found<br>";
}

// Test 4: Payment System Integration
echo "<h3>4. Payment System Integration Test</h3>";

// Check payment tables exist
$paymentTables = ['payment_batches', 'payment_transactions'];
foreach ($paymentTables as $table) {
    try {
        $db->query("SELECT 1 FROM {$table} LIMIT 1");
        echo "✓ Table {$table} exists<br>";
    } catch (Exception $e) {
        echo "❌ Table {$table} missing<br>";
    }
}

// Test payment configuration
$paymentConfig = [
    'auto_payment_enabled' => $config->get('auto_payment_enabled', false),
    'auto_payment_threshold' => $config->get('auto_payment_threshold', 500),
    'payment_processing_day' => $config->get('payment_processing_day', 6),
    'payment_batch_size' => $config->get('payment_batch_size', 100)
];

echo "Payment Configuration:<br>";
foreach ($paymentConfig as $key => $value) {
    echo "- {$key}: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "<br>";
}

// Test 5: Enhanced Reporting Features
echo "<h3>5. Enhanced Reporting Features Test</h3>";

// Get recent reports with new fields
$recentReportsStmt = $db->query("
    SELECT report_id, report_number, total_users_processed, total_users_earned, total_users_skipped, 
           total_gross_income, total_service_charge, total_tds_amount, total_income_distributed
    FROM weekly_income_reports 
    ORDER BY week_start_date DESC 
    LIMIT 3
");
$recentReports = $recentReportsStmt->fetchAll();

if (!empty($recentReports)) {
    echo "Recent reports with enhanced data:<br>";
    foreach ($recentReports as $report) {
        echo "- Report #{$report['report_number']} (ID: {$report['report_id']})<br>";
        echo "  Processed: {$report['total_users_processed']}, Earned: {$report['total_users_earned']}, Skipped: {$report['total_users_skipped']}<br>";
        echo "  Gross: ₹" . number_format($report['total_gross_income'], 2) . ", Net: ₹" . number_format($report['total_income_distributed'], 2) . "<br>";
    }
} else {
    echo "ℹ No recent reports found<br>";
}

// Test 6: Payment Statistics
echo "<h3>6. Payment Statistics Test</h3>";

if (!empty($recentReports)) {
    $latestReport = $recentReports[0];
    $weekStart = $db->query("SELECT week_start_date FROM weekly_income_reports WHERE report_id = '{$latestReport['report_id']}'")->fetchColumn();
    
    if ($weekStart) {
        $paymentStats = $paymentProcessor->getWeeklyPaymentStats($weekStart);
        
        echo "Payment stats for week {$weekStart}:<br>";
        echo "- Total logs: {$paymentStats['total_logs']}<br>";
        echo "- Paid: {$paymentStats['paid_count']} (₹" . number_format($paymentStats['paid_amount'], 2) . ")<br>";
        echo "- Pending: {$paymentStats['pending_count']} (₹" . number_format($paymentStats['pending_amount'], 2) . ")<br>";
        echo "- Failed: {$paymentStats['failed_count']}<br>";
    }
}

// Test 7: System Performance
echo "<h3>7. System Performance Test</h3>";

$startTime = microtime(true);

// Test query performance for large datasets
$performanceStmt = $db->query("
    SELECT COUNT(*) as total_logs, 
           COUNT(DISTINCT week_start_date) as total_weeks,
           MAX(week_start_date) as latest_week
    FROM weekly_income_logs
");
$performanceStats = $performanceStmt->fetch();

$queryTime = microtime(true) - $startTime;

echo "Performance metrics:<br>";
echo "- Total income logs: {$performanceStats['total_logs']}<br>";
echo "- Total weeks processed: {$performanceStats['total_weeks']}<br>";
echo "- Latest week: {$performanceStats['latest_week']}<br>";
echo "- Query time: " . round($queryTime * 1000, 2) . "ms<br>";

// Test 8: Data Integrity
echo "<h3>8. Data Integrity Test</h3>";

// Check for orphaned records
$orphanedStmt = $db->query("
    SELECT COUNT(*) as count 
    FROM weekly_income_logs wil 
    LEFT JOIN users u ON wil.user_id = u.user_id 
    WHERE u.user_id IS NULL
");
$orphanedCount = $orphanedStmt->fetchColumn();

echo "- Orphaned income logs: " . ($orphanedCount == 0 ? "✓ None found" : "❌ {$orphanedCount} found") . "<br>";

// Check for duplicate user-week combinations
$duplicatesStmt = $db->query("
    SELECT user_id, week_start_date, COUNT(*) as count 
    FROM weekly_income_logs 
    GROUP BY user_id, week_start_date 
    HAVING COUNT(*) > 1
");
$duplicates = $duplicatesStmt->fetchAll();

echo "- Duplicate user-week combinations: " . (empty($duplicates) ? "✓ None found" : "❌ " . count($duplicates) . " found") . "<br>";

echo "<h3>✅ Enhanced Weekly Income System Test Completed</h3>";
echo "<p>Review the results above to ensure all features are working correctly.</p>";

echo "<div style='margin-top: 30px;'>";
echo "<a href='weekly-income-reports.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Weekly Reports</a>";
echo "<a href='payment-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Payment Management</a>";
echo "<a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</div>";
?>
