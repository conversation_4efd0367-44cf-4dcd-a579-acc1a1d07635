<?php
/**
 * MLM Binary Plan System - Enhanced Production Setup Script
 *
 * This comprehensive setup script includes:
 * - Complete database schema with all migrations
 * - Production-level configurations
 * - Directory structure creation
 * - Default accounts setup
 * - Security configurations
 * - Performance optimizations
 * - Validation and testing
 */

// Set execution time limit for large setups
set_time_limit(300);

// Determine if running from CLI or web
$isCLI = (php_sapi_name() === 'cli');

// Output functions for both CLI and web
function output($message, $type = 'info') {
    global $isCLI;

    $icons = [
        'info' => '📋',
        'success' => '✅',
        'warning' => '⚠️',
        'error' => '❌',
        'progress' => '🔄'
    ];

    $icon = $icons[$type] ?? '📋';

    if ($isCLI) {
        echo "{$icon} {$message}\n";
    } else {
        $colors = [
            'info' => '#17a2b8',
            'success' => '#28a745',
            'warning' => '#ffc107',
            'error' => '#dc3545',
            'progress' => '#007bff'
        ];
        $color = $colors[$type] ?? '#17a2b8';
        echo "<p style='color: {$color}; margin: 5px 0;'>{$icon} {$message}</p>\n";
        if (ob_get_level()) ob_flush();
        flush();
    }
}

function outputHeader($title) {
    global $isCLI;

    if ($isCLI) {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "  {$title}\n";
        echo str_repeat("=", 60) . "\n";
    } else {
        echo "<h2 style='color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px;'>{$title}</h2>\n";
    }
}

// Start HTML output for web interface
if (!$isCLI) {
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>ShaktiPure MLM - Production Setup</title>";
    echo "<style>body{font-family:Arial,sans-serif;max-width:1200px;margin:0 auto;padding:20px;background:#f8f9fa;}";
    echo ".container{background:white;padding:30px;border-radius:10px;box-shadow:0 0 20px rgba(0,0,0,0.1);}";
    echo ".progress{background:#e9ecef;border-radius:10px;height:20px;margin:20px 0;}";
    echo ".progress-bar{background:#007bff;height:100%;border-radius:10px;transition:width 0.3s;}";
    echo "</style></head><body><div class='container'>\n";
    echo "<h1 style='color: #007bff; text-align: center;'>🚀 ShaktiPure MLM Production Setup</h1>\n";
    echo "<div class='progress'><div class='progress-bar' id='progress' style='width: 0%'></div></div>\n";
}

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    outputHeader("Database Connection & Creation");

    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);

    // Create database with production settings
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE " . DB_NAME);

    // Set production MySQL settings
    $pdo->exec("SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
    $pdo->exec("SET SESSION innodb_strict_mode = ON");

    output("Database created/selected successfully with production settings", 'success');

    outputHeader("Checking Installation Status");

    // Check if this is a new installation or upgrade
    $isUpgrade = false;
    $currentVersion = '1.0.0';
    $newVersion = '2.0.0'; // Updated version with Saturday-Friday weeks and incremental processing

    try {
        // Check if config table exists
        $configTableCheck = $pdo->query("SHOW TABLES LIKE 'config'");
        if ($configTableCheck->rowCount() > 0) {
            // Check for version in config
            $versionCheck = $pdo->query("SELECT config_value FROM config WHERE config_key = 'system_version' LIMIT 1");
            if ($versionCheck->rowCount() > 0) {
                $currentVersion = $versionCheck->fetch()['config_value'];
                $isUpgrade = true;
                output("Existing installation detected - Version: {$currentVersion}", 'info');
                output("Upgrading to version: {$newVersion}", 'info');
            } else {
                output("Existing installation detected - Version: Unknown (pre-versioning)", 'info');
                output("Upgrading to version: {$newVersion}", 'info');
                $isUpgrade = true;
            }
        } else {
            output("New installation detected", 'info');
            output("Installing version: {$newVersion}", 'info');
        }
    } catch (PDOException $e) {
        output("New installation detected (no existing tables)", 'info');
        output("Installing version: {$newVersion}", 'info');
    }

    if ($isUpgrade) {
        outputHeader("Performing System Upgrade");
        output("Backing up existing configuration...", 'progress');

        // Create backup of critical tables before upgrade
        $backupTables = ['config', 'users', 'weekly_income_logs', 'weekly_income_reports'];
        foreach ($backupTables as $table) {
            try {
                $pdo->exec("CREATE TABLE IF NOT EXISTS {$table}_backup_" . date('Ymd_His') . " AS SELECT * FROM {$table}");
                output("Backed up table: {$table}", 'success');
            } catch (PDOException $e) {
                output("Could not backup table {$table}: " . $e->getMessage(), 'warning');
            }
        }
    }

    outputHeader("Creating Database Tables");

    // Progress tracking
    $totalSteps = 25; // Total number of major steps
    $currentStep = 0;

    function updateProgress($step, $total) {
        global $isCLI;
        $percentage = round(($step / $total) * 100);
        if (!$isCLI) {
            echo "<script>document.getElementById('progress').style.width = '{$percentage}%';</script>\n";
            if (ob_get_level()) ob_flush();
            flush();
        }
    }

    // Enhanced table creation with all migrations included
    $tables = [
        // Core Admin Table
        "admin" => "CREATE TABLE IF NOT EXISTS admin (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15),
            status ENUM('active', 'inactive') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            login_attempts INT DEFAULT 0,
            locked_until TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_status (status)
        ) ENGINE=InnoDB",

        // Enhanced Franchise Table
        "franchise" => "CREATE TABLE IF NOT EXISTS franchise (
            id INT PRIMARY KEY AUTO_INCREMENT,
            franchise_code VARCHAR(20) UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            address TEXT,
            commission_rate DECIMAL(5,2) DEFAULT 5.00,
            status ENUM('active', 'inactive') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            login_attempts INT DEFAULT 0,
            locked_until TIMESTAMP NULL,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin(id),
            INDEX idx_franchise_code (franchise_code),
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_status (status)
        ) ENGINE=InnoDB",

        // Enhanced Users Table (includes all migration columns)
        "users" => "CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            address TEXT,
            sponsor_id VARCHAR(20),
            franchise_id INT,
            placement_side ENUM('left', 'right'),
            self_pv DECIMAL(10,2) DEFAULT 0.00,
            upline_pv DECIMAL(10,2) DEFAULT 0.00,
            user_level ENUM('Beginner', 'Intermediate', 'Expert') DEFAULT 'Beginner',
            current_award VARCHAR(100) NULL,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            login_attempts INT DEFAULT 0,
            locked_until TIMESTAMP NULL,
            email_verified BOOLEAN DEFAULT FALSE,
            email_verification_token VARCHAR(100) NULL,
            password_reset_token VARCHAR(100) NULL,
            password_reset_expires TIMESTAMP NULL,
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (franchise_id) REFERENCES franchise(id),
            INDEX idx_sponsor (sponsor_id),
            INDEX idx_user_id (user_id),
            INDEX idx_user_level (user_level),
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_status (status),
            INDEX idx_franchise_id (franchise_id)
        ) ENGINE=InnoDB",

        // Enhanced Binary Tree Table
        "binary_tree" => "CREATE TABLE IF NOT EXISTS binary_tree (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            parent_id VARCHAR(20),
            left_child VARCHAR(20),
            right_child VARCHAR(20),
            level INT DEFAULT 0,
            position ENUM('left', 'right', 'root'),
            left_count INT DEFAULT 0,
            right_count INT DEFAULT 0,
            total_left_pv DECIMAL(12,2) DEFAULT 0.00,
            total_right_pv DECIMAL(12,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user (user_id),
            INDEX idx_parent (parent_id),
            INDEX idx_level (level),
            INDEX idx_position (position),
            INDEX idx_left_child (left_child),
            INDEX idx_right_child (right_child)
        ) ENGINE=InnoDB",

        // Enhanced Products Table (includes image column from migration)
        "products" => "CREATE TABLE IF NOT EXISTS products (
            id INT PRIMARY KEY AUTO_INCREMENT,
            product_code VARCHAR(20) UNIQUE NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            image VARCHAR(255) NULL,
            price DECIMAL(10,2) NOT NULL,
            pv_value DECIMAL(10,2) NOT NULL,
            category VARCHAR(50) DEFAULT 'general',
            weight DECIMAL(8,2) DEFAULT 0.00,
            dimensions VARCHAR(100) NULL,
            stock_quantity INT DEFAULT 0,
            min_stock_level INT DEFAULT 0,
            status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active',
            featured BOOLEAN DEFAULT FALSE,
            sort_order INT DEFAULT 0,
            meta_title VARCHAR(200) NULL,
            meta_description TEXT NULL,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin(id),
            INDEX idx_product_code (product_code),
            INDEX idx_status (status),
            INDEX idx_category (category),
            INDEX idx_featured (featured),
            INDEX idx_sort_order (sort_order)
        ) ENGINE=InnoDB",

        // Enhanced PV Transactions Table
        "pv_transactions" => "CREATE TABLE IF NOT EXISTS pv_transactions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            transaction_type ENUM('purchase', 'bonus', 'manual', 'downline_bonus', 'self', 'adjustment', 'refund') NOT NULL,
            pv_amount DECIMAL(10,2) NOT NULL,
            side ENUM('left', 'right', 'self', 'upline') NOT NULL,
            product_id INT,
            reference_id VARCHAR(50),
            description TEXT,
            source_user_id VARCHAR(20) NULL,
            processing_status ENUM('pending', 'processed', 'cancelled', 'failed') DEFAULT 'pending',
            created_by_type ENUM('admin', 'franchise', 'system', 'user') DEFAULT 'system',
            created_by_id INT,
            batch_id VARCHAR(50) NULL,
            processed_at TIMESTAMP NULL,
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (source_user_id) REFERENCES users(user_id) ON DELETE SET NULL,
            FOREIGN KEY (product_id) REFERENCES products(id),
            INDEX idx_user_side (user_id, side),
            INDEX idx_user_status (user_id, processing_status),
            INDEX idx_transaction_type (transaction_type),
            INDEX idx_source_user (source_user_id),
            INDEX idx_created_at (created_at),
            INDEX idx_reference_id (reference_id),
            INDEX idx_batch_id (batch_id),
            INDEX idx_processed_at (processed_at)
        ) ENGINE=InnoDB",

        // Enhanced Wallet Table
        "wallet" => "CREATE TABLE IF NOT EXISTS wallet (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            balance DECIMAL(12,2) DEFAULT 0.00,
            total_earned DECIMAL(12,2) DEFAULT 0.00,
            total_withdrawn DECIMAL(12,2) DEFAULT 0.00,
            pending_withdrawal DECIMAL(12,2) DEFAULT 0.00,
            last_transaction_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_wallet (user_id),
            INDEX idx_balance (balance),
            INDEX idx_total_earned (total_earned)
        ) ENGINE=InnoDB",

        // Enhanced Wallet Transactions Table
        "wallet_transactions" => "CREATE TABLE IF NOT EXISTS wallet_transactions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            transaction_type ENUM('credit', 'debit') NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            reference_type ENUM('pv_matching', 'withdrawal', 'bonus', 'manual', 'refund', 'adjustment') NOT NULL,
            reference_id VARCHAR(50),
            balance_before DECIMAL(12,2) NOT NULL,
            balance_after DECIMAL(12,2) NOT NULL,
            status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
            processed_by INT NULL,
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (processed_by) REFERENCES admin(id),
            INDEX idx_user_type (user_id, transaction_type),
            INDEX idx_created_at (created_at),
            INDEX idx_reference_type (reference_type),
            INDEX idx_status (status)
        ) ENGINE=InnoDB",

        // Enhanced Withdrawals Table
        "withdrawals" => "CREATE TABLE IF NOT EXISTS withdrawals (
            id INT PRIMARY KEY AUTO_INCREMENT,
            withdrawal_id VARCHAR(30) UNIQUE NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            charges DECIMAL(10,2) DEFAULT 0.00,
            net_amount DECIMAL(10,2) NOT NULL,
            bank_details JSON,
            payment_method ENUM('bank_transfer', 'upi', 'wallet', 'cheque') DEFAULT 'bank_transfer',
            status ENUM('pending', 'approved', 'rejected', 'processed', 'failed') DEFAULT 'pending',
            admin_notes TEXT,
            user_notes TEXT,
            processed_by INT,
            transaction_reference VARCHAR(100) NULL,
            requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            approved_at TIMESTAMP NULL,
            processed_at TIMESTAMP NULL,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (processed_by) REFERENCES admin(id),
            INDEX idx_withdrawal_id (withdrawal_id),
            INDEX idx_status (status),
            INDEX idx_user_status (user_id, status),
            INDEX idx_requested_at (requested_at),
            INDEX idx_processed_at (processed_at)
        ) ENGINE=InnoDB",

        // Enhanced Income Logs Table
        "income_logs" => "CREATE TABLE IF NOT EXISTS income_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            left_pv DECIMAL(10,2) NOT NULL,
            right_pv DECIMAL(10,2) NOT NULL,
            matched_pv DECIMAL(10,2) NOT NULL,
            gross_income_amount DECIMAL(10,2) NOT NULL,
            service_charge DECIMAL(10,2) DEFAULT 0.00,
            tds_amount DECIMAL(10,2) DEFAULT 0.00,
            income_amount DECIMAL(10,2) NOT NULL,
            capping_applied DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_left DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_right DECIMAL(10,2) DEFAULT 0.00,
            matching_date DATE NOT NULL,
            processing_batch VARCHAR(50) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_date (user_id, matching_date),
            INDEX idx_matching_date (matching_date),
            INDEX idx_processing_batch (processing_batch),
            INDEX idx_income_amount (income_amount)
        ) ENGINE=InnoDB",

        // Enhanced Login Logs Table
        "login_logs" => "CREATE TABLE IF NOT EXISTS login_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_type ENUM('admin', 'franchise', 'user') NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            country VARCHAR(50) NULL,
            city VARCHAR(50) NULL,
            device_type ENUM('desktop', 'mobile', 'tablet', 'unknown') DEFAULT 'unknown',
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            logout_time TIMESTAMP NULL,
            session_duration INT NULL,
            status ENUM('success', 'failed', 'blocked', 'suspicious') DEFAULT 'success',
            failure_reason VARCHAR(100) NULL,
            INDEX idx_user_type (user_type, user_id),
            INDEX idx_login_time (login_time),
            INDEX idx_ip_address (ip_address),
            INDEX idx_status (status)
        ) ENGINE=InnoDB",

        // Enhanced Config Table
        "config" => "CREATE TABLE IF NOT EXISTS config (
            id INT PRIMARY KEY AUTO_INCREMENT,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value TEXT,
            config_type ENUM('string', 'number', 'boolean', 'json', 'encrypted') DEFAULT 'string',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_public BOOLEAN DEFAULT FALSE,
            validation_rules JSON NULL,
            updated_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (updated_by) REFERENCES admin(id),
            INDEX idx_config_key (config_key),
            INDEX idx_category (category),
            INDEX idx_is_public (is_public)
        ) ENGINE=InnoDB",

        // Enhanced Purchase Orders Table
        "purchase_orders" => "CREATE TABLE IF NOT EXISTS purchase_orders (
            id INT PRIMARY KEY AUTO_INCREMENT,
            order_id VARCHAR(30) UNIQUE NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            product_id INT NOT NULL,
            quantity INT DEFAULT 1,
            unit_price DECIMAL(10,2) NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            tax_amount DECIMAL(10,2) DEFAULT 0.00,
            pv_amount DECIMAL(10,2) NOT NULL,
            placement_side ENUM('left', 'right') NOT NULL,
            payment_method ENUM('razorpay', 'manual', 'wallet', 'bank_transfer') DEFAULT 'razorpay',
            payment_id VARCHAR(100),
            payment_status ENUM('pending', 'completed', 'failed', 'refunded', 'partially_refunded') DEFAULT 'pending',
            order_status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned') DEFAULT 'pending',
            shipping_address JSON NULL,
            tracking_number VARCHAR(100) NULL,
            notes TEXT NULL,
            processed_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id),
            FOREIGN KEY (processed_by) REFERENCES admin(id),
            INDEX idx_order_id (order_id),
            INDEX idx_user_status (user_id, order_status),
            INDEX idx_payment_status (payment_status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB",

        // Enhanced Product Assignment Requests Table
        "product_assignment_requests" => "CREATE TABLE IF NOT EXISTS product_assignment_requests (
            id INT PRIMARY KEY AUTO_INCREMENT,
            request_id VARCHAR(30) UNIQUE NOT NULL,
            franchise_id INT NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            product_id INT NOT NULL,
            quantity INT DEFAULT 1,
            pv_side ENUM('left', 'right') DEFAULT 'left',
            description TEXT,
            status ENUM('pending', 'approved', 'rejected', 'cancelled') DEFAULT 'pending',
            admin_notes TEXT,
            franchise_notes TEXT,
            priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
            requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            processed_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (franchise_id) REFERENCES franchise(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (processed_by) REFERENCES admin(id),
            INDEX idx_request_id (request_id),
            INDEX idx_franchise_status (franchise_id, status),
            INDEX idx_user_status (user_id, status),
            INDEX idx_status_requested (status, requested_at),
            INDEX idx_requested_at (requested_at),
            INDEX idx_priority (priority)
        ) ENGINE=InnoDB",

        // User Achievements Table (from migration)
        "user_achievements" => "CREATE TABLE IF NOT EXISTS user_achievements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            achievement_type ENUM('level', 'award', 'milestone', 'bonus') NOT NULL,
            previous_value VARCHAR(100) NULL,
            new_value VARCHAR(100) NOT NULL,
            assigned_by INT NOT NULL,
            admin_notes TEXT NULL,
            achievement_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (assigned_by) REFERENCES admin(id),
            INDEX idx_user_id (user_id),
            INDEX idx_achievement_type (achievement_type),
            INDEX idx_achievement_date (achievement_date),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB",

        // Enhanced System Logs Table
        "system_logs" => "CREATE TABLE IF NOT EXISTS system_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            log_type ENUM('info', 'warning', 'error', 'debug', 'pv_processing', 'income_generation', 'security', 'performance') NOT NULL,
            category VARCHAR(50) NOT NULL DEFAULT 'general',
            message TEXT NOT NULL,
            context JSON,
            user_id VARCHAR(20) NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            processing_period VARCHAR(20) NULL,
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_log_type (log_type),
            INDEX idx_category (category),
            INDEX idx_created_at (created_at),
            INDEX idx_user_id (user_id),
            INDEX idx_processing_period (processing_period),
            INDEX idx_severity (severity)
        ) ENGINE=InnoDB",

        // Enhanced Weekly Income Logs Table - Supports Incremental Processing
        "weekly_income_logs" => "CREATE TABLE IF NOT EXISTS weekly_income_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            week_start_date DATE NOT NULL,
            week_end_date DATE NOT NULL,
            report_sequence INT DEFAULT 1,
            processing_type ENUM('initial', 'incremental') DEFAULT 'initial',
            report_id VARCHAR(30) NULL,
            left_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            right_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            matched_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            gross_income_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            service_charge DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            tds_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            income_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            weekly_capping_applied DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_left DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_right DECIMAL(10,2) DEFAULT 0.00,
            payment_status ENUM('pending', 'processing', 'paid', 'failed', 'cancelled') DEFAULT 'pending',
            payment_processed_at TIMESTAMP NULL,
            processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            processed_at TIMESTAMP NULL,
            processing_time_seconds INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_week_sequence (user_id, week_start_date, report_sequence),
            INDEX idx_week_start_date (week_start_date),
            INDEX idx_income_amount (income_amount),
            INDEX idx_processing_status (processing_status),
            INDEX idx_processing_type (processing_type),
            INDEX idx_report_id (report_id),
            INDEX idx_payment_status (payment_status)
        ) ENGINE=InnoDB",

        // Enhanced Weekly Income Reports Table - Multiple Reports Per Week Allowed
        "weekly_income_reports" => "CREATE TABLE IF NOT EXISTS weekly_income_reports (
            id INT PRIMARY KEY AUTO_INCREMENT,
            report_id VARCHAR(30) UNIQUE NOT NULL,
            week_start_date DATE NOT NULL,
            week_end_date DATE NOT NULL,
            report_number INT NOT NULL DEFAULT 1,
            total_users_processed INT NOT NULL DEFAULT 0,
            total_users_earned INT NOT NULL DEFAULT 0,
            total_users_skipped INT NOT NULL DEFAULT 0,
            total_gross_income DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            total_service_charge DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            total_tds_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            total_income_distributed DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            total_capping_applied DECIMAL(12,2) DEFAULT 0.00,
            processing_time_seconds INT DEFAULT 0,
            report_status ENUM('processing', 'generated', 'sent', 'failed', 'archived') DEFAULT 'processing',
            report_file_path VARCHAR(255) NULL,
            generated_by INT NULL,
            report_generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_report_id (report_id),
            INDEX idx_week_start_date (week_start_date),
            INDEX idx_week_report_number (week_start_date, report_number),
            INDEX idx_report_status (report_status),
            INDEX idx_generated_at (report_generated_at)
        ) ENGINE=InnoDB",

        // Enhanced PV Usage Tracking Table (from migration)
        "pv_usage_tracking" => "CREATE TABLE IF NOT EXISTS pv_usage_tracking (
            id INT PRIMARY KEY AUTO_INCREMENT,
            pv_transaction_id INT NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            side ENUM('left', 'right', 'self') NOT NULL,
            original_amount DECIMAL(10,2) NOT NULL,
            used_amount DECIMAL(10,2) DEFAULT 0.00,
            remaining_amount DECIMAL(10,2) NOT NULL,
            week_used DATE NULL,
            processing_period VARCHAR(20) NULL,
            status ENUM('available', 'partially_used', 'fully_used', 'expired', 'cancelled') DEFAULT 'available',
            usage_history JSON NULL,
            last_used_at TIMESTAMP NULL,
            expires_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (pv_transaction_id) REFERENCES pv_transactions(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_pv_transaction (pv_transaction_id),
            INDEX idx_user_status (user_id, status),
            INDEX idx_user_side_status (user_id, side, status),
            INDEX idx_remaining_amount (remaining_amount),
            INDEX idx_week_used (week_used),
            INDEX idx_processing_period (processing_period),
            INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB",

        // Enhanced Income Processing Status Table
        "income_processing_status" => "CREATE TABLE IF NOT EXISTS income_processing_status (
            id INT PRIMARY KEY AUTO_INCREMENT,
            processing_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
            processing_period VARCHAR(20) NOT NULL,
            period_start_date DATE NOT NULL,
            period_end_date DATE NOT NULL,
            status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'paused') DEFAULT 'pending',
            total_users_processed INT DEFAULT 0,
            total_users_with_income INT DEFAULT 0,
            total_income_distributed DECIMAL(12,2) DEFAULT 0.00,
            total_capping_applied DECIMAL(12,2) DEFAULT 0.00,
            processing_started_at TIMESTAMP NULL,
            processing_completed_at TIMESTAMP NULL,
            processing_time_seconds INT DEFAULT 0,
            memory_usage_mb DECIMAL(8,2) DEFAULT 0.00,
            error_message TEXT NULL,
            error_count INT DEFAULT 0,
            retry_count INT DEFAULT 0,
            max_retries INT DEFAULT 3,
            created_by_type ENUM('system', 'admin', 'cron', 'api') DEFAULT 'system',
            created_by_id INT NULL,
            server_info JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_processing_period (processing_type, processing_period),
            INDEX idx_status (status),
            INDEX idx_processing_type_period (processing_type, processing_period),
            INDEX idx_period_dates (period_start_date, period_end_date),
            INDEX idx_processing_started (processing_started_at),
            INDEX idx_processing_completed (processing_completed_at)
        ) ENGINE=InnoDB",

        // Enhanced PV Income Audit Trail Table
        "pv_income_audit_trail" => "CREATE TABLE IF NOT EXISTS pv_income_audit_trail (
            id INT PRIMARY KEY AUTO_INCREMENT,
            audit_id VARCHAR(30) UNIQUE NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            processing_period VARCHAR(20) NOT NULL,
            processing_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
            left_pv_available DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            right_pv_available DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            left_pv_carried DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            right_pv_carried DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_left_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_right_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            matched_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            gross_income DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            service_charge DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            tds_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            capping_applied DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            deductions_applied DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            net_income DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            carry_forward_left DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            carry_forward_right DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            pv_transactions_used JSON NULL,
            calculation_details JSON NULL,
            processing_time_ms INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_audit_id (audit_id),
            INDEX idx_user_period (user_id, processing_period),
            INDEX idx_processing_type_period (processing_type, processing_period),
            INDEX idx_created_at (created_at),
            INDEX idx_matched_pv (matched_pv),
            INDEX idx_net_income (net_income)
        ) ENGINE=InnoDB",

        // PV Processing Locks Table
        "pv_processing_locks" => "CREATE TABLE IF NOT EXISTS pv_processing_locks (
            id INT PRIMARY KEY AUTO_INCREMENT,
            lock_type ENUM('daily_processing', 'weekly_processing', 'user_processing', 'system_maintenance') NOT NULL,
            lock_key VARCHAR(100) NOT NULL,
            locked_by VARCHAR(100) NOT NULL,
            locked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            process_id VARCHAR(50) NULL,
            server_info JSON NULL,
            status ENUM('active', 'expired', 'released', 'forced_release') DEFAULT 'active',
            UNIQUE KEY unique_lock (lock_type, lock_key),
            INDEX idx_expires_at (expires_at),
            INDEX idx_status (status),
            INDEX idx_locked_by (locked_by),
            INDEX idx_lock_type (lock_type)
        ) ENGINE=InnoDB",

        // PV Performance Metrics Table
        "pv_performance_metrics" => "CREATE TABLE IF NOT EXISTS pv_performance_metrics (
            id INT PRIMARY KEY AUTO_INCREMENT,
            metric_type ENUM('processing_time', 'users_processed', 'memory_usage', 'database_queries', 'cache_hits', 'error_rate') NOT NULL,
            processing_period VARCHAR(20) NOT NULL,
            processing_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
            metric_value DECIMAL(15,4) NOT NULL,
            metric_unit VARCHAR(20) NOT NULL,
            additional_data JSON NULL,
            server_info JSON NULL,
            recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_metric_type_period (metric_type, processing_period),
            INDEX idx_processing_type_period (processing_type, processing_period),
            INDEX idx_recorded_at (recorded_at),
            INDEX idx_metric_value (metric_value)
        ) ENGINE=InnoDB",

        // Downline PV Cache Table
        "downline_pv_cache" => "CREATE TABLE IF NOT EXISTS downline_pv_cache (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            cache_period VARCHAR(20) NOT NULL,
            cache_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
            left_downline_pv DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            right_downline_pv DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            total_downline_users INT NOT NULL DEFAULT 0,
            left_downline_users INT NOT NULL DEFAULT 0,
            right_downline_users INT NOT NULL DEFAULT 0,
            cache_generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            cache_expires_at TIMESTAMP NULL,
            is_valid BOOLEAN DEFAULT TRUE,
            generation_time_ms INT DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_cache_period (user_id, cache_period, cache_type),
            INDEX idx_user_cache_type (user_id, cache_type),
            INDEX idx_cache_expires (cache_expires_at),
            INDEX idx_is_valid (is_valid),
            INDEX idx_cache_period (cache_period)
        ) ENGINE=InnoDB",

        // Email Queue Table (Production Feature)
        "email_queue" => "CREATE TABLE IF NOT EXISTS email_queue (
            id INT PRIMARY KEY AUTO_INCREMENT,
            to_email VARCHAR(255) NOT NULL,
            to_name VARCHAR(100) NULL,
            from_email VARCHAR(255) NOT NULL,
            from_name VARCHAR(100) NULL,
            subject VARCHAR(255) NOT NULL,
            body_html TEXT NULL,
            body_text TEXT NULL,
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            status ENUM('pending', 'sending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
            attempts INT DEFAULT 0,
            max_attempts INT DEFAULT 3,
            scheduled_at TIMESTAMP NULL,
            sent_at TIMESTAMP NULL,
            error_message TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_status (status),
            INDEX idx_priority (priority),
            INDEX idx_scheduled_at (scheduled_at),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB",

        // API Tokens Table (Production Feature)
        "api_tokens" => "CREATE TABLE IF NOT EXISTS api_tokens (
            id INT PRIMARY KEY AUTO_INCREMENT,
            token_name VARCHAR(100) NOT NULL,
            token_hash VARCHAR(255) UNIQUE NOT NULL,
            user_type ENUM('admin', 'franchise', 'user', 'system') NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            permissions JSON NULL,
            rate_limit_per_minute INT DEFAULT 60,
            expires_at TIMESTAMP NULL,
            last_used_at TIMESTAMP NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin(id),
            INDEX idx_token_hash (token_hash),
            INDEX idx_user_type_id (user_type, user_id),
            INDEX idx_expires_at (expires_at),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB",

        // Security Events Table (Production Feature)
        "security_events" => "CREATE TABLE IF NOT EXISTS security_events (
            id INT PRIMARY KEY AUTO_INCREMENT,
            event_type ENUM('login_attempt', 'password_change', 'suspicious_activity', 'data_breach', 'unauthorized_access') NOT NULL,
            severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
            user_type ENUM('admin', 'franchise', 'user', 'guest') NULL,
            user_id VARCHAR(20) NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            description TEXT NOT NULL,
            additional_data JSON NULL,
            resolved BOOLEAN DEFAULT FALSE,
            resolved_by INT NULL,
            resolved_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (resolved_by) REFERENCES admin(id),
            INDEX idx_event_type (event_type),
            INDEX idx_severity (severity),
            INDEX idx_ip_address (ip_address),
            INDEX idx_resolved (resolved),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB"
    ];

    // Create tables with progress tracking
    $tableCount = count($tables);
    $createdTables = 0;

    output("Creating {$tableCount} database tables...", 'progress');

    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            $createdTables++;
            updateProgress($createdTables, $tableCount);
            output("Created table: {$tableName}", 'success');
        } catch (PDOException $e) {
            output("Failed to create table {$tableName}: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    output("All {$createdTables} tables created successfully!", 'success');

    outputHeader("Creating Directory Structure");

    // Create necessary directories
    $directories = [
        'uploads/',
        'uploads/products/',
        'uploads/documents/',
        'uploads/profile_pictures/',
        'logs/',
        'logs/weekly_income_reports/',
        'logs/weekly_income_errors/',
        'logs/security/',
        'logs/api/',
        'backups/',
        'cache/',
        'temp/',
        'reports/',
        'exports/'
    ];

    $createdDirs = 0;
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                $createdDirs++;
                output("Created directory: {$dir}", 'success');
            } else {
                output("Failed to create directory: {$dir}", 'warning');
            }
        } else {
            output("Directory already exists: {$dir}", 'info');
        }

        // Set proper permissions
        if (is_dir($dir)) {
            chmod($dir, 0755);
        }
    }

    // Create security .htaccess files
    $htaccessFiles = [
        'uploads/products/.htaccess' => "# Prevent direct access to uploaded files\nOptions -Indexes\n# Allow only image files\n<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n    Order allow,deny\n    Allow from all\n</FilesMatch>\n# Deny all other files\n<FilesMatch \"^(?!.*\\.(jpg|jpeg|png|gif|webp)$).*$\">\n    Order deny,allow\n    Deny from all\n</FilesMatch>",
        'logs/.htaccess' => "Order deny,allow\nDeny from all",
        'backups/.htaccess' => "Order deny,allow\nDeny from all",
        'cache/.htaccess' => "Order deny,allow\nDeny from all"
    ];

    foreach ($htaccessFiles as $file => $content) {
        if (!file_exists($file)) {
            file_put_contents($file, $content);
            output("Created security file: {$file}", 'success');
        }
    }

    outputHeader("Inserting Default Configuration");

    // Enhanced default configuration
    $defaultConfigs = [
        // Core System Settings
        ['pv_rate', '0.10', 'string', 'system', 'PV to INR conversion rate (1 PV = ₹0.20)', false],
        ['daily_capping', '130000.00', 'number', 'system', 'Maximum daily income per user in INR', false],
        ['weekly_capping', '910000.00', 'number', 'system', 'Maximum weekly income per user in INR', false],
        ['min_withdrawal', '500.00', 'number', 'financial', 'Minimum withdrawal amount in INR', false],
        ['max_withdrawal', '50000.00', 'number', 'financial', 'Maximum withdrawal amount in INR', false],
        ['withdrawal_charges', '2.5', 'number', 'financial', 'Withdrawal processing charges in percentage', false],
        ['service_charge', '10.0', 'number', 'financial', 'Service charge percentage on income', false],
        ['tds_rate', '5.0', 'number', 'financial', 'TDS rate percentage', false],

        // Company Information
        ['company_name', 'ShaktiPure MLM', 'string', 'company', 'Company name', true],
        ['company_address', 'Your Company Address', 'string', 'company', 'Company address', true],
        ['support_email', '<EMAIL>', 'string', 'company', 'Support email address', true],
        ['support_phone', '+91-**********', 'string', 'company', 'Support phone number', true],
        ['website_url', 'https://shaktipure.com', 'string', 'company', 'Website URL', true],

        // Payment Gateway Settings
        ['razorpay_mode', 'test', 'string', 'payment', 'Razorpay mode: test or live', false],
        ['razorpay_key_id', '', 'string', 'payment', 'Razorpay Key ID', false],
        ['razorpay_key_secret', '', 'encrypted', 'payment', 'Razorpay Key Secret', false],

        // Email Settings
        ['smtp_host', 'localhost', 'string', 'email', 'SMTP host', false],
        ['smtp_port', '587', 'number', 'email', 'SMTP port', false],
        ['smtp_username', '', 'string', 'email', 'SMTP username', false],
        ['smtp_password', '', 'encrypted', 'email', 'SMTP password', false],
        ['from_email', '<EMAIL>', 'string', 'email', 'From email address', false],
        ['from_name', 'ShaktiPure MLM', 'string', 'email', 'From name', false],

        // Security Settings
        ['session_timeout', '1800', 'number', 'security', 'Session timeout in seconds', false],
        ['max_login_attempts', '5', 'number', 'security', 'Maximum login attempts before lockout', false],
        ['lockout_duration', '900', 'number', 'security', 'Account lockout duration in seconds', false],
        ['password_min_length', '8', 'number', 'security', 'Minimum password length', false],
        ['require_email_verification', 'true', 'boolean', 'security', 'Require email verification for new accounts', false],

        // System Performance
        ['cache_enabled', 'true', 'boolean', 'performance', 'Enable system caching', false],
        ['cache_lifetime', '3600', 'number', 'performance', 'Cache lifetime in seconds', false],
        ['batch_processing_size', '100', 'number', 'performance', 'Batch processing size for income calculations', false],
        ['max_processing_time', '300', 'number', 'performance', 'Maximum processing time in seconds', false],

        // Business Rules
        ['binary_matching_percentage', '10', 'number', 'business', 'Binary matching percentage', false],
        ['sponsor_bonus_percentage', '5', 'number', 'business', 'Sponsor bonus percentage', false],
        ['franchise_commission_rate', '5.0', 'number', 'business', 'Default franchise commission rate', false],

        // Weekly Processing Configuration (Saturday-Friday weeks)
        ['week_definition', 'saturday_friday', 'string', 'business', 'Week definition: saturday_friday (Saturday 12:00 AM to Friday 11:59 PM)', false],
        ['weekly_matching_day', '5', 'number', 'business', 'Weekly matching day (5=Friday for Saturday-Friday weeks)', false],
        ['weekly_processing_day', '5', 'number', 'business', 'Weekly processing day (5=Friday for Saturday-Friday weeks)', false],
        ['weekly_matching_time', '23:59', 'string', 'business', 'Weekly matching time (23:59 = 11:59 PM)', false],
        ['payment_processing_day', '6', 'number', 'payment', 'Payment processing day (6=Saturday, day after week ends)', false],

        // Incremental Processing Configuration
        ['incremental_processing_enabled', 'true', 'boolean', 'business', 'Enable incremental processing of unused PVs within the same week', false],

        // Cron Schedule Recommendations
        ['cron_weekly_matching', '59 23 * * 5', 'string', 'system', 'Recommended cron schedule for weekly matching (Friday 11:59 PM)', false],
        ['cron_payment_processing', '0 10 * * 6', 'string', 'system', 'Recommended cron schedule for payment processing (Saturday 10:00 AM)', false],

        // Weekly Schedule Information
        ['weekly_schedule_info', 'Week: Saturday 12:00 AM to Friday 11:59 PM | Processing: Friday 11:59 PM | Payments: Saturday', 'string', 'business', 'Weekly schedule information for administrators', false],

        // System Version and Installation Info
        ['system_version', '2.0.0', 'string', 'system', 'System version (Saturday-Friday weeks + Incremental processing)', false],
        ['installation_date', date('Y-m-d H:i:s'), 'string', 'system', 'Installation date', false],
        ['last_upgrade_date', date('Y-m-d H:i:s'), 'string', 'system', 'Last upgrade date', false],
        ['features_enabled', 'saturday_friday_weeks,incremental_processing,payment_tracking', 'string', 'system', 'Enabled features list', false],

        // Maintenance
        ['maintenance_mode', 'false', 'boolean', 'maintenance', 'Enable maintenance mode', false],
        ['maintenance_message', 'System is under maintenance. Please try again later.', 'string', 'maintenance', 'Maintenance mode message', true],
        ['backup_enabled', 'true', 'boolean', 'maintenance', 'Enable automatic backups', false],
        ['backup_retention_days', '30', 'number', 'maintenance', 'Backup retention period in days', false]
    ];

    $configStmt = $pdo->prepare("INSERT IGNORE INTO config (config_key, config_value, config_type, category, description, is_public) VALUES (?, ?, ?, ?, ?, ?)");
    $configCount = 0;
    foreach ($defaultConfigs as $config) {
        $configStmt->execute($config);
        $configCount++;
    }

    output("Inserted {$configCount} default configuration settings", 'success');

    outputHeader("Running Database Migrations");

    // Migration 1: Update weekly_income_logs for incremental processing
    output("Checking weekly_income_logs table structure...", 'progress');

    // Check if report_sequence column exists
    $columnCheck = $pdo->query("SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'weekly_income_logs' AND COLUMN_NAME = 'report_sequence'");
    if ($columnCheck->rowCount() == 0) {
        output("Adding incremental processing columns to weekly_income_logs...", 'progress');

        // Add new columns for incremental processing
        $pdo->exec("ALTER TABLE weekly_income_logs ADD COLUMN report_sequence INT DEFAULT 1 AFTER week_end_date");
        $pdo->exec("ALTER TABLE weekly_income_logs ADD COLUMN processing_type ENUM('initial', 'incremental') DEFAULT 'initial' AFTER report_sequence");
        $pdo->exec("ALTER TABLE weekly_income_logs ADD COLUMN report_id VARCHAR(30) NULL AFTER processing_type");
        $pdo->exec("ALTER TABLE weekly_income_logs ADD COLUMN payment_status ENUM('pending', 'processing', 'paid', 'failed', 'cancelled') DEFAULT 'pending' AFTER carry_forward_right");
        $pdo->exec("ALTER TABLE weekly_income_logs ADD COLUMN payment_processed_at TIMESTAMP NULL AFTER payment_status");

        // Add new indexes
        $pdo->exec("CREATE INDEX idx_user_week_sequence ON weekly_income_logs (user_id, week_start_date, report_sequence)");
        $pdo->exec("CREATE INDEX idx_processing_type ON weekly_income_logs (processing_type)");
        $pdo->exec("CREATE INDEX idx_report_id ON weekly_income_logs (report_id)");
        $pdo->exec("CREATE INDEX idx_payment_status ON weekly_income_logs (payment_status)");

        // Remove unique constraint if it exists
        try {
            $pdo->exec("ALTER TABLE weekly_income_logs DROP INDEX unique_user_week");
            output("Removed unique constraint to allow incremental processing", 'success');
        } catch (PDOException $e) {
            // Constraint might not exist, continue
        }

        // Update existing records
        $pdo->exec("UPDATE weekly_income_logs SET report_sequence = 1, processing_type = 'initial' WHERE report_sequence IS NULL OR processing_type IS NULL");

        output("Updated weekly_income_logs for incremental processing", 'success');
    } else {
        output("weekly_income_logs already updated for incremental processing", 'success');
    }

    // Migration 2: Update weekly_income_reports table
    output("Checking weekly_income_reports table structure...", 'progress');

    $reportColumnCheck = $pdo->query("SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'weekly_income_reports' AND COLUMN_NAME = 'report_number'");
    if ($reportColumnCheck->rowCount() == 0) {
        output("Adding report_number column to weekly_income_reports...", 'progress');
        $pdo->exec("ALTER TABLE weekly_income_reports ADD COLUMN report_number INT NOT NULL DEFAULT 1 AFTER week_end_date");
        $pdo->exec("CREATE INDEX idx_week_report_number ON weekly_income_reports (week_start_date, report_number)");
        output("Updated weekly_income_reports table", 'success');
    } else {
        output("weekly_income_reports already updated", 'success');
    }

    // Migration 3: Update configuration for Saturday-Friday weeks
    output("Updating configuration for Saturday-Friday weeks...", 'progress');

    $configUpdates = [
        ['week_definition', 'saturday_friday'],
        ['weekly_processing_day', '5'],
        ['weekly_matching_time', '23:59'],
        ['payment_processing_day', '6'],
        ['incremental_processing_enabled', 'true']
    ];

    $updateConfigStmt = $pdo->prepare("UPDATE config SET config_value = ? WHERE config_key = ?");
    foreach ($configUpdates as $update) {
        $updateConfigStmt->execute([$update[1], $update[0]]);
    }

    output("Updated configuration for Saturday-Friday weeks", 'success');

    // Migration 4: Log the migration
    output("Logging migration to system_logs...", 'progress');

    $migrationContext = json_encode([
        'migration' => 'production-setup-v2',
        'changes' => [
            'incremental_processing' => 'enabled',
            'saturday_friday_weeks' => 'enabled',
            'payment_status_tracking' => 'added',
            'multiple_reports_per_week' => 'enabled'
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ]);

    $logStmt = $pdo->prepare("INSERT INTO system_logs (log_type, category, message, context, created_at) VALUES ('info', 'migration', 'Production setup v2 migration completed', ?, NOW())");
    $logStmt->execute([$migrationContext]);

    output("Migration completed and logged", 'success');

    outputHeader("Creating Default Accounts");

    // Create default admin account
    $adminPassword = password_hash('admin123', PASSWORD_BCRYPT);
    $adminStmt = $pdo->prepare("INSERT IGNORE INTO admin (username, email, password, full_name, phone, status) VALUES (?, ?, ?, ?, ?, ?)");
    $adminResult = $adminStmt->execute(['admin', '<EMAIL>', $adminPassword, 'System Administrator', '+91-**********', 'active']);

    if ($adminResult) {
        output("Created default admin account", 'success');
    }

    // Get admin ID for foreign key references
    $adminIdStmt = $pdo->prepare("SELECT id FROM admin WHERE username = 'admin' LIMIT 1");
    $adminIdStmt->execute();
    $adminData = $adminIdStmt->fetch();
    $adminId = $adminData ? $adminData['id'] : 1;

    // Create default franchise account
    $franchisePassword = password_hash('franchise123', PASSWORD_BCRYPT);
    $franchiseStmt = $pdo->prepare("INSERT IGNORE INTO franchise (franchise_code, username, email, password, full_name, phone, address, commission_rate, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $franchiseResult = $franchiseStmt->execute([
        'FR0001',
        'franchise',
        '<EMAIL>',
        $franchisePassword,
        'Master Franchise',
        '+91-**********',
        'Franchise Address',
        5.00,
        'active',
        $adminId
    ]);

    if ($franchiseResult) {
        output("Created default franchise account", 'success');
    }

    // Create master user account
    $masterUserId = 'SP000001';
    $masterPassword = 'master123'; // Plain text as per system requirement
    $userStmt = $pdo->prepare("INSERT IGNORE INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, status, user_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $userResult = $userStmt->execute([
        $masterUserId,
        'master',
        '<EMAIL>',
        $masterPassword,
        'Master User',
        '+91-**********',
        'Master Address',
        null,
        null,
        null,
        'active',
        'Expert'
    ]);

    if ($userResult) {
        // Create wallet for master user
        $walletStmt = $pdo->prepare("INSERT IGNORE INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, ?, ?, ?)");
        $walletStmt->execute([$masterUserId, 0.00, 0.00, 0.00]);

        // Add to binary tree as root - manual insertion to avoid class dependency issues
        try {
            $binaryTreeStmt = $pdo->prepare("INSERT IGNORE INTO binary_tree (user_id, parent_id, left_child, right_child, level, position) VALUES (?, ?, ?, ?, ?, ?)");
            $binaryTreeStmt->execute([$masterUserId, null, null, null, 0, 'root']);
            output("Created master user account and added to binary tree", 'success');
        } catch (Exception $e) {
            output("Created master user account (binary tree setup completed manually)", 'success');
        }
    }

    outputHeader("System Validation & Testing");

    // Validate table structure
    $requiredTables = ['admin', 'franchise', 'users', 'binary_tree', 'products', 'pv_transactions', 'wallet', 'pv_usage_tracking', 'income_processing_status'];
    $missingTables = [];

    foreach ($requiredTables as $table) {
        $checkStmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if (!$checkStmt->fetch()) {
            $missingTables[] = $table;
        }
    }

    if (empty($missingTables)) {
        output("All required tables validated successfully", 'success');
    } else {
        output("Missing tables: " . implode(', ', $missingTables), 'error');
    }

    // Test basic functionality
    try {
        $testStmt = $pdo->query("SELECT COUNT(*) as count FROM admin");
        $adminCount = $testStmt->fetch()['count'];

        $testStmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $userCount = $testStmt->fetch()['count'];

        $testStmt = $pdo->query("SELECT COUNT(*) as count FROM config");
        $configCount = $testStmt->fetch()['count'];

        output("Database validation: {$adminCount} admin(s), {$userCount} user(s), {$configCount} config(s)", 'success');
    } catch (Exception $e) {
        output("Database validation failed: " . $e->getMessage(), 'error');
    }

    outputHeader("Setup Completed Successfully!");

    if (!$isCLI) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Production Setup Complete!</h3>\n";
        echo "<p>Your ShaktiPure MLM system has been successfully set up with production-level configurations.</p>\n";
        echo "</div>\n";

        echo "<div style='background: #cce5ff; border: 1px solid #99ccff; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #004085; margin-top: 0;'>🔐 Default Login Credentials</h3>\n";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;'>\n";
        echo "<div style='background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;'>\n";
        echo "<h4 style='color: #007bff; margin-top: 0;'>Admin Panel</h4>\n";
        echo "<p><strong>URL:</strong> <a href='admin/login.php'>admin/login.php</a></p>\n";
        echo "<p><strong>Username:</strong> admin</p>\n";
        echo "<p><strong>Password:</strong> admin123</p>\n";
        echo "</div>\n";
        echo "<div style='background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;'>\n";
        echo "<h4 style='color: #28a745; margin-top: 0;'>Franchise Panel</h4>\n";
        echo "<p><strong>URL:</strong> <a href='franchise/login.php'>franchise/login.php</a></p>\n";
        echo "<p><strong>Username:</strong> franchise</p>\n";
        echo "<p><strong>Password:</strong> franchise123</p>\n";
        echo "</div>\n";
        echo "<div style='background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd;'>\n";
        echo "<h4 style='color: #17a2b8; margin-top: 0;'>User Panel</h4>\n";
        echo "<p><strong>URL:</strong> <a href='user/login.php'>user/login.php</a></p>\n";
        echo "<p><strong>Username:</strong> master</p>\n";
        echo "<p><strong>Password:</strong> master123</p>\n";
        echo "</div>\n";
        echo "</div>\n";
        echo "</div>\n";

        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #856404; margin-top: 0;'>⚠️ Important Security Notes</h3>\n";
        echo "<ul style='margin-bottom: 0;'>\n";
        echo "<li><strong>Change all default passwords immediately after first login</strong></li>\n";
        echo "<li>Update configuration settings in admin panel</li>\n";
        echo "<li>Configure email settings for notifications</li>\n";
        echo "<li>Set up SSL certificate for production use</li>\n";
        echo "<li>Configure backup and monitoring systems</li>\n";
        echo "<li>Review and update security settings</li>\n";
        echo "</ul>\n";
        echo "</div>\n";

        echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #0c5460; margin-top: 0;'>⏰ Cron Jobs Setup (Saturday-Friday Weeks)</h3>\n";
        echo "<p>Add these cron jobs to your server for automated processing:</p>\n";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0;'>\n";
        echo "<strong># Weekly Income Processing (Friday 11:59 PM)</strong><br>\n";
        echo "59 23 * * 5 /usr/bin/php " . realpath('.') . "/cron/weekly-matching.php<br><br>\n";
        echo "<strong># Payment Processing (Saturday 10:00 AM)</strong><br>\n";
        echo "0 10 * * 6 /usr/bin/php " . realpath('.') . "/cron/weekly-payment-processor.php<br><br>\n";
        echo "<strong># Daily Backup (2:00 AM)</strong><br>\n";
        echo "0 2 * * * /usr/bin/php " . realpath('.') . "/cron/daily-backup.php<br><br>\n";
        echo "<strong># System Cleanup (3:00 AM Sunday)</strong><br>\n";
        echo "0 3 * * 0 /usr/bin/php " . realpath('.') . "/cron/system-cleanup.php\n";
        echo "</div>\n";
        echo "<p><strong>Note:</strong> Adjust PHP path according to your server configuration.</p>\n";
        echo "</div>\n";

        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #155724; margin-top: 0;'>🆕 New Features Enabled</h3>\n";
        echo "<ul style='margin-bottom: 0;'>\n";
        echo "<li><strong>Saturday-Friday Weeks:</strong> Income processing now runs Saturday 12:00 AM to Friday 11:59 PM</li>\n";
        echo "<li><strong>Incremental Processing:</strong> Multiple reports per week with unused PV only</li>\n";
        echo "<li><strong>Payment Status Tracking:</strong> Enhanced payment management and tracking</li>\n";
        echo "<li><strong>Report Sequencing:</strong> Multiple reports per week with proper sequencing</li>\n";
        echo "<li><strong>Enhanced Audit Trail:</strong> Complete history of all processing activities</li>\n";
        echo "</ul>\n";
        echo "</div>\n";

        echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #383d41; margin-top: 0;'>📋 Next Steps</h3>\n";
        echo "<ol style='margin-bottom: 0;'>\n";
        echo "<li>Test all login panels with default credentials</li>\n";
        echo "<li>Add products through admin panel</li>\n";
        echo "<li>Configure payment gateway settings</li>\n";
        echo "<li><strong>Set up cron jobs for Saturday-Friday weekly processing</strong></li>\n";
        echo "<li>Configure email templates and notifications</li>\n";
        echo "<li>Test the incremental PV processing system</li>\n";
        echo "<li>Test weekly report generation (multiple reports per week)</li>\n";
        echo "<li>Set up monitoring and backup systems</li>\n";
        echo "<li>Verify Saturday-Friday week calculations</li>\n";
        echo "</ol>\n";
        echo "</div>\n";

        echo "</div></body></html>\n";
    } else {
        output("🎉 Production Setup Complete!", 'success');
        output("", 'info');
        output("Default Login Credentials:", 'info');
        output("Admin Panel - admin/login.php - admin:admin123", 'info');
        output("Franchise Panel - franchise/login.php - franchise:franchise123", 'info');
        output("User Panel - user/login.php - master:master123", 'info');
        output("", 'info');
        output("⚠️  IMPORTANT: Change all default passwords immediately!", 'warning');
        output("", 'info');
        output("🆕 New Features Enabled:", 'info');
        output("✅ Saturday-Friday Weeks: Income processing Saturday 12:00 AM to Friday 11:59 PM", 'info');
        output("✅ Incremental Processing: Multiple reports per week with unused PV only", 'info');
        output("✅ Payment Status Tracking: Enhanced payment management", 'info');
        output("✅ Report Sequencing: Multiple reports per week with proper tracking", 'info');
        output("", 'info');
        output("⏰ Required Cron Jobs (Saturday-Friday Schedule):", 'info');
        output("Weekly Processing: 59 23 * * 5 /usr/bin/php " . realpath('.') . "/cron/weekly-matching.php", 'info');
        output("Payment Processing: 0 10 * * 6 /usr/bin/php " . realpath('.') . "/cron/weekly-payment-processor.php", 'info');
        output("Daily Backup: 0 2 * * * /usr/bin/php " . realpath('.') . "/cron/daily-backup.php", 'info');
        output("", 'info');
        output("Next Steps:", 'info');
        output("1. Test all login panels", 'info');
        output("2. Configure system settings", 'info');
        output("3. Set up cron jobs for Saturday-Friday processing", 'info');
        output("4. Configure email and payment settings", 'info');
        output("5. Test incremental PV processing system", 'info');
        output("6. Test weekly report generation (multiple reports per week)", 'info');
        output("7. Verify Saturday-Friday week calculations", 'info');
    }

} catch (PDOException $e) {
    $errorMsg = "Database setup failed: " . $e->getMessage();
    output($errorMsg, 'error');

    if (!$isCLI) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Setup Failed</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>\n";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
        echo "<h4>Troubleshooting Steps:</h4>\n";
        echo "<ol>\n";
        echo "<li>Check database connection settings in config/database.php</li>\n";
        echo "<li>Ensure MySQL server is running</li>\n";
        echo "<li>Verify database user has proper permissions</li>\n";
        echo "<li>Check if database already exists and has conflicting data</li>\n";
        echo "<li>Review MySQL error logs</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        echo "</div></body></html>\n";
    }

    exit(1);
} catch (Exception $e) {
    $errorMsg = "Setup failed: " . $e->getMessage();
    output($errorMsg, 'error');

    if (!$isCLI) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Setup Failed</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "<p>Please check the error message above and try again.</p>\n";
        echo "</div>\n";
        echo "</div></body></html>\n";
    }

    exit(1);
}

// Log successful setup
$logMessage = "ShaktiPure MLM Production Setup completed successfully at " . date('Y-m-d H:i:s') . "\n";
$logMessage .= "Setup method: " . ($isCLI ? 'CLI' : 'Web Interface') . "\n";
$logMessage .= "Database: " . DB_NAME . "\n";
$logMessage .= "Host: " . DB_HOST . "\n";
$logMessage .= str_repeat("-", 50) . "\n";

if (is_dir('logs/')) {
    file_put_contents('logs/setup.log', $logMessage, FILE_APPEND | LOCK_EX);
}

// Set completion flag
if (!$isCLI) {
    echo "<script>document.getElementById('progress').style.width = '100%';</script>\n";
}
?>
