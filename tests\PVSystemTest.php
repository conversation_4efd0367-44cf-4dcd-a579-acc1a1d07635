<?php
/**
 * Comprehensive PV System Test Suite
 * Tests for Enhanced PV Income Generation System
 * 
 * Features:
 * - Unit tests for core functionality
 * - Integration tests for complete workflows
 * - Data integrity validation
 * - Performance testing
 * - Edge case handling
 * - Duplicate prevention testing
 */

require_once __DIR__ . '/../config/Connection.php';
require_once __DIR__ . '/../includes/EnhancedPVSystem.php';
require_once __DIR__ . '/../includes/IncomeGenerationEngine.php';
require_once __DIR__ . '/../includes/PVValidationSystem.php';
require_once __DIR__ . '/../includes/BinaryTree.php';

class PVSystemTest {
    private $db;
    private $pvSystem;
    private $incomeEngine;
    private $validator;
    private $binaryTree;
    private $testResults;
    private $testUserId;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->pvSystem = new EnhancedPVSystem();
        $this->incomeEngine = new IncomeGenerationEngine();
        $this->validator = new PVValidationSystem();
        $this->binaryTree = new BinaryTree();
        $this->testResults = [];
        $this->testUserId = 'TEST' . time() . rand(1000, 9999);
    }
    
    /**
     * Run all tests
     */
    public function runAllTests() {
        echo "Starting PV System Test Suite...\n";
        echo str_repeat("=", 50) . "\n";
        
        try {
            // Setup test environment
            $this->setupTestEnvironment();
            
            // Run test categories
            $this->runBasicFunctionalityTests();
            $this->runDuplicatePreventionTests();
            $this->runDataIntegrityTests();
            $this->runPerformanceTests();
            $this->runEdgeCaseTests();
            $this->runIntegrationTests();
            
            // Cleanup test environment
            $this->cleanupTestEnvironment();
            
            // Generate test report
            $this->generateTestReport();
            
        } catch (Exception $e) {
            echo "Test suite failed with error: " . $e->getMessage() . "\n";
            $this->cleanupTestEnvironment();
            return false;
        }
        
        return $this->getOverallResult();
    }
    
    /**
     * Setup test environment
     */
    private function setupTestEnvironment() {
        echo "Setting up test environment...\n";

        // Clean up any existing locks for this user
        $this->cleanupProcessingLocks();

        // Create test user
        $stmt = $this->db->prepare("
            INSERT INTO users (user_id, username, email, password, full_name, phone, status)
            VALUES (?, ?, ?, ?, ?, ?, 'active')
        ");

        $stmt->execute([
            $this->testUserId,
            'test_user_' . time(),
            'test' . time() . '@example.com',
            password_hash('test123', PASSWORD_BCRYPT),
            'Test User',
            '+1234567890'
        ]);

        // Create test binary tree node
        $this->binaryTree->addRoot($this->testUserId);

        echo "Test environment setup complete.\n\n";
    }

    /**
     * Clean up processing locks
     */
    private function cleanupProcessingLocks() {
        try {
            // Clean up any existing locks
            $this->db->prepare("DELETE FROM pv_processing_locks WHERE lock_key = ? OR locked_by LIKE '%test%'")->execute([$this->testUserId]);
            $this->db->prepare("UPDATE pv_processing_locks SET status = 'expired' WHERE status = 'active' AND expires_at < NOW()")->execute();
        } catch (Exception $e) {
            // Ignore errors during cleanup
        }
    }
    
    /**
     * Run basic functionality tests
     */
    private function runBasicFunctionalityTests() {
        echo "Running Basic Functionality Tests...\n";
        
        // Test 1: Add PV Transaction
        $this->runTest('Add PV Transaction', function() {
            $transactionId = $this->pvSystem->addPV($this->testUserId, 100.00, 'left', 'purchase');
            return $transactionId > 0;
        });
        
        // Test 2: Get Available PV
        $this->runTest('Get Available PV', function() {
            $availablePV = $this->pvSystem->getAvailablePV($this->testUserId);
            return $availablePV['left_pv'] == 100.00;
        });
        
        // Test 3: PV Usage Tracking
        $this->runTest('PV Usage Tracking', function() {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM pv_usage_tracking WHERE user_id = ?");
            $stmt->execute([$this->testUserId]);
            $result = $stmt->fetch();
            return $result['count'] > 0;
        });
        
        // Test 4: Input Validation
        $this->runTest('Input Validation', function() {
            try {
                $this->pvSystem->addPV('', 100.00, 'left', 'purchase');
                return false; // Should have thrown exception
            } catch (Exception $e) {
                return true; // Exception expected
            }
        });
        
        echo "Basic Functionality Tests completed.\n\n";
    }
    
    /**
     * Run duplicate prevention tests
     */
    private function runDuplicatePreventionTests() {
        echo "Running Duplicate Prevention Tests...\n";
        
        // Test 1: Prevent Duplicate Income Processing
        $this->runTest('Prevent Duplicate Income Processing', function() {
            $processingPeriod = date('Y-m-d', strtotime('+10 days')); // Use future date to avoid conflicts

            // Process income first time
            $result1 = $this->pvSystem->processUserIncome($this->testUserId, $processingPeriod, 'daily');

            // Try to process again
            $result2 = $this->pvSystem->processUserIncome($this->testUserId, $processingPeriod, 'daily');

            return isset($result2['already_processed']) && $result2['already_processed'] === true;
        });
        
        // Test 2: PV Usage Tracking Consistency
        $this->runTest('PV Usage Tracking Consistency', function() {
            // Add more PV
            $this->pvSystem->addPV($this->testUserId, 50.00, 'right', 'purchase');
            
            // Check that usage tracking is consistent
            $stmt = $this->db->prepare("
                SELECT SUM(used_amount + remaining_amount) as total_tracked, 
                       SUM(original_amount) as total_original
                FROM pv_usage_tracking 
                WHERE user_id = ?
            ");
            $stmt->execute([$this->testUserId]);
            $result = $stmt->fetch();
            
            return abs($result['total_tracked'] - $result['total_original']) < 0.01;
        });
        
        // Test 3: FIFO PV Usage
        $this->runTest('FIFO PV Usage', function() {
            // Add multiple PV transactions
            $this->pvSystem->addPV($this->testUserId, 30.00, 'left', 'purchase');
            $this->pvSystem->addPV($this->testUserId, 20.00, 'left', 'purchase');

            // Process some income to use PV
            $processingPeriod = date('Y-m-d', strtotime('+11 days')); // Use different date
            $this->pvSystem->processUserIncome($this->testUserId, $processingPeriod, 'daily');

            // Check that oldest PV was used first
            $stmt = $this->db->prepare("
                SELECT * FROM pv_usage_tracking
                WHERE user_id = ? AND side = 'left'
                ORDER BY created_at ASC
            ");
            $stmt->execute([$this->testUserId]);
            $records = $stmt->fetchAll();

            // First record should have some usage
            return count($records) > 0 && $records[0]['used_amount'] > 0;
        });
        
        echo "Duplicate Prevention Tests completed.\n\n";
    }
    
    /**
     * Run data integrity tests
     */
    private function runDataIntegrityTests() {
        echo "Running Data Integrity Tests...\n";
        
        // Test 1: Validate System Integrity
        $this->runTest('System Data Integrity', function() {
            $validation = $this->validator->validateDataIntegrity($this->testUserId);
            return $validation['healthy'];
        });
        
        // Test 2: Transaction Consistency
        $this->runTest('Transaction Consistency', function() {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as untracked_count
                FROM pv_transactions pt
                LEFT JOIN pv_usage_tracking put ON pt.id = put.pv_transaction_id
                WHERE pt.user_id = ? AND put.id IS NULL AND pt.processing_status = 'processed'
            ");
            $stmt->execute([$this->testUserId]);
            $result = $stmt->fetch();
            
            return $result['untracked_count'] == 0;
        });
        
        // Test 3: No Negative Values
        $this->runTest('No Negative Values', function() {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as negative_count
                FROM pv_usage_tracking
                WHERE user_id = ? AND remaining_amount < 0
            ");
            $stmt->execute([$this->testUserId]);
            $result = $stmt->fetch();
            
            return $result['negative_count'] == 0;
        });
        
        echo "Data Integrity Tests completed.\n\n";
    }
    
    /**
     * Run performance tests
     */
    private function runPerformanceTests() {
        echo "Running Performance Tests...\n";
        
        // Test 1: Bulk PV Addition Performance
        $this->runTest('Bulk PV Addition Performance', function() {
            $startTime = microtime(true);
            
            // Add 100 PV transactions
            for ($i = 0; $i < 100; $i++) {
                $this->pvSystem->addPV($this->testUserId, 1.00, 'left', 'purchase');
            }
            
            $endTime = microtime(true);
            $duration = $endTime - $startTime;
            
            // Should complete within 10 seconds
            return $duration < 10.0;
        });
        
        // Test 2: Income Processing Performance
        $this->runTest('Income Processing Performance', function() {
            $startTime = microtime(true);

            $processingPeriod = date('Y-m-d', strtotime('+12 days')); // Use different date
            $this->pvSystem->processUserIncome($this->testUserId, $processingPeriod, 'daily');

            $endTime = microtime(true);
            $duration = $endTime - $startTime;

            // Should complete within 5 seconds
            return $duration < 5.0;
        });
        
        echo "Performance Tests completed.\n\n";
    }
    
    /**
     * Run edge case tests
     */
    private function runEdgeCaseTests() {
        echo "Running Edge Case Tests...\n";
        
        // Test 1: Zero PV Amount
        $this->runTest('Zero PV Amount Handling', function() {
            try {
                $this->pvSystem->addPV($this->testUserId, 0.00, 'left', 'purchase');
                return false; // Should have thrown exception
            } catch (Exception $e) {
                return true;
            }
        });
        
        // Test 2: Very Large PV Amount
        $this->runTest('Large PV Amount Handling', function() {
            try {
                $this->pvSystem->addPV($this->testUserId, 999999.99, 'left', 'purchase');
                return true; // Should handle large amounts
            } catch (Exception $e) {
                return false;
            }
        });
        
        // Test 3: Invalid User ID
        $this->runTest('Invalid User ID Handling', function() {
            try {
                $this->pvSystem->addPV('INVALID_USER', 100.00, 'left', 'purchase');
                return false; // Should have thrown exception
            } catch (Exception $e) {
                return true;
            }
        });
        
        echo "Edge Case Tests completed.\n\n";
    }
    
    /**
     * Run integration tests
     */
    private function runIntegrationTests() {
        echo "Running Integration Tests...\n";
        
        // Test 1: Complete Income Generation Workflow
        $this->runTest('Complete Income Generation Workflow', function() {
            // Add PV to both sides
            $this->pvSystem->addPV($this->testUserId, 100.00, 'left', 'purchase');
            $this->pvSystem->addPV($this->testUserId, 80.00, 'right', 'purchase');

            // Process income
            $processingPeriod = date('Y-m-d', strtotime('+13 days')); // Use different date
            $result = $this->pvSystem->processUserIncome($this->testUserId, $processingPeriod, 'daily');

            // Should have matched 80 PV (minimum of left and right)
            return $result['matched_pv'] == 80.00 && $result['net_income'] > 0;
        });
        
        echo "Integration Tests completed.\n\n";
    }
    
    /**
     * Run a single test
     */
    private function runTest($testName, $testFunction) {
        try {
            $startTime = microtime(true);
            $result = $testFunction();
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds

            $this->testResults[] = [
                'name' => $testName,
                'result' => $result ? 'PASS' : 'FAIL',
                'duration' => $duration
            ];

            $status = $result ? '✓ PASS' : '✗ FAIL';
            echo "  {$status} - {$testName} ({$duration}ms)\n";

        } catch (Exception $e) {
            $this->testResults[] = [
                'name' => $testName,
                'result' => 'ERROR',
                'error' => $e->getMessage(),
                'duration' => 0
            ];

            echo "  ✗ ERROR - {$testName}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Cleanup test environment
     */
    private function cleanupTestEnvironment() {
        echo "Cleaning up test environment...\n";

        try {
            // Delete test data in reverse order of dependencies
            $this->db->prepare("DELETE FROM pv_income_audit_trail WHERE user_id = ?")->execute([$this->testUserId]);
            $this->db->prepare("DELETE FROM weekly_income_logs WHERE user_id = ?")->execute([$this->testUserId]);
            $this->db->prepare("DELETE FROM income_logs WHERE user_id = ?")->execute([$this->testUserId]);
            $this->db->prepare("DELETE FROM pv_usage_tracking WHERE user_id = ?")->execute([$this->testUserId]);
            $this->db->prepare("DELETE FROM pv_transactions WHERE user_id = ?")->execute([$this->testUserId]);
            $this->db->prepare("DELETE FROM wallet_transactions WHERE user_id = ?")->execute([$this->testUserId]);
            $this->db->prepare("DELETE FROM wallet WHERE user_id = ?")->execute([$this->testUserId]);
            $this->db->prepare("DELETE FROM binary_tree WHERE user_id = ?")->execute([$this->testUserId]);
            $this->db->prepare("DELETE FROM users WHERE user_id = ?")->execute([$this->testUserId]);

            echo "Test environment cleanup complete.\n\n";

        } catch (Exception $e) {
            echo "Warning: Cleanup failed - " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Generate test report
     */
    private function generateTestReport() {
        echo str_repeat("=", 50) . "\n";
        echo "TEST REPORT\n";
        echo str_repeat("=", 50) . "\n";

        $totalTests = count($this->testResults);
        $passedTests = count(array_filter($this->testResults, function($test) {
            return $test['result'] === 'PASS';
        }));
        $failedTests = count(array_filter($this->testResults, function($test) {
            return $test['result'] === 'FAIL';
        }));
        $errorTests = count(array_filter($this->testResults, function($test) {
            return $test['result'] === 'ERROR';
        }));

        $totalDuration = array_sum(array_column($this->testResults, 'duration'));

        echo "Total Tests: {$totalTests}\n";
        echo "Passed: {$passedTests}\n";
        echo "Failed: {$failedTests}\n";
        echo "Errors: {$errorTests}\n";
        echo "Total Duration: {$totalDuration}ms\n";
        echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 2) . "%\n";

        if ($failedTests > 0 || $errorTests > 0) {
            echo "\nFAILED/ERROR TESTS:\n";
            echo str_repeat("-", 30) . "\n";

            foreach ($this->testResults as $test) {
                if ($test['result'] !== 'PASS') {
                    echo "- {$test['name']}: {$test['result']}";
                    if (isset($test['error'])) {
                        echo " - {$test['error']}";
                    }
                    echo "\n";
                }
            }
        }

        // Save detailed report to file
        $reportFile = __DIR__ . '/test_report_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents($reportFile, json_encode([
            'timestamp' => date('Y-m-d H:i:s'),
            'summary' => [
                'total' => $totalTests,
                'passed' => $passedTests,
                'failed' => $failedTests,
                'errors' => $errorTests,
                'success_rate' => round(($passedTests / $totalTests) * 100, 2),
                'total_duration_ms' => $totalDuration
            ],
            'tests' => $this->testResults
        ], JSON_PRETTY_PRINT));

        echo "\nDetailed report saved to: {$reportFile}\n";
        echo str_repeat("=", 50) . "\n";
    }

    /**
     * Get overall test result
     */
    private function getOverallResult() {
        $failedTests = count(array_filter($this->testResults, function($test) {
            return $test['result'] !== 'PASS';
        }));

        return $failedTests === 0;
    }
}

// Test runner script
if (php_sapi_name() === 'cli') {
    echo "PV System Test Suite\n";
    echo "==================\n\n";

    try {
        $testSuite = new PVSystemTest();
        $success = $testSuite->runAllTests();

        if ($success) {
            echo "\n🎉 All tests passed successfully!\n";
            exit(0);
        } else {
            echo "\n❌ Some tests failed. Please review the report above.\n";
            exit(1);
        }

    } catch (Exception $e) {
        echo "\n💥 Test suite crashed: " . $e->getMessage() . "\n";
        echo $e->getTraceAsString() . "\n";
        exit(2);
    }
} else {
    echo "This test suite can only be run from the command line.\n";
    echo "Usage: php tests/PVSystemTest.php\n";
}
