<?php
/**
 * Payment Management - Admin Panel
 * Manage automatic payments for weekly income
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PaymentProcessor.php';
require_once '../config/config.php';
require_once '../includes/WeeklyDateHelper.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Get database instance
$db = Database::getInstance();
$config = Config::getInstance();
$paymentProcessor = new PaymentProcessor();

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'process_payments') {
        $weekStart = $_POST['week_start'] ?? '';
        
        if ($weekStart) {
            try {
                $result = $paymentProcessor->processWeeklyPayments($weekStart);
                
                if ($result) {
                    $message = "Payment processing completed! Processed {$result['processed']} payments. Successful: {$result['successful']}, Failed: {$result['failed']}. Total amount: ₹" . number_format($result['total_amount'], 2);
                    $messageType = 'success';
                } else {
                    $message = "Payment processing failed. Please check the logs.";
                    $messageType = 'danger';
                }
            } catch (Exception $e) {
                $message = "Error processing payments: " . $e->getMessage();
                $messageType = 'danger';
            }
        }
    }
    
    if ($action === 'update_config') {
        try {
            $configs = [
                'auto_payment_enabled' => isset($_POST['auto_payment_enabled']),
                'auto_payment_threshold' => floatval($_POST['auto_payment_threshold']),
                'payment_processing_day' => intval($_POST['payment_processing_day']),
                'payment_processing_time' => $_POST['payment_processing_time'],
                'payment_batch_size' => intval($_POST['payment_batch_size'])
            ];
            
            foreach ($configs as $key => $value) {
                $config->set($key, $value);
            }
            
            $message = "Payment configuration updated successfully!";
            $messageType = 'success';
        } catch (Exception $e) {
            $message = "Error updating configuration: " . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get recent payment batches
$batchesStmt = $db->query("
    SELECT * FROM payment_batches 
    ORDER BY created_at DESC 
    LIMIT 10
");
$recentBatches = $batchesStmt->fetchAll();

// Get payment statistics for recent weeks
$statsStmt = $db->query("
    SELECT 
        wil.week_start_date,
        COUNT(*) as total_logs,
        COUNT(CASE WHEN wil.payment_status = 'paid' THEN 1 END) as paid_count,
        COUNT(CASE WHEN wil.payment_status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN wil.payment_status = 'failed' THEN 1 END) as failed_count,
        SUM(CASE WHEN wil.payment_status = 'paid' THEN wil.income_amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN wil.payment_status = 'pending' THEN wil.income_amount ELSE 0 END) as pending_amount
    FROM weekly_income_logs wil
    WHERE wil.income_amount > 0 
    AND wil.week_start_date >= DATE_SUB(CURDATE(), INTERVAL 8 WEEK)
    GROUP BY wil.week_start_date
    ORDER BY wil.week_start_date DESC
");
$weeklyStats = $statsStmt->fetchAll();

// Get current configuration
$currentConfig = [
    'auto_payment_enabled' => $config->get('auto_payment_enabled', false),
    'auto_payment_threshold' => $config->get('auto_payment_threshold', 500),
    'payment_processing_day' => $config->get('payment_processing_day', 6),
    'payment_processing_time' => $config->get('payment_processing_time', '10:00'),
    'payment_batch_size' => $config->get('payment_batch_size', 100)
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Management - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
                <!-- Page Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2><i class="fas fa-credit-card me-2"></i>Payment Management</h2>
                            <div>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#processPaymentsModal">
                                    <i class="fas fa-play me-1"></i>Process Payments
                                </button>
                                <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#configModal">
                                    <i class="fas fa-cog me-1"></i>Configuration
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Configuration Status -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Payment Configuration</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Auto Payment</small>
                                        <div class="h6">
                                            <?php if ($currentConfig['auto_payment_enabled']): ?>
                                                <span class="badge bg-success">Enabled</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Disabled</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Minimum Threshold</small>
                                        <div class="h6">₹<?php echo number_format($currentConfig['auto_payment_threshold'], 2); ?></div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <small class="text-muted">Processing Day</small>
                                        <div class="h6">
                                            <?php 
                                            $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                                            echo $days[$currentConfig['payment_processing_day']];
                                            ?>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Processing Time</small>
                                        <div class="h6"><?php echo $currentConfig['payment_processing_time']; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Quick Stats</h6>
                                <?php if (!empty($weeklyStats)): ?>
                                    <?php $latestWeek = $weeklyStats[0]; ?>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">Latest Week</small>
                                            <div class="h6"><?php echo date('M d, Y', strtotime($latestWeek['week_start_date'])); ?></div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Pending Payments</small>
                                            <div class="h6">
                                                <?php echo $latestWeek['pending_count']; ?>
                                                <small class="text-muted">(₹<?php echo number_format($latestWeek['pending_amount'], 0); ?>)</small>
                                            </div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No payment data available</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Weekly Payment Statistics -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Weekly Payment Statistics</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($weeklyStats)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Payment Data</h5>
                                <p class="text-muted">No weekly income logs found for payment processing.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Week</th>
                                            <th>Total Logs</th>
                                            <th>Paid</th>
                                            <th>Pending</th>
                                            <th>Failed</th>
                                            <th>Paid Amount</th>
                                            <th>Pending Amount</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($weeklyStats as $week): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo date('M d, Y', strtotime($week['week_start_date'])); ?></strong>
                                                </td>
                                                <td><?php echo $week['total_logs']; ?></td>
                                                <td>
                                                    <span class="badge bg-success"><?php echo $week['paid_count']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning"><?php echo $week['pending_count']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-danger"><?php echo $week['failed_count']; ?></span>
                                                </td>
                                                <td>₹<?php echo number_format($week['paid_amount'], 2); ?></td>
                                                <td>₹<?php echo number_format($week['pending_amount'], 2); ?></td>
                                                <td>
                                                    <?php if ($week['pending_count'] > 0): ?>
                                                        <button class="btn btn-sm btn-outline-primary process-week-btn" 
                                                                data-week="<?php echo $week['week_start_date']; ?>">
                                                            <i class="fas fa-play"></i> Process
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="text-muted">Complete</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
    </div>

    <!-- Process Payments Modal -->
    <div class="modal fade" id="processPaymentsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Process Weekly Payments</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="process_payments">
                        <div class="mb-3">
                            <label for="week_start" class="form-label">Week Start Date</label>
                            <input type="date" class="form-control" id="week_start" name="week_start"
                                   value="<?php echo WeeklyDateHelper::getPreviousWeek()['start']; ?>" required>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This will process payments for all eligible income logs in the selected week.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Process Payments</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Configuration Modal -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Payment Configuration</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_config">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="auto_payment_enabled"
                                               name="auto_payment_enabled" <?php echo $currentConfig['auto_payment_enabled'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="auto_payment_enabled">
                                            Enable Automatic Payments
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="auto_payment_threshold" class="form-label">Minimum Payment Threshold (₹)</label>
                                    <input type="number" class="form-control" id="auto_payment_threshold"
                                           name="auto_payment_threshold" step="0.01"
                                           value="<?php echo $currentConfig['auto_payment_threshold']; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="payment_batch_size" class="form-label">Batch Size</label>
                                    <input type="number" class="form-control" id="payment_batch_size"
                                           name="payment_batch_size" min="1" max="1000"
                                           value="<?php echo $currentConfig['payment_batch_size']; ?>" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_processing_day" class="form-label">Processing Day</label>
                                    <select class="form-control" id="payment_processing_day" name="payment_processing_day" required>
                                        <?php
                                        $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                                        foreach ($days as $index => $day): ?>
                                            <option value="<?php echo $index; ?>" <?php echo ($currentConfig['payment_processing_day'] == $index) ? 'selected' : ''; ?>>
                                                <?php echo $day; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="payment_processing_time" class="form-label">Processing Time</label>
                                    <input type="time" class="form-control" id="payment_processing_time"
                                           name="payment_processing_time"
                                           value="<?php echo $currentConfig['payment_processing_time']; ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Changes will take effect immediately. Automatic payments will run according to the schedule you set.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Configuration</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Handle process week buttons
        document.querySelectorAll('.process-week-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const week = this.dataset.week;
                document.getElementById('week_start').value = week;
                new bootstrap.Modal(document.getElementById('processPaymentsModal')).show();
            });
        });
    </script>
</body>
</html>
