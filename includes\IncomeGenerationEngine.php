<?php
/**
 * Income Generation Engine
 * MLM Binary Plan System - Production Level Income Processing
 * 
 * Features:
 * - Batch processing for large user bases
 * - Complete downline income calculation
 * - Duplicate prevention with comprehensive tracking
 * - Performance optimization with caching
 * - Comprehensive error handling and recovery
 * - Detailed audit trails and monitoring
 */

require_once __DIR__ . '/../config/Connection.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/EnhancedPVSystem.php';
require_once __DIR__ . '/BinaryTree.php';

class IncomeGenerationEngine {
    private $db;
    private $config;
    private $pvSystem;
    private $binaryTree;
    private $logger;
    
    // Processing configuration
    const BATCH_SIZE = 100;
    const MAX_PROCESSING_TIME = 3600; // 1 hour
    const MEMORY_LIMIT_MB = 512;
    const MAX_RETRY_ATTEMPTS = 3;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
        $this->pvSystem = new EnhancedPVSystem();
        $this->binaryTree = new BinaryTree();
        $this->initializeLogger();
        
        // Set memory and time limits
        ini_set('memory_limit', self::MEMORY_LIMIT_MB . 'M');
        set_time_limit(self::MAX_PROCESSING_TIME);
    }
    
    /**
     * Initialize logging system
     */
    private function initializeLogger() {
        $this->logger = [
            'info' => function($message, $context = []) {
                $this->logMessage('info', 'income_generation', $message, $context);
            },
            'error' => function($message, $context = []) {
                $this->logMessage('error', 'income_generation', $message, $context);
            },
            'debug' => function($message, $context = []) {
                $this->logMessage('debug', 'income_generation', $message, $context);
            }
        ];
    }
    
    /**
     * Process income for all users in a given period
     */
    public function processAllUsersIncome($processingPeriod, $processingType = 'weekly') {
        $startTime = microtime(true);
        $processId = uniqid('income_proc_');
        
        try {
            // Acquire global processing lock
            if (!$this->acquireGlobalProcessingLock($processingType, $processingPeriod)) {
                throw new Exception("Unable to acquire global processing lock for {$processingType} processing");
            }
            
            // Check if processing already completed
            if ($this->isProcessingCompleted($processingPeriod, $processingType)) {
                $this->logger['info']("Processing already completed for period", [
                    'processing_period' => $processingPeriod,
                    'processing_type' => $processingType
                ]);
                return ['status' => 'already_completed'];
            }
            
            // Initialize processing status
            $processingStatusId = $this->initializeProcessingStatus($processingPeriod, $processingType);
            
            // Get all active users
            $users = $this->getActiveUsers();
            $totalUsers = count($users);
            
            $this->logger['info']("Starting income processing", [
                'processing_period' => $processingPeriod,
                'processing_type' => $processingType,
                'total_users' => $totalUsers,
                'process_id' => $processId
            ]);
            
            // Process users in batches
            $processed = 0;
            $usersWithIncome = 0;
            $totalIncomeDistributed = 0;
            $totalCappingApplied = 0;
            $errors = [];
            
            $batches = array_chunk($users, self::BATCH_SIZE);
            
            foreach ($batches as $batchIndex => $batch) {
                $batchResult = $this->processBatch($batch, $processingPeriod, $processingType, $batchIndex + 1);
                
                $processed += $batchResult['processed'];
                $usersWithIncome += $batchResult['users_with_income'];
                $totalIncomeDistributed += $batchResult['total_income'];
                $totalCappingApplied += $batchResult['total_capping'];
                $errors = array_merge($errors, $batchResult['errors']);
                
                // Update processing status
                $this->updateProcessingStatus($processingStatusId, $processed, $usersWithIncome, $totalIncomeDistributed, $totalCappingApplied);
                
                // Memory cleanup
                gc_collect_cycles();
                
                $this->logger['debug']("Batch processed", [
                    'batch_index' => $batchIndex + 1,
                    'batch_size' => count($batch),
                    'processed_so_far' => $processed,
                    'memory_usage' => memory_get_usage(true)
                ]);
            }
            
            // Complete processing
            $this->completeProcessingStatus($processingStatusId, $processed, $usersWithIncome, $totalIncomeDistributed, $totalCappingApplied);
            
            // Generate summary report
            $this->generateProcessingSummaryReport($processingPeriod, $processingType, $processed, $usersWithIncome, $totalIncomeDistributed, $totalCappingApplied, $errors);
            
            $processingTime = microtime(true) - $startTime;
            
            $result = [
                'status' => 'completed',
                'processing_period' => $processingPeriod,
                'processing_type' => $processingType,
                'total_users' => $totalUsers,
                'processed_users' => $processed,
                'users_with_income' => $usersWithIncome,
                'total_income_distributed' => $totalIncomeDistributed,
                'total_capping_applied' => $totalCappingApplied,
                'processing_time_seconds' => round($processingTime, 2),
                'errors_count' => count($errors),
                'process_id' => $processId
            ];
            
            $this->logger['info']("Income processing completed successfully", $result);
            
            return $result;
            
        } catch (Exception $e) {
            $this->logger['error']("Income processing failed", [
                'processing_period' => $processingPeriod,
                'processing_type' => $processingType,
                'error' => $e->getMessage(),
                'process_id' => $processId
            ]);
            
            // Mark processing as failed
            if (isset($processingStatusId)) {
                $this->markProcessingAsFailed($processingStatusId, $e->getMessage());
            }
            
            throw $e;
            
        } finally {
            // Always release the global lock
            $this->releaseGlobalProcessingLock($processingType, $processingPeriod);
        }
    }
    
    /**
     * Process a batch of users
     */
    private function processBatch($users, $processingPeriod, $processingType, $batchNumber) {
        $batchStartTime = microtime(true);
        $processed = 0;
        $usersWithIncome = 0;
        $totalIncome = 0;
        $totalCapping = 0;
        $errors = [];
        
        foreach ($users as $userId) {
            $retryCount = 0;
            $success = false;
            
            while ($retryCount < self::MAX_RETRY_ATTEMPTS && !$success) {
                try {
                    $result = $this->pvSystem->processUserIncome($userId, $processingPeriod, $processingType);
                    
                    if (!isset($result['already_processed'])) {
                        $processed++;
                        
                        if ($result['net_income'] > 0) {
                            $usersWithIncome++;
                            $totalIncome += $result['net_income'];
                            $totalCapping += $result['capping_applied'];
                        }
                    }
                    
                    $success = true;
                    
                } catch (Exception $e) {
                    $retryCount++;
                    
                    if ($retryCount >= self::MAX_RETRY_ATTEMPTS) {
                        $errors[] = [
                            'user_id' => $userId,
                            'error' => $e->getMessage(),
                            'retry_attempts' => $retryCount
                        ];
                        
                        $this->logger['error']("Failed to process user after retries", [
                            'user_id' => $userId,
                            'error' => $e->getMessage(),
                            'retry_attempts' => $retryCount
                        ]);
                    } else {
                        // Wait before retry
                        usleep(100000); // 100ms
                    }
                }
            }
        }
        
        $batchTime = microtime(true) - $batchStartTime;
        
        $this->logger['debug']("Batch processing completed", [
            'batch_number' => $batchNumber,
            'users_in_batch' => count($users),
            'processed' => $processed,
            'users_with_income' => $usersWithIncome,
            'total_income' => $totalIncome,
            'errors' => count($errors),
            'processing_time' => round($batchTime, 2)
        ]);
        
        return [
            'processed' => $processed,
            'users_with_income' => $usersWithIncome,
            'total_income' => $totalIncome,
            'total_capping' => $totalCapping,
            'errors' => $errors
        ];
    }
    
    /**
     * Get all active users for processing
     */
    private function getActiveUsers() {
        $stmt = $this->db->prepare("SELECT user_id FROM users WHERE status = 'active' ORDER BY user_id");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Acquire global processing lock
     */
    private function acquireGlobalProcessingLock($processingType, $processingPeriod) {
        $lockKey = "{$processingType}_{$processingPeriod}";
        $expiresAt = date('Y-m-d H:i:s', time() + self::MAX_PROCESSING_TIME);
        $processId = getmypid() . '_' . uniqid();

        try {
            $stmt = $this->db->prepare("
                INSERT INTO pv_processing_locks (lock_type, lock_key, locked_by, expires_at, process_id, status)
                VALUES (?, ?, ?, ?, ?, 'active')
            ");

            $stmt->execute(["{$processingType}_processing", $lockKey, 'income_engine', $expiresAt, $processId]);
            return true;

        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Release global processing lock
     */
    private function releaseGlobalProcessingLock($processingType, $processingPeriod) {
        $lockKey = "{$processingType}_{$processingPeriod}";

        $stmt = $this->db->prepare("
            UPDATE pv_processing_locks
            SET status = 'released'
            WHERE lock_type = ? AND lock_key = ? AND status = 'active'
        ");

        $stmt->execute(["{$processingType}_processing", $lockKey]);
    }

    /**
     * Check if processing already completed
     */
    private function isProcessingCompleted($processingPeriod, $processingType) {
        $stmt = $this->db->prepare("
            SELECT id FROM income_processing_status
            WHERE processing_type = ? AND processing_period = ? AND status = 'completed'
        ");

        $stmt->execute([$processingType, $processingPeriod]);
        return $stmt->fetch() !== false;
    }

    /**
     * Initialize processing status record
     */
    private function initializeProcessingStatus($processingPeriod, $processingType) {
        $periodStartDate = $processingPeriod;
        $periodEndDate = ($processingType === 'weekly') ?
            date('Y-m-d', strtotime($processingPeriod . ' +6 days')) :
            $processingPeriod;

        $stmt = $this->db->prepare("
            INSERT INTO income_processing_status
            (processing_type, processing_period, period_start_date, period_end_date, status, processing_started_at, created_by_type)
            VALUES (?, ?, ?, ?, 'processing', NOW(), 'system')
        ");

        $stmt->execute([$processingType, $processingPeriod, $periodStartDate, $periodEndDate]);
        return $this->db->lastInsertId();
    }

    /**
     * Update processing status
     */
    private function updateProcessingStatus($statusId, $processed, $usersWithIncome, $totalIncome, $totalCapping) {
        $stmt = $this->db->prepare("
            UPDATE income_processing_status
            SET total_users_processed = ?,
                total_users_with_income = ?,
                total_income_distributed = ?,
                total_capping_applied = ?
            WHERE id = ?
        ");

        $stmt->execute([$processed, $usersWithIncome, $totalIncome, $totalCapping, $statusId]);
    }

    /**
     * Complete processing status
     */
    private function completeProcessingStatus($statusId, $processed, $usersWithIncome, $totalIncome, $totalCapping) {
        $stmt = $this->db->prepare("
            UPDATE income_processing_status
            SET status = 'completed',
                total_users_processed = ?,
                total_users_with_income = ?,
                total_income_distributed = ?,
                total_capping_applied = ?,
                processing_completed_at = NOW()
            WHERE id = ?
        ");

        $stmt->execute([$processed, $usersWithIncome, $totalIncome, $totalCapping, $statusId]);
    }

    /**
     * Mark processing as failed
     */
    private function markProcessingAsFailed($statusId, $errorMessage) {
        $stmt = $this->db->prepare("
            UPDATE income_processing_status
            SET status = 'failed', error_message = ?
            WHERE id = ?
        ");

        $stmt->execute([$errorMessage, $statusId]);
    }

    /**
     * Generate processing summary report
     */
    private function generateProcessingSummaryReport($processingPeriod, $processingType, $processed, $usersWithIncome, $totalIncome, $totalCapping, $errors) {
        // Create or update weekly income report if it's weekly processing
        if ($processingType === 'weekly') {
            $weekEndDate = date('Y-m-d', strtotime($processingPeriod . ' +6 days'));

            $stmt = $this->db->prepare("
                INSERT INTO weekly_income_reports
                (week_start_date, week_end_date, total_users_earned, total_income_distributed, total_capping_applied, report_status)
                VALUES (?, ?, ?, ?, ?, 'generated')
                ON DUPLICATE KEY UPDATE
                total_users_earned = VALUES(total_users_earned),
                total_income_distributed = VALUES(total_income_distributed),
                total_capping_applied = VALUES(total_capping_applied),
                report_status = 'generated',
                report_generated_at = CURRENT_TIMESTAMP
            ");

            $stmt->execute([$processingPeriod, $weekEndDate, $usersWithIncome, $totalIncome, $totalCapping]);
        }

        // Record performance metrics
        $this->recordPerformanceMetrics($processingPeriod, $processingType, $processed, $totalIncome);
    }

    /**
     * Record performance metrics
     */
    private function recordPerformanceMetrics($processingPeriod, $processingType, $usersProcessed, $totalIncome) {
        $metrics = [
            ['users_processed', $usersProcessed, 'count'],
            ['total_income', $totalIncome, 'INR'],
            ['memory_usage', memory_get_peak_usage(true) / 1024 / 1024, 'MB']
        ];

        $stmt = $this->db->prepare("
            INSERT INTO pv_performance_metrics
            (metric_type, processing_period, processing_type, metric_value, metric_unit)
            VALUES (?, ?, ?, ?, ?)
        ");

        foreach ($metrics as $metric) {
            $stmt->execute([$metric[0], $processingPeriod, $processingType, $metric[1], $metric[2]]);
        }
    }

    /**
     * Log system messages
     */
    private function logMessage($level, $category, $message, $context = []) {
        try {
            // Try new schema first
            $stmt = $this->db->prepare("
                INSERT INTO system_logs (log_type, category, message, context, user_id, processing_period)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $level,
                $category,
                $message,
                json_encode($context),
                $context['user_id'] ?? null,
                $context['processing_period'] ?? null
            ]);
        } catch (Exception $e) {
            // Fall back to old schema
            try {
                $stmt = $this->db->prepare("
                    INSERT INTO system_logs (log_type, message)
                    VALUES (?, ?)
                ");

                $fullMessage = "[{$category}] {$message}";
                if (!empty($context)) {
                    $fullMessage .= " - Context: " . json_encode($context);
                }

                $stmt->execute([$level, $fullMessage]);
            } catch (Exception $e2) {
                // If logging fails completely, just continue
                error_log("Logging failed: " . $e2->getMessage());
            }
        }
    }
}
