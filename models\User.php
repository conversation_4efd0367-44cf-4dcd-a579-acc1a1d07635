    <?php
    /**
     * User Model
     * MLM Binary Plan System
     */

    require_once 'BaseModel.php';

    class User extends BaseModel {
        protected $table = 'users';
        protected $primaryKey = 'id';
        protected $fillable = [
            'user_id', 'username', 'email', 'password', 'full_name', 'phone',
            'address', 'sponsor_id', 'franchise_id', 'placement_side', 'status',
            'self_pv', 'upline_pv', 'user_level', 'current_award'
        ];
        protected $hidden = ['password'];

        /**
         * Find user by user_id
         *
         * @param string $userId User ID
         * @return array|false User data or false if not found
         */
        public function findByUserId($userId) {
            return $this->findBy('user_id', $userId);
        }

        /**
         * Find user by username
         *
         * @param string $username Username
         * @return array|false User data or false if not found
         */
        public function findByUsername($username) {
            return $this->findBy('username', $username);
        }

        /**
         * Find user by email
         *
         * @param string $email Email
         * @return array|false User data or false if not found
         */
        public function findByEmail($email) {
            return $this->findBy('email', $email);
        }

        /**
         * Authenticate user
         *
         * @param string $username Username or email
         * @param string $password Password
         * @return array|false User data or false if authentication fails
         */
        public function authenticate($username, $password) {
            // Check if username is an email
            $isEmail = filter_var($username, FILTER_VALIDATE_EMAIL);

            // Find user by username or email
            if ($isEmail) {
                $user = $this->findByEmail($username);
            } else {
                $user = $this->findByUsername($username);
            }

            // Check if user exists
            if (!$user) {
                return false;
            }

            // Check if user is active
            if ($user['status'] !== 'active') {
                return false;
            }

            // Check password (plain text as requested by user)
            if ($user['password'] !== $password) {
                return false;
            }

            // Remove password from user data
            unset($user['password']);

            return $user;
        }

        /**
         * Create a new user
         *
         * @param array $data User data
         * @return int|false User ID or false on failure
         */
        public function createUser(array $data) {
            // Generate user ID if not provided
            if (empty($data['user_id'])) {
                $data['user_id'] = $this->generateUserId();
            }

            // Set default status if not provided (inactive until 500 PV is reached)
            if (empty($data['status'])) {
                $data['status'] = 'inactive';
            }

            // Create user
            $userId = $this->create($data);

            if ($userId) {
                // Create binary tree node
                $this->createBinaryTreeNode($data['user_id'], $data['sponsor_id'], $data['placement_side']);

                // Create wallet
                $this->createWallet($data['user_id']);
            }

            return $userId;
        }

        /**
         * Check and update user activation status based on PV threshold
         *
         * @param string $userId User ID to check
         * @return bool True if status was updated, false otherwise
         */
        public function checkAndUpdateActivationStatus($userId) {
            try {
                // Get current user data
                $user = $this->findByUserId($userId);
                if (!$user) {
                    return false;
                }

                // Only check inactive users (don't deactivate active users)
                if ($user['status'] !== 'inactive') {
                    return false;
                }

                // Check if user has reached 500 PV threshold
                $selfPV = floatval($user['self_pv'] ?? 0);
                $activationThreshold = 500.0;

                if ($selfPV >= $activationThreshold) {
                    // Activate the user
                    $stmt = $this->db->prepare("UPDATE users SET status = 'active' WHERE user_id = ?");
                    $result = $stmt->execute([$userId]);

                    if ($result) {
                        error_log("User {$userId} activated automatically - reached {$selfPV} PV (threshold: {$activationThreshold})");
                        return true;
                    }
                }

                return false;
            } catch (Exception $e) {
                error_log("Error checking user activation status for {$userId}: " . $e->getMessage());
                return false;
            }
        }

        /**
         * Get activation status information for a user
         *
         * @param string $userId User ID
         * @return array Activation status information
         */
        public function getActivationStatus($userId) {
            $user = $this->findByUserId($userId);
            if (!$user) {
                return ['error' => 'User not found'];
            }

            $selfPV = floatval($user['self_pv'] ?? 0);
            $activationThreshold = 500.0;
            $remainingPV = max(0, $activationThreshold - $selfPV);

            return [
                'user_id' => $userId,
                'current_status' => $user['status'],
                'self_pv' => $selfPV,
                'activation_threshold' => $activationThreshold,
                'remaining_pv' => $remainingPV,
                'is_activated' => $user['status'] === 'active',
                'can_activate' => $selfPV >= $activationThreshold
            ];
        }

        /**
         * Generate a unique user ID
         *
         * @return string User ID
         */
        private function generateUserId() {
            $prefix = 'USR';
            $timestamp = time();
            $random = mt_rand(1000, 9999);

            return $prefix . $timestamp . $random;
        }

        /**
         * Create binary tree node
         *
         * @param string $userId User ID
         * @param string $sponsorId Sponsor ID
         * @param string $placementSide Placement side (left or right)
         * @return bool Success or failure
         */
        private function createBinaryTreeNode($userId, $sponsorId, $placementSide) {
            // Include BinaryTree class
            require_once 'includes/BinaryTree.php';

            $binaryTree = new BinaryTree();
            return $binaryTree->addNode($userId, $sponsorId, $placementSide);
        }

        /**
         * Create wallet
         *
         * @param string $userId User ID
         * @return bool Success or failure
         */
        private function createWallet($userId) {
            $stmt = $this->db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0, 0, 0)");
            return $stmt->execute([$userId]);
        }

        /**
         * Get user's downline
         *
         * @param string $userId User ID
         * @param string $side Side (left or right)
         * @param int $limit Maximum number of users to return
         * @param int $offset Offset for pagination
         * @return array Downline users
         */
        public function getDownline($userId, $side = null, $limit = 1000, $offset = 0) {
            // Include BinaryTree class
            require_once 'includes/BinaryTree.php';

            $binaryTree = new BinaryTree();

            if ($side === 'left') {
                $downlineIds = $binaryTree->getLeftLeg($userId);
            } elseif ($side === 'right') {
                $downlineIds = $binaryTree->getRightLeg($userId);
            } else {
                $leftLeg = $binaryTree->getLeftLeg($userId);
                $rightLeg = $binaryTree->getRightLeg($userId);
                $downlineIds = array_merge($leftLeg, $rightLeg);
            }

            if (empty($downlineIds)) {
                return [];
            }

            // Convert array of IDs to string for SQL IN clause
            $downlineIdsStr = "'" . implode("','", $downlineIds) . "'";

            // Get users
            $sql = "SELECT * FROM {$this->table} WHERE user_id IN ({$downlineIdsStr}) ORDER BY registration_date DESC LIMIT ? OFFSET ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$limit, $offset]);

            return $stmt->fetchAll();
        }

        /**
         * Count user's downline
         *
         * @param string $userId User ID
         * @param string $side Side (left or right)
         * @return int Number of downline users
         */
        public function countDownline($userId, $side = null) {
            // Include BinaryTree class
            require_once 'includes/BinaryTree.php';

            $binaryTree = new BinaryTree();

            if ($side === 'left') {
                $downlineIds = $binaryTree->getLeftLeg($userId);
            } elseif ($side === 'right') {
                $downlineIds = $binaryTree->getRightLeg($userId);
            } else {
                $leftLeg = $binaryTree->getLeftLeg($userId);
                $rightLeg = $binaryTree->getRightLeg($userId);
                $downlineIds = array_merge($leftLeg, $rightLeg);
            }

            return count($downlineIds);
        }

        /**
         * Get user's PV totals
         *
         * @param string $userId User ID
         * @return array PV totals
         */
        public function getPVTotals($userId) {
            // Include PVSystem class
            require_once 'includes/PVSystem.php';

            $pvSystem = new PVSystem();
            return $pvSystem->getUserPVTotals($userId);
        }

        /**
         * Get user's wallet
         *
         * @param string $userId User ID
         * @return array|false Wallet data or false if not found
         */
        public function getWallet($userId) {
            $stmt = $this->db->prepare("SELECT * FROM wallet WHERE user_id = ?");
            $stmt->execute([$userId]);
            return $stmt->fetch();
        }

        /**
         * Get user's wallet transactions
         *
         * @param string $userId User ID
         * @param int $limit Maximum number of transactions to return
         * @param int $offset Offset for pagination
         * @return array Wallet transactions
         */
        public function getWalletTransactions($userId, $limit = 50, $offset = 0) {
            $stmt = $this->db->prepare("SELECT * FROM wallet_transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?");
            $stmt->execute([$userId, $limit, $offset]);
            return $stmt->fetchAll();
        }
    }
    ?>
