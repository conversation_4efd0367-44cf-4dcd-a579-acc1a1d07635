<?php
/**
 * Weekly Payment Processor Cron Job
 * Processes automatic payments for weekly income logs
 * 
 * Schedule: Run on Saturdays at 10:00 AM (configurable)
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/Connection.php';
require_once __DIR__ . '/../includes/PaymentProcessor.php';
require_once __DIR__ . '/../includes/Logger.php';
require_once __DIR__ . '/../includes/WeeklyDateHelper.php';

// Initialize components
$db = Database::getInstance();
$config = Config::getInstance();
$paymentProcessor = new PaymentProcessor();
$logger = new Logger('payment_processor');

try {
    echo "=== Weekly Payment Processor Started ===\n";
    echo "Time: " . date('Y-m-d H:i:s') . "\n\n";
    
    // Check if automatic payment processing is enabled
    if (!$config->get('auto_payment_enabled', false)) {
        echo "Automatic payment processing is disabled. Exiting.\n";
        $logger->info("Payment processing skipped - disabled in configuration");
        exit(0);
    }
    
    // Get processing configuration
    $processingDay = $config->get('payment_processing_day', 6); // 6 = Saturday
    $processingTime = $config->get('payment_processing_time', '10:00');
    
    // Check if today is the configured processing day
    $currentDay = date('w'); // 0 = Sunday, 6 = Saturday
    $currentTime = date('H:i');
    
    if ($currentDay != $processingDay) {
        echo "Today is not the configured processing day (configured: {$processingDay}, current: {$currentDay}). Exiting.\n";
        exit(0);
    }
    
    // Determine which week to process (previous Saturday-Friday week)
    $previousWeek = WeeklyDateHelper::getPreviousWeek();
    $weekStartDate = $previousWeek['start'];
    $weekEndDate = $previousWeek['end'];
    
    echo "Processing payments for week: {$weekStartDate} to {$weekEndDate}\n";
    
    // Check if there are any income logs for this week
    $checkStmt = $db->prepare("
        SELECT COUNT(*) as count, SUM(income_amount) as total_amount 
        FROM weekly_income_logs 
        WHERE week_start_date = ? AND income_amount > 0
    ");
    $checkStmt->execute([$weekStartDate]);
    $weekStats = $checkStmt->fetch();
    
    if ($weekStats['count'] == 0) {
        echo "No income logs found for this week. Exiting.\n";
        $logger->info("No income logs found for payment processing", ['week_start' => $weekStartDate]);
        exit(0);
    }
    
    echo "Found {$weekStats['count']} income logs with total amount: ₹" . number_format($weekStats['total_amount'], 2) . "\n";
    
    // Get current payment status
    $statusStmt = $db->prepare("
        SELECT 
            payment_status,
            COUNT(*) as count,
            SUM(income_amount) as amount
        FROM weekly_income_logs 
        WHERE week_start_date = ? AND income_amount > 0
        GROUP BY payment_status
    ");
    $statusStmt->execute([$weekStartDate]);
    $statusBreakdown = $statusStmt->fetchAll();
    
    echo "\nCurrent payment status breakdown:\n";
    foreach ($statusBreakdown as $status) {
        $statusName = $status['payment_status'] ?: 'pending';
        echo "- {$statusName}: {$status['count']} logs, ₹" . number_format($status['amount'], 2) . "\n";
    }
    
    // Process payments
    echo "\nStarting payment processing...\n";
    $startTime = microtime(true);
    
    $result = $paymentProcessor->processWeeklyPayments($weekStartDate, $weekEndDate);
    
    $processingTime = microtime(true) - $startTime;
    
    if ($result) {
        echo "\n=== Payment Processing Completed ===\n";
        echo "Batch ID: {$result['batch_id']}\n";
        echo "Processed: {$result['processed']} payments\n";
        echo "Successful: {$result['successful']} payments\n";
        echo "Failed: {$result['failed']} payments\n";
        echo "Total Amount: ₹" . number_format($result['total_amount'], 2) . "\n";
        echo "Processing Time: " . round($processingTime, 2) . " seconds\n";
        
        // Log success
        $logger->info("Weekly payment processing completed successfully", [
            'week_start' => $weekStartDate,
            'batch_id' => $result['batch_id'],
            'processed' => $result['processed'],
            'successful' => $result['successful'],
            'failed' => $result['failed'],
            'total_amount' => $result['total_amount'],
            'processing_time_seconds' => round($processingTime, 2)
        ]);
        
        // Send notification to admin if there were failures
        if ($result['failed'] > 0) {
            sendFailureNotification($result, $weekStartDate, $weekEndDate);
        }
        
        // Send success notification
        sendSuccessNotification($result, $weekStartDate, $weekEndDate);
        
    } else {
        echo "\n❌ Payment processing failed. Check logs for details.\n";
        $logger->error("Weekly payment processing failed", [
            'week_start' => $weekStartDate,
            'week_end' => $weekEndDate
        ]);
        
        // Send failure notification
        sendCriticalFailureNotification($weekStartDate, $weekEndDate);
    }
    
} catch (Exception $e) {
    $errorMessage = "Payment processor error: " . $e->getMessage();
    echo "\n❌ {$errorMessage}\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    
    $logger->error($errorMessage, [
        'error_trace' => $e->getTraceAsString(),
        'week_start' => $weekStartDate ?? 'unknown',
        'week_end' => $weekEndDate ?? 'unknown'
    ]);
    
    // Send critical failure notification
    sendCriticalFailureNotification($weekStartDate ?? 'unknown', $weekEndDate ?? 'unknown', $e->getMessage());
}

/**
 * Send success notification to admin
 */
function sendSuccessNotification($result, $weekStart, $weekEnd) {
    // Implementation would depend on your notification system
    // This could be email, SMS, or webhook notification
    echo "\n📧 Success notification sent to admin\n";
}

/**
 * Send failure notification for partial failures
 */
function sendFailureNotification($result, $weekStart, $weekEnd) {
    echo "\n⚠️ Failure notification sent to admin ({$result['failed']} failed payments)\n";
}

/**
 * Send critical failure notification
 */
function sendCriticalFailureNotification($weekStart, $weekEnd, $error = null) {
    echo "\n🚨 Critical failure notification sent to admin\n";
    if ($error) {
        echo "Error: {$error}\n";
    }
}

echo "\n=== Weekly Payment Processor Finished ===\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";
?>
