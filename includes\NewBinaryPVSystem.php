<?php
/**
 * New Binary PV System Class
 * MLM Binary Plan System - Fresh Implementation
 * Handles binary PV matching and income generation
 */

require_once __DIR__ . '/BinaryTree.php';
require_once __DIR__ . '/Wallet.php';
require_once __DIR__ . '/../config/config.php';

class NewBinaryPVSystem {
    private $db;
    private $config;
    private $wallet;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
        $this->wallet = new Wallet();
    }

    /**
     * Add PV to user's account (basic functionality preserved)
     */
    public function addPV($userId, $pvAmount, $side, $transactionType = 'purchase', $productId = null, $referenceId = null, $description = '', $createdByType = 'system', $createdById = null) {
        try {
            $this->db->beginTransaction();

            // Validate required parameters
            if (empty($userId) || empty($pvAmount) || empty($side)) {
                throw new Exception("Missing required parameters: userId, pvAmount, or side");
            }

            // Validate side parameter
            if (!in_array($side, ['left', 'right'])) {
                throw new Exception("Invalid side parameter. Must be 'left' or 'right'");
            }

            // Insert PV transaction
            $stmt = $this->db->prepare("INSERT INTO pv_transactions (user_id, transaction_type, pv_amount, side, product_id, reference_id, description, created_by_type, created_by_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')");
            $result = $stmt->execute([
                $userId,
                $transactionType,
                $pvAmount,
                $side,
                $productId,
                $referenceId,
                $description,
                $createdByType,
                $createdById
            ]);

            if (!$result) {
                throw new Exception("Failed to insert PV transaction");
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }
            error_log("PV addition error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get user's total PV from all downline (left and right legs)
     */
    /**
 * Get user's total PV from all downline (left and right legs)
 */
public function getUserTotalPV($userId) {
    $binaryTree = new BinaryTree();
    
    // Get all users in left leg
    $leftLeg = $binaryTree->getLeftLeg($userId);
    $rightLeg = $binaryTree->getRightLeg($userId);
    
    $leftPV = 0;
    $rightPV = 0;
    
    // Calculate left leg PV
    if (!empty($leftLeg)) {
        $leftUserIds = array_column($leftLeg, 'user_id');
        $leftUserIds[] = $userId; // Include self
        
        // Fix: Check if array has elements before creating placeholders
        if (count($leftUserIds) > 0) {
            $placeholders = implode(',', array_fill(0, count($leftUserIds), '?'));
            $stmt = $this->db->prepare("SELECT SUM(pv_amount) as total FROM pv_transactions WHERE user_id IN ($placeholders) AND processing_status = 'pending'");
            $stmt->execute($leftUserIds);
            $result = $stmt->fetch();
            $leftPV = (float) ($result['total'] ?? 0);
        }
    }
    
    // Calculate right leg PV
    if (!empty($rightLeg)) {
        $rightUserIds = array_column($rightLeg, 'user_id');
        
        // Fix: Check if array has elements before creating placeholders
        if (count($rightUserIds) > 0) {
            $placeholders = implode(',', array_fill(0, count($rightUserIds), '?'));
            $stmt = $this->db->prepare("SELECT SUM(pv_amount) as total FROM pv_transactions WHERE user_id IN ($placeholders) AND processing_status = 'pending'");
            $stmt->execute($rightUserIds);
            $result = $stmt->fetch();
            $rightPV = (float) ($result['total'] ?? 0);
        }
    }
    
    return [
        'left_pv' => $leftPV,
        'right_pv' => $rightPV,
        'total_pv' => $leftPV + $rightPV
    ];
}

    /**
     * Generate report and credit income to all users
     * This is the main function called when admin clicks "Generate Report"
     */
    public function generateReport($reportDate = null) {
        try {
            if (!$reportDate) {
                $reportDate = date('Y-m-d');
            }
            
            $this->db->beginTransaction();
            
            // Get all active users
            $stmt = $this->db->prepare("SELECT user_id FROM users WHERE status = 'active'");
            $stmt->execute();
            $users = $stmt->fetchAll();
            
            $totalUsersProcessed = 0;
            $totalIncomeGenerated = 0;
            $reportId = 'RPT_' . date('YmdHis') . '_' . uniqid();
            
            foreach ($users as $user) {
                $userId = $user['user_id'];
                
                // Get user's PV totals
                $pvTotals = $this->getUserTotalPV($userId);
                $leftPV = $pvTotals['left_pv'];
                $rightPV = $pvTotals['right_pv'];
                
                // Calculate matched PV (binary matching - minimum of left and right)
                $matchedPV = min($leftPV, $rightPV);
                
                if ($matchedPV > 0) {
                    // Calculate income based on PV rate
                    $pvRate = $this->config->getPVRate(); // Default ₹0.20 per PV
                    $income = $matchedPV * $pvRate;
                    
                    // Credit income to user's wallet
                    $this->wallet->credit($userId, $income, "Binary PV Matching Income - Report: $reportId", 'pv_matching', $reportId, false);
                    
                    // Mark PVs as processed
                    $this->markPVsAsProcessed($userId, $matchedPV);
                    
                    // Record income log
                    $this->recordIncomeLog($userId, $leftPV, $rightPV, $matchedPV, $income, $reportId, $reportDate);
                    
                    $totalUsersProcessed++;
                    $totalIncomeGenerated += $income;
                }
            }
            
            // Create report summary
            $this->createReportSummary($reportId, $reportDate, $totalUsersProcessed, $totalIncomeGenerated);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'report_id' => $reportId,
                'users_processed' => $totalUsersProcessed,
                'total_income' => $totalIncomeGenerated,
                'report_date' => $reportDate
            ];
            
        } catch (Exception $e) {
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }
            error_log("Report generation error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Mark PVs as processed to prevent double-counting
     */
    private function markPVsAsProcessed($userId, $matchedPV) {
        // Get user's downline structure
        $binaryTree = new BinaryTree();
        $leftLeg = $binaryTree->getLeftLeg($userId);
        $rightLeg = $binaryTree->getRightLeg($userId);
        
        $leftPVToMark = $matchedPV;
        $rightPVToMark = $matchedPV;
        
        // Mark left leg PVs as processed
        if (!empty($leftLeg)) {
            $leftUserIds = array_column($leftLeg, 'user_id');
            $leftUserIds[] = $userId; // Include self
            $this->markPVsByAmount($leftUserIds, $leftPVToMark);
        }
        
        // Mark right leg PVs as processed
        if (!empty($rightLeg)) {
            $rightUserIds = array_column($rightLeg, 'user_id');
            $this->markPVsByAmount($rightUserIds, $rightPVToMark);
        }
    }

    /**
     * Mark specific amount of PVs as processed for given users
     */
  /**
 * Mark specific amount of PVs as processed for given users
 */
private function markPVsByAmount($userIds, $amountToMark) {
    if (empty($userIds) || $amountToMark <= 0) {
        return;
    }
    
    // Fix: Check if array has elements before creating placeholders
    if (count($userIds) > 0) {
        $placeholders = implode(',', array_fill(0, count($userIds), '?'));
        
        // Get pending PVs ordered by creation date (FIFO)
        $stmt = $this->db->prepare("SELECT id, pv_amount FROM pv_transactions WHERE user_id IN ($placeholders) AND processing_status = 'pending' ORDER BY created_at ASC");
        $stmt->execute($userIds);
        $pendingPVs = $stmt->fetchAll();
        
        $remainingToMark = $amountToMark;
        
        foreach ($pendingPVs as $pv) {
            if ($remainingToMark <= 0) {
                break;
            }
            
            if ($pv['pv_amount'] <= $remainingToMark) {
                // Mark entire PV as processed
                $updateStmt = $this->db->prepare("UPDATE pv_transactions SET processing_status = 'processed', processed_at = NOW() WHERE id = ?");
                $updateStmt->execute([$pv['id']]);
                $remainingToMark -= $pv['pv_amount'];
            } else {
                // Partial processing - this shouldn't happen in binary matching but handle it
                $updateStmt = $this->db->prepare("UPDATE pv_transactions SET processing_status = 'processed', processed_at = NOW() WHERE id = ?");
                $updateStmt->execute([$pv['id']]);
                $remainingToMark = 0;
            }
        }
    }
}

    /**
     * Record income log for audit trail
     */
    private function recordIncomeLog($userId, $leftPV, $rightPV, $matchedPV, $income, $reportId, $reportDate) {
        $stmt = $this->db->prepare("INSERT INTO binary_income_logs (user_id, report_id, report_date, left_pv, right_pv, matched_pv, income_amount, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute([$userId, $reportId, $reportDate, $leftPV, $rightPV, $matchedPV, $income]);
    }

    /**
     * Create report summary
     */
    private function createReportSummary($reportId, $reportDate, $usersProcessed, $totalIncome) {
        $stmt = $this->db->prepare("INSERT INTO binary_reports (report_id, report_date, users_processed, total_income_generated, status, created_at) VALUES (?, ?, ?, ?, 'completed', NOW())");
        $stmt->execute([$reportId, $reportDate, $usersProcessed, $totalIncome]);
    }

    /**
     * Get report history
     */
    public function getReportHistory($limit = 20) {
        $stmt = $this->db->prepare("SELECT * FROM binary_reports ORDER BY created_at DESC LIMIT ?");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    }

    /**
     * Get user's income history
     */
    public function getUserIncomeHistory($userId, $limit = 50) {
        $stmt = $this->db->prepare("SELECT * FROM binary_income_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT ?");
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    }
}
