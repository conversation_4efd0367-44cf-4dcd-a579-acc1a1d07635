<?php
/**
 * Create Required Directories
 * ShaktiPure MLM System
 * 
 * This script creates all necessary directories with proper permissions
 */

echo "<!DOCTYPE html>\n";
echo "<html><head><title>Create Directories</title></head><body>\n";
echo "<h1>📁 Creating Required Directories</h1>\n";

$directories = [
    'uploads/',
    'uploads/products/',
    'uploads/documents/',
    'uploads/profile_pictures/',
    'logs/',
    'logs/weekly_income_reports/',
    'logs/weekly_income_errors/',
    'logs/security/',
    'logs/api/',
    'backups/',
    'cache/',
    'temp/',
    'reports/',
    'exports/'
];

$created = 0;
$existing = 0;
$failed = 0;

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p style='color: green;'>✅ Created: {$dir}</p>\n";
            $created++;
        } else {
            echo "<p style='color: red;'>❌ Failed to create: {$dir}</p>\n";
            $failed++;
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Already exists: {$dir}</p>\n";
        $existing++;
    }
    
    // Set proper permissions
    if (is_dir($dir)) {
        chmod($dir, 0755);
    }
}

// Create security .htaccess files
$htaccessFiles = [
    'uploads/products/.htaccess' => "Options -Indexes\n<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n    Order allow,deny\n    Allow from all\n</FilesMatch>\n<FilesMatch \"^(?!.*\\.(jpg|jpeg|png|gif|webp)$).*$\">\n    Order deny,allow\n    Deny from all\n</FilesMatch>",
    'logs/.htaccess' => "Order deny,allow\nDeny from all",
    'backups/.htaccess' => "Order deny,allow\nDeny from all",
    'cache/.htaccess' => "Order deny,allow\nDeny from all"
];

echo "<h2>🔒 Creating Security Files</h2>\n";

foreach ($htaccessFiles as $file => $content) {
    if (!file_exists($file)) {
        if (file_put_contents($file, $content)) {
            echo "<p style='color: green;'>✅ Created security file: {$file}</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Failed to create: {$file}</p>\n";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Security file exists: {$file}</p>\n";
    }
}

echo "<h2>📊 Summary</h2>\n";
echo "<p><strong>Created:</strong> {$created} directories</p>\n";
echo "<p><strong>Already existed:</strong> {$existing} directories</p>\n";
echo "<p><strong>Failed:</strong> {$failed} directories</p>\n";

if ($failed === 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3 style='color: #155724;'>✅ All directories created successfully!</h3>\n";
    echo "<p>Your system now has all required directories with proper permissions.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3 style='color: #721c24;'>⚠️ Some directories failed to create</h3>\n";
    echo "<p>Please check file permissions and try again, or create them manually.</p>\n";
    echo "</div>\n";
}

echo "</body></html>\n";
?>
