<?php
/**
 * Branches Page
 * ShaktiPure Industries Pvt Ltd
 */

require_once 'includes/header.php';
renderHeader('Our Branches - ShaktiPure Industries Pvt Ltd');
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="page-header-content">
            <h1>Our Branches</h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Branches</span>
            </nav>
        </div>
    </div>
</section>

<!-- Branches Content -->
<section class="branches-section">
    <div class="container">
        <div class="branches-intro">
            <h2>Nationwide Presence</h2>
            <p>ShaktiPure Industries has established a strong network of branches and service centers across India to provide you with the best sales and after-sales support. Find your nearest branch for product demonstrations, purchases, and service support.</p>
        </div>

        <!-- Head Office -->
        <div class="head-office">
            <h3>Head Office</h3>
            <div class="office-card featured">
                <div class="office-info">
                    <div class="office-header">
                        <h4>ShaktiPure Industries Pvt. Ltd.</h4>
                        <span class="office-type">Head Office</span>
                    </div>
                    <div class="office-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>D-224, Udhana Complex, Udhana, Surat-394210, Gujarat, India</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span>+91-8460203679</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Mon - Sat: 9:00 AM - 6:00 PM</span>
                        </div>
                    </div>
                </div>
                <div class="office-actions">
                    <a href="tel:+************" class="btn-contact">
                        <i class="fas fa-phone"></i>
                        Call Now
                    </a>
                    <a href="#" class="btn-directions" onclick="alert('Google Maps integration coming soon!')">
                        <i class="fas fa-directions"></i>
                        Get Directions
                    </a>
                </div>
            </div>
        </div>

        <!-- Regional Branches -->
        <div class="regional-branches">
            <h3>Regional Branches</h3>
            <div class="branches-grid">
                <!-- Mumbai Branch -->
                <div class="branch-card">
                    <div class="branch-header">
                        <h4>Mumbai Branch</h4>
                        <span class="branch-status active">Active</span>
                    </div>
                    <div class="branch-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Shop No. 15, Andheri West, Mumbai - 400058, Maharashtra</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span>+91-9876543210</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Mon - Sat: 10:00 AM - 7:00 PM</span>
                        </div>
                    </div>
                    <div class="branch-services">
                        <span class="service-tag">Sales</span>
                        <span class="service-tag">Service</span>
                        <span class="service-tag">Installation</span>
                    </div>
                </div>

                <!-- Delhi Branch -->
                <div class="branch-card">
                    <div class="branch-header">
                        <h4>Delhi Branch</h4>
                        <span class="branch-status active">Active</span>
                    </div>
                    <div class="branch-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>B-45, Lajpat Nagar, New Delhi - 110024, Delhi</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span>+91-9876543211</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Mon - Sat: 9:30 AM - 6:30 PM</span>
                        </div>
                    </div>
                    <div class="branch-services">
                        <span class="service-tag">Sales</span>
                        <span class="service-tag">Service</span>
                        <span class="service-tag">Installation</span>
                    </div>
                </div>

                <!-- Bangalore Branch -->
                <div class="branch-card">
                    <div class="branch-header">
                        <h4>Bangalore Branch</h4>
                        <span class="branch-status active">Active</span>
                    </div>
                    <div class="branch-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>No. 123, MG Road, Bangalore - 560001, Karnataka</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span>+91-9876543212</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Mon - Sat: 10:00 AM - 7:00 PM</span>
                        </div>
                    </div>
                    <div class="branch-services">
                        <span class="service-tag">Sales</span>
                        <span class="service-tag">Service</span>
                    </div>
                </div>

                <!-- Chennai Branch -->
                <div class="branch-card">
                    <div class="branch-header">
                        <h4>Chennai Branch</h4>
                        <span class="branch-status active">Active</span>
                    </div>
                    <div class="branch-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Plot 67, Anna Nagar, Chennai - 600040, Tamil Nadu</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span>+91-9876543213</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Mon - Sat: 9:00 AM - 6:00 PM</span>
                        </div>
                    </div>
                    <div class="branch-services">
                        <span class="service-tag">Sales</span>
                        <span class="service-tag">Service</span>
                        <span class="service-tag">Installation</span>
                    </div>
                </div>

                <!-- Pune Branch -->
                <div class="branch-card">
                    <div class="branch-header">
                        <h4>Pune Branch</h4>
                        <span class="branch-status coming-soon">Coming Soon</span>
                    </div>
                    <div class="branch-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>FC Road, Pune - 411005, Maharashtra</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span>+91-8460203679 (Head Office)</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Opening Soon</span>
                        </div>
                    </div>
                    <div class="branch-services">
                        <span class="service-tag upcoming">Upcoming</span>
                    </div>
                </div>

                <!-- Ahmedabad Branch -->
                <div class="branch-card">
                    <div class="branch-header">
                        <h4>Ahmedabad Branch</h4>
                        <span class="branch-status active">Active</span>
                    </div>
                    <div class="branch-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>CG Road, Ahmedabad - 380009, Gujarat</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span>+91-9876543214</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Mon - Sat: 9:30 AM - 6:30 PM</span>
                        </div>
                    </div>
                    <div class="branch-services">
                        <span class="service-tag">Sales</span>
                        <span class="service-tag">Service</span>
                        <span class="service-tag">Installation</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Notice -->
        <div class="contact-notice">
            <div class="notice-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="notice-content">
                <h4>Need Help Finding a Branch?</h4>
                <p>Can't find a branch near you? Contact our head office and we'll help you locate the nearest service center or arrange for home service in your area.</p>
                <a href="tel:+************" class="contact-btn">
                    <i class="fas fa-phone"></i>
                    Call Head Office
                </a>
            </div>
        </div>
    </div>
</section>

<?php renderFooter(); ?>

<style>
/* Page Header */
.page-header {
    background: var(--gradient-primary);
    padding: 60px 0;
    color: white;
}

.page-header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

/* Branches Section */
.branches-section {
    padding: 80px 0;
}

.branches-intro {
    text-align: center;
    margin-bottom: 60px;
}

.branches-intro h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.branches-intro p {
    font-size: 16px;
    color: var(--text-gray-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Head Office */
.head-office {
    margin-bottom: 80px;
}

.head-office h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 30px;
    text-align: center;
}

.office-card {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 40px;
    max-width: 800px;
    margin: 0 auto;
}

.office-card.featured {
    border-left: 4px solid var(--primary-green);
}

.office-info {
    flex: 1;
}

.office-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.office-header h4 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin: 0;
}

.office-type {
    background: var(--primary-green);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.office-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    color: var(--text-gray-light);
}

.detail-item i {
    color: var(--primary-green);
    margin-top: 2px;
    min-width: 16px;
}

.office-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.btn-contact, .btn-directions {
    padding: 12px 24px;
    border-radius: var(--button-border-radius);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-align: center;
    justify-content: center;
}

.btn-contact {
    background: var(--primary-green);
    color: white;
}

.btn-contact:hover {
    background: var(--primary-dark);
    color: white;
    text-decoration: none;
}

.btn-directions {
    background: var(--background-light);
    color: var(--text-dark);
    border: 1px solid var(--border-color);
}

.btn-directions:hover {
    background: var(--text-dark);
    color: white;
    text-decoration: none;
}

/* Regional Branches */
.regional-branches h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-green);
}

.branches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.branch-card {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.branch-card:hover {
    transform: translateY(-5px);
}

.branch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.branch-header h4 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin: 0;
}

.branch-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.branch-status.active {
    background: #d4edda;
    color: #155724;
}

.branch-status.coming-soon {
    background: #fff3cd;
    color: #856404;
}

.branch-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.branch-services {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.service-tag {
    background: var(--primary-green);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 500;
}

.service-tag.upcoming {
    background: var(--text-gray-light);
}

/* Contact Notice */
.contact-notice {
    background: var(--background-light);
    border-radius: var(--card-border-radius);
    padding: 40px;
    text-align: center;
    border-left: 4px solid var(--secondary-blue);
}

.notice-icon {
    font-size: 2.5rem;
    color: var(--secondary-blue);
    margin-bottom: 20px;
}

.notice-content h4 {
    color: var(--text-dark);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.notice-content p {
    color: var(--text-gray-light);
    line-height: 1.6;
    margin-bottom: 25px;
}

.contact-btn {
    background: var(--secondary-blue);
    color: white;
    padding: 12px 24px;
    border-radius: var(--button-border-radius);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    background: #1e7bb8;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
    .branches-grid {
        grid-template-columns: 1fr;
    }
    
    .office-card {
        flex-direction: column;
        text-align: center;
    }
    
    .office-actions {
        flex-direction: row;
        justify-content: center;
    }
}
</style>
