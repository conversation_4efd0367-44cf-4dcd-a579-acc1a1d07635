<?php
/**
 * Run All Migration Scripts
 * ShaktiPure MLM System
 * 
 * This script runs all individual migration scripts in the correct order
 * for existing installations that need to be updated.
 */

// Set execution time limit
set_time_limit(300);

// Determine if running from CLI or web
$isCLI = (php_sapi_name() === 'cli');

// Output functions
function output($message, $type = 'info') {
    global $isCLI;
    
    $icons = [
        'info' => '📋',
        'success' => '✅',
        'warning' => '⚠️',
        'error' => '❌',
        'progress' => '🔄'
    ];
    
    $icon = $icons[$type] ?? '📋';
    
    if ($isCLI) {
        echo "{$icon} {$message}\n";
    } else {
        $colors = [
            'info' => '#17a2b8',
            'success' => '#28a745',
            'warning' => '#ffc107',
            'error' => '#dc3545',
            'progress' => '#007bff'
        ];
        $color = $colors[$type] ?? '#17a2b8';
        echo "<p style='color: {$color}; margin: 5px 0;'>{$icon} {$message}</p>\n";
        if (ob_get_level()) ob_flush();
        flush();
    }
}

// Start HTML output for web interface
if (!$isCLI) {
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>ShaktiPure MLM - Run All Migrations</title>";
    echo "<style>body{font-family:Arial,sans-serif;max-width:1200px;margin:0 auto;padding:20px;background:#f8f9fa;}";
    echo ".container{background:white;padding:30px;border-radius:10px;box-shadow:0 0 20px rgba(0,0,0,0.1);}";
    echo "</style></head><body><div class='container'>\n";
    echo "<h1 style='color: #007bff; text-align: center;'>🔄 Running All Migration Scripts</h1>\n";
}

try {
    output("Starting migration process...", 'progress');
    
    // List of migration scripts in order
    $migrations = [
        'migrate-add-pv-columns.php' => 'Add PV columns to users table',
        'migrate-fix-products-table.php' => 'Fix products table structure',
        'migrate-achievements.php' => 'Add user achievements system',
        'migrate-add-pv-usage-tracking.php' => 'Add PV usage tracking table',
        'create-upload-directories.php' => 'Create upload directories',
        'create-master-accounts.php' => 'Create master accounts'
    ];
    
    $successCount = 0;
    $failureCount = 0;
    $skippedCount = 0;
    
    foreach ($migrations as $script => $description) {
        output("Running: {$description}", 'progress');
        
        if (!file_exists($script)) {
            output("Migration script not found: {$script}", 'warning');
            $skippedCount++;
            continue;
        }
        
        // Capture output from migration script
        ob_start();
        $result = include $script;
        $migrationOutput = ob_get_clean();
        
        // Check if migration was successful
        if ($result !== false && !empty($migrationOutput)) {
            output("Completed: {$description}", 'success');
            $successCount++;
            
            // Show migration output in web interface
            if (!$isCLI && $migrationOutput) {
                echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 5px; font-size: 12px;'>";
                echo "<strong>Migration Output:</strong><br>";
                echo $migrationOutput;
                echo "</div>";
            }
        } else {
            output("Failed: {$description}", 'error');
            $failureCount++;
        }
        
        // Small delay between migrations
        if (!$isCLI) {
            usleep(500000); // 0.5 second delay
        }
    }
    
    // Summary
    output("", 'info');
    output("Migration Summary:", 'info');
    output("✅ Successful: {$successCount}", 'success');
    output("❌ Failed: {$failureCount}", 'error');
    output("⚠️ Skipped: {$skippedCount}", 'warning');
    
    if ($failureCount === 0) {
        output("All migrations completed successfully!", 'success');
        
        if (!$isCLI) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
            echo "<h3 style='color: #155724; margin-top: 0;'>🎉 All Migrations Completed!</h3>\n";
            echo "<p>Your system has been successfully updated with all available migrations.</p>\n";
            echo "<p><strong>Next Steps:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Test your system functionality</li>\n";
            echo "<li>Check admin panel for any issues</li>\n";
            echo "<li>Verify user registration and PV system</li>\n";
            echo "<li>Test report generation</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        }
    } else {
        output("Some migrations failed. Please check the errors above.", 'warning');
        
        if (!$isCLI) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
            echo "<h3 style='color: #856404; margin-top: 0;'>⚠️ Some Migrations Failed</h3>\n";
            echo "<p>Please review the error messages above and fix any issues before proceeding.</p>\n";
            echo "<p>You may need to run individual migration scripts manually.</p>\n";
            echo "</div>\n";
        }
    }
    
} catch (Exception $e) {
    output("Migration process failed: " . $e->getMessage(), 'error');
    
    if (!$isCLI) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 10px;'>\n";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Migration Process Failed</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
}

if (!$isCLI) {
    echo "</div></body></html>\n";
}
?>
