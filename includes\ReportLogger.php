<?php
/**
 * Report Logger - Enhanced logging for report generation
 * Provides detailed logging and debugging capabilities for income report generation
 */

class ReportLogger {
    private $logFile;
    private $sessionId;
    
    public function __construct($logType = 'report_generation') {
        $this->sessionId = uniqid();
        $logDir = __DIR__ . '/../logs';
        
        // Create logs directory if it doesn't exist
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->logFile = $logDir . '/' . $logType . '_' . date('Y-m-d') . '.log';
    }
    
    /**
     * Log info message
     */
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    /**
     * Log warning message
     */
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
    
    /**
     * Log error message
     */
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    /**
     * Log debug message
     */
    public function debug($message, $context = []) {
        $this->log('DEBUG', $message, $context);
    }
    
    /**
     * Log message with level
     */
    private function log($level, $message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
        $logEntry = "[{$timestamp}] [{$level}] [Session: {$this->sessionId}] {$message}{$contextStr}" . PHP_EOL;
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Also log to PHP error log for critical errors
        if ($level === 'ERROR') {
            error_log($logEntry);
        }
    }
    
    /**
     * Start timing an operation
     */
    public function startTimer($operation) {
        $this->timers[$operation] = microtime(true);
        $this->info("Started operation: {$operation}");
    }
    
    /**
     * End timing an operation
     */
    public function endTimer($operation) {
        if (isset($this->timers[$operation])) {
            $duration = microtime(true) - $this->timers[$operation];
            $this->info("Completed operation: {$operation}", ['duration_seconds' => round($duration, 3)]);
            unset($this->timers[$operation]);
        }
    }
    
    /**
     * Log report generation summary
     */
    public function logReportSummary($weekStart, $weekEnd, $result) {
        $summary = [
            'week_start' => $weekStart,
            'week_end' => $weekEnd,
            'processed_users' => $result['processed'] ?? 0,
            'users_with_income' => $result['users_with_income'] ?? 0,
            'total_income' => $result['total_income'] ?? 0,
            'error_count' => $result['error_count'] ?? 0,
            'success' => $result !== false
        ];
        
        $this->info("Report generation completed", $summary);
        
        if (isset($result['errors']) && !empty($result['errors'])) {
            foreach ($result['errors'] as $error) {
                $this->error("User processing error: {$error}");
            }
        }
    }
    
    private $timers = [];
}
?>
