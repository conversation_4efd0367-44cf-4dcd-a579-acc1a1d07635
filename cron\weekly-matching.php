<?php
/**
 * Weekly PV Matching Cron Job
 * MLM Binary Plan System
 * 
 * This script should be run weekly (e.g., every Sunday at midnight)
 * to process PV matching income for all users
 */

// Prevent direct browser access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

// Set time limit and memory limit for long-running process
set_time_limit(0);
ini_set('memory_limit', '512M');

echo str_repeat('=', 60) . "\n";
echo "Weekly PV Matching Process\n";
echo "Start time: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat('=', 60) . "\n";

// Include required files
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/config/Connection.php';
require_once dirname(__DIR__) . '/includes/PVSystem.php';
require_once dirname(__DIR__) . '/includes/BinaryTree.php';
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/includes/EmailService.php';
require_once dirname(__DIR__) . '/includes/WeeklyDateHelper.php';

try {
    $pvSystem = new PVSystem();
    $config = Config::getInstance();
    
    // Determine the week to process (Saturday-Friday weeks)
    $previousWeek = WeeklyDateHelper::getPreviousWeek();
    $weekStartDate = $previousWeek['start'];
    $weekEndDate = $previousWeek['end'];

    // Check if we should process - runs on Friday at 11:59 PM
    $processingDay = WeeklyDateHelper::getProcessingDay(); // 5 = Friday
    $currentDayOfWeek = (int) date('w'); // 0 = Sunday, 1 = Monday, etc.

    if ($currentDayOfWeek === $processingDay) {
        // If today is Friday, process the completed Saturday-Friday week
        echo "Processing completed Saturday-Friday week: {$weekStartDate} to {$weekEndDate}\n";
    } else {
        echo "Not the designated processing day. Current day: {$currentDayOfWeek}, Processing day: {$processingDay} (Friday)\n";
        echo "Weekly matching will run on Friday at 11:59 PM\n";
        exit(0);
    }
    
    // Check if this week has already been processed
    $db = Database::getInstance();
    $checkStmt = $db->prepare("SELECT id FROM weekly_income_reports WHERE week_start_date = ?");
    $checkStmt->execute([$weekStartDate]);
    
    if ($checkStmt->fetch()) {
        echo "Week {$weekStartDate} to {$weekEndDate} has already been processed.\n";
        echo "Skipping weekly matching.\n";
        exit(0);
    }
    
    echo "Starting weekly PV matching process...\n\n";
    
    // Run weekly matching for all users
    $result = $pvSystem->runWeeklyMatching($weekStartDate, $weekEndDate);
    
    if ($result !== false) {
        echo "\n" . str_repeat('-', 50) . "\n";
        echo "Weekly PV matching completed successfully!\n";
        echo "Summary:\n";
        echo "- Total users processed: {$result['processed']}\n";
        echo "- Users with income: {$result['users_with_income']}\n";
        echo "- Total income distributed: ₹" . number_format($result['total_income'], 2) . "\n";
        echo "- Total capping applied: ₹" . number_format($result['total_capping'], 2) . "\n";
        echo str_repeat('-', 50) . "\n";
        
        // Log the successful run
        $logStmt = $db->prepare("INSERT INTO system_logs (log_type, message, created_at) VALUES (?, ?, NOW())");
        $logStmt->execute(['cron', "Weekly PV matching completed. Processed {$result['processed']} users, distributed ₹" . number_format($result['total_income'], 2)]);
        
        // Send admin notification
        sendAdminNotification($weekStartDate, $weekEndDate, $result);
        
    } else {
        echo "Error occurred during weekly PV matching process\n";
        
        // Log the error
        $logStmt = $db->prepare("INSERT INTO system_logs (log_type, message, created_at) VALUES (?, ?, NOW())");
        $logStmt->execute(['error', "Weekly PV matching failed for week {$weekStartDate} to {$weekEndDate}."]);
    }
    
} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    
    // Log the fatal error
    try {
        $db = Database::getInstance();
        $logStmt = $db->prepare("INSERT INTO system_logs (log_type, message, created_at) VALUES (?, ?, NOW())");
        $logStmt->execute(['error', "Weekly PV matching fatal error: " . $e->getMessage()]);
    } catch (Exception $logError) {
        // If we can't log to database, log to file
        error_log("Weekly matching error: " . $e->getMessage());
    }
}

/**
 * Send admin notification about weekly income processing
 */
function sendAdminNotification($weekStartDate, $weekEndDate, $result) {
    try {
        $emailService = new EmailService();

        // Send email notification
        $emailResult = $emailService->sendWeeklyIncomeReport($weekStartDate, $weekEndDate, $result);

        if ($emailResult) {
            echo "\nAdmin notification sent successfully.\n";

            // Update report status to 'sent'
            $db = Database::getInstance();
            $updateStmt = $db->prepare("UPDATE weekly_income_reports SET report_sent_at = NOW(), report_status = 'sent' WHERE week_start_date = ?");
            $updateStmt->execute([$weekStartDate]);
        } else {
            throw new Exception("Failed to send email notification");
        }

    } catch (Exception $e) {
        echo "Failed to send admin notification: " . $e->getMessage() . "\n";

        // Update report status to 'failed'
        try {
            $db = Database::getInstance();
            $updateStmt = $db->prepare("UPDATE weekly_income_reports SET report_status = 'failed' WHERE week_start_date = ?");
            $updateStmt->execute([$weekStartDate]);
        } catch (Exception $updateError) {
            error_log("Failed to update report status: " . $updateError->getMessage());
        }
    }
}

echo str_repeat('=', 60) . "\n";
echo "Weekly PV matching process completed\n";
echo "End time: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat('=', 60) . "\n";
?>
