-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: shaktipure
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin`
--

DROP TABLE IF EXISTS `admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin`
--

LOCK TABLES `admin` WRITE;
/*!40000 ALTER TABLE `admin` DISABLE KEYS */;
INSERT INTO `admin` VALUES (1,'admin','<EMAIL>','admin123','System Administrator','+91-9999999999','active','2025-07-28 10:42:01','2025-07-28 10:43:23');
/*!40000 ALTER TABLE `admin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `binary_tree`
--

DROP TABLE IF EXISTS `binary_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `binary_tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `parent_id` varchar(20) DEFAULT NULL,
  `left_child` varchar(20) DEFAULT NULL,
  `right_child` varchar(20) DEFAULT NULL,
  `level` int(11) DEFAULT 0,
  `position` enum('left','right','root') DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user` (`user_id`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_level` (`level`),
  CONSTRAINT `binary_tree_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `binary_tree`
--

LOCK TABLES `binary_tree` WRITE;
/*!40000 ALTER TABLE `binary_tree` DISABLE KEYS */;
INSERT INTO `binary_tree` VALUES (1,'SP000001',NULL,'SP20257568','SP20256192',1,NULL,'2025-07-28 10:42:44','2025-07-28 10:52:36'),(2,'SP20257568','SP000001',NULL,NULL,2,'left','2025-07-28 10:44:30','2025-07-28 10:44:30'),(3,'SP20256192','SP000001',NULL,NULL,2,'right','2025-07-28 10:52:36','2025-07-28 10:52:36');
/*!40000 ALTER TABLE `binary_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config`
--

DROP TABLE IF EXISTS `config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL,
  `config_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `config_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config`
--

LOCK TABLES `config` WRITE;
/*!40000 ALTER TABLE `config` DISABLE KEYS */;
INSERT INTO `config` VALUES (1,'pv_rate','0.10','PV to INR conversion rate (1 PV = ₹0.10)',NULL,'2025-07-28 10:42:01'),(2,'daily_capping','130000.00','Maximum daily income per user in INR',NULL,'2025-07-28 10:42:01'),(3,'min_withdrawal','500.00','Minimum withdrawal amount in INR',NULL,'2025-07-28 10:42:01'),(4,'razorpay_mode','test','Razorpay mode: test or live',NULL,'2025-07-28 10:42:01'),(5,'company_name','ShaktiPure MLM','Company name',NULL,'2025-07-28 10:42:01'),(6,'support_email','<EMAIL>','Support email address',NULL,'2025-07-28 10:42:01'),(7,'support_phone','+91-9999999999','Support phone number',NULL,'2025-07-28 10:42:01');
/*!40000 ALTER TABLE `config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `downline_pv_cache`
--

DROP TABLE IF EXISTS `downline_pv_cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `downline_pv_cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `cache_period` varchar(20) NOT NULL,
  `cache_type` enum('daily','weekly','monthly') NOT NULL,
  `left_downline_pv` decimal(12,2) NOT NULL DEFAULT 0.00,
  `right_downline_pv` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_downline_users` int(11) NOT NULL DEFAULT 0,
  `cache_generated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `cache_expires_at` timestamp NULL DEFAULT NULL,
  `is_valid` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_cache_period` (`user_id`,`cache_period`,`cache_type`),
  KEY `idx_user_cache_type` (`user_id`,`cache_type`),
  KEY `idx_cache_expires` (`cache_expires_at`),
  KEY `idx_is_valid` (`is_valid`),
  CONSTRAINT `downline_pv_cache_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `downline_pv_cache`
--

LOCK TABLES `downline_pv_cache` WRITE;
/*!40000 ALTER TABLE `downline_pv_cache` DISABLE KEYS */;
/*!40000 ALTER TABLE `downline_pv_cache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `franchise`
--

DROP TABLE IF EXISTS `franchise`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `franchise` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `franchise_code` varchar(20) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `address` text DEFAULT NULL,
  `commission_rate` decimal(5,2) DEFAULT 5.00,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `franchise_code` (`franchise_code`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `franchise_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `franchise`
--

LOCK TABLES `franchise` WRITE;
/*!40000 ALTER TABLE `franchise` DISABLE KEYS */;
INSERT INTO `franchise` VALUES (1,'FR0001','franchise','<EMAIL>','franchise123','Master Franchise','+91-8888888888','Franchise Address',5.00,'active',1,'2025-07-28 10:42:44','2025-07-28 10:42:44');
/*!40000 ALTER TABLE `franchise` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `income_logs`
--

DROP TABLE IF EXISTS `income_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `income_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `left_pv` decimal(10,2) NOT NULL,
  `right_pv` decimal(10,2) NOT NULL,
  `matched_pv` decimal(10,2) NOT NULL,
  `income_amount` decimal(10,2) NOT NULL,
  `capping_applied` decimal(10,2) DEFAULT 0.00,
  `carry_forward_left` decimal(10,2) DEFAULT 0.00,
  `carry_forward_right` decimal(10,2) DEFAULT 0.00,
  `matching_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_date` (`user_id`,`matching_date`),
  KEY `idx_matching_date` (`matching_date`),
  CONSTRAINT `income_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `income_logs`
--

LOCK TABLES `income_logs` WRITE;
/*!40000 ALTER TABLE `income_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `income_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `income_processing_status`
--

DROP TABLE IF EXISTS `income_processing_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `income_processing_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `processing_type` enum('daily','weekly','monthly') NOT NULL,
  `processing_period` varchar(20) NOT NULL,
  `period_start_date` date NOT NULL,
  `period_end_date` date NOT NULL,
  `status` enum('pending','processing','completed','failed','cancelled') DEFAULT 'pending',
  `total_users_processed` int(11) DEFAULT 0,
  `total_users_with_income` int(11) DEFAULT 0,
  `total_income_distributed` decimal(12,2) DEFAULT 0.00,
  `total_capping_applied` decimal(12,2) DEFAULT 0.00,
  `processing_started_at` timestamp NULL DEFAULT NULL,
  `processing_completed_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `created_by_type` enum('system','admin','cron') DEFAULT 'system',
  `created_by_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_processing_period` (`processing_type`,`processing_period`),
  KEY `idx_status` (`status`),
  KEY `idx_processing_type_period` (`processing_type`,`processing_period`),
  KEY `idx_period_dates` (`period_start_date`,`period_end_date`),
  KEY `idx_processing_started` (`processing_started_at`),
  KEY `idx_processing_completed` (`processing_completed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `income_processing_status`
--

LOCK TABLES `income_processing_status` WRITE;
/*!40000 ALTER TABLE `income_processing_status` DISABLE KEYS */;
/*!40000 ALTER TABLE `income_processing_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `login_logs`
--

DROP TABLE IF EXISTS `login_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('admin','franchise','user') NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `login_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `logout_time` timestamp NULL DEFAULT NULL,
  `status` enum('success','failed') DEFAULT 'success',
  PRIMARY KEY (`id`),
  KEY `idx_user_type` (`user_type`,`user_id`),
  KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `login_logs`
--

LOCK TABLES `login_logs` WRITE;
/*!40000 ALTER TABLE `login_logs` DISABLE KEYS */;
INSERT INTO `login_logs` VALUES (1,'user','SP000001','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-28 10:43:08','2025-07-28 10:43:49','success'),(2,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-28 11:04:35',NULL,'success'),(3,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-29 05:50:30',NULL,'success'),(4,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-29 09:07:38','2025-07-29 09:08:38','success'),(5,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-29 09:17:47','2025-07-29 09:17:55','success'),(6,'user','SP000001','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-29 09:18:00','2025-07-29 09:18:37','success'),(7,'user','SP000001','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-30 06:26:23',NULL,'success'),(8,'user','SP000001','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-30 06:29:56','2025-07-30 06:53:26','success'),(9,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-31 08:30:40','2025-07-31 08:48:00','success'),(10,'user','SP20257568','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-31 08:48:16','2025-07-31 08:50:08','success'),(11,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-04 15:07:32',NULL,'success'),(12,'admin','1','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36','2025-08-08 10:27:39','2025-08-08 10:28:44','success');
/*!40000 ALTER TABLE `login_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_assignment_requests`
--

DROP TABLE IF EXISTS `product_assignment_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_assignment_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `franchise_id` int(11) NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) DEFAULT 1,
  `pv_side` enum('left','right') DEFAULT 'left',
  `description` text DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `requested_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `processed_at` timestamp NULL DEFAULT NULL,
  `processed_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_franchise_status` (`franchise_id`,`status`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_status_requested` (`status`,`requested_at`),
  KEY `idx_requested_at` (`requested_at`),
  CONSTRAINT `product_assignment_requests_ibfk_1` FOREIGN KEY (`franchise_id`) REFERENCES `franchise` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_assignment_requests_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `product_assignment_requests_ibfk_3` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_assignment_requests_ibfk_4` FOREIGN KEY (`processed_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_assignment_requests`
--

LOCK TABLES `product_assignment_requests` WRITE;
/*!40000 ALTER TABLE `product_assignment_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_assignment_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_code` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `pv_value` decimal(10,2) NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `product_code` (`product_code`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES (1,'SP002','Gas saf','diuhfdfiguhouguho','SP002_688863340a79e.png',4599.00,2500.00,'active',1,'2025-07-29 05:59:16','2025-07-29 05:59:16'),(2,'sp0002','Braclete','THIS IS TESTING PRODUCT','sp0002_68888f92e9a02.png',4599.00,2500.00,'active',1,'2025-07-29 09:08:34','2025-07-29 09:08:34');
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_orders`
--

DROP TABLE IF EXISTS `purchase_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(30) NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) DEFAULT 1,
  `total_amount` decimal(10,2) NOT NULL,
  `pv_amount` decimal(10,2) NOT NULL,
  `placement_side` enum('left','right') NOT NULL,
  `payment_method` enum('razorpay','manual') DEFAULT 'razorpay',
  `payment_id` varchar(100) DEFAULT NULL,
  `payment_status` enum('pending','completed','failed','refunded') DEFAULT 'pending',
  `order_status` enum('pending','confirmed','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  KEY `idx_user_status` (`user_id`,`order_status`),
  KEY `idx_payment_status` (`payment_status`),
  CONSTRAINT `purchase_orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_orders_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_orders`
--

LOCK TABLES `purchase_orders` WRITE;
/*!40000 ALTER TABLE `purchase_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `purchase_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_income_audit_trail`
--

DROP TABLE IF EXISTS `pv_income_audit_trail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_income_audit_trail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `processing_period` varchar(20) NOT NULL,
  `processing_type` enum('daily','weekly','monthly') NOT NULL,
  `left_pv_available` decimal(10,2) NOT NULL DEFAULT 0.00,
  `right_pv_available` decimal(10,2) NOT NULL DEFAULT 0.00,
  `left_pv_carried` decimal(10,2) NOT NULL DEFAULT 0.00,
  `right_pv_carried` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_left_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_right_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `matched_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `gross_income` decimal(10,2) NOT NULL DEFAULT 0.00,
  `capping_applied` decimal(10,2) NOT NULL DEFAULT 0.00,
  `deductions_applied` decimal(10,2) NOT NULL DEFAULT 0.00,
  `net_income` decimal(10,2) NOT NULL DEFAULT 0.00,
  `carry_forward_left` decimal(10,2) NOT NULL DEFAULT 0.00,
  `carry_forward_right` decimal(10,2) NOT NULL DEFAULT 0.00,
  `pv_transactions_used` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`pv_transactions_used`)),
  `calculation_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`calculation_details`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_period` (`user_id`,`processing_period`),
  KEY `idx_processing_type_period` (`processing_type`,`processing_period`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_matched_pv` (`matched_pv`),
  KEY `idx_net_income` (`net_income`),
  CONSTRAINT `pv_income_audit_trail_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_income_audit_trail`
--

LOCK TABLES `pv_income_audit_trail` WRITE;
/*!40000 ALTER TABLE `pv_income_audit_trail` DISABLE KEYS */;
/*!40000 ALTER TABLE `pv_income_audit_trail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_performance_metrics`
--

DROP TABLE IF EXISTS `pv_performance_metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_performance_metrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `metric_type` enum('processing_time','users_processed','memory_usage','database_queries') NOT NULL,
  `processing_period` varchar(20) NOT NULL,
  `processing_type` enum('daily','weekly','monthly') NOT NULL,
  `metric_value` decimal(15,4) NOT NULL,
  `metric_unit` varchar(20) NOT NULL,
  `additional_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`additional_data`)),
  `recorded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_metric_type_period` (`metric_type`,`processing_period`),
  KEY `idx_processing_type_period` (`processing_type`,`processing_period`),
  KEY `idx_recorded_at` (`recorded_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_performance_metrics`
--

LOCK TABLES `pv_performance_metrics` WRITE;
/*!40000 ALTER TABLE `pv_performance_metrics` DISABLE KEYS */;
/*!40000 ALTER TABLE `pv_performance_metrics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_processing_locks`
--

DROP TABLE IF EXISTS `pv_processing_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_processing_locks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lock_type` enum('daily_processing','weekly_processing','user_processing') NOT NULL,
  `lock_key` varchar(100) NOT NULL,
  `locked_by` varchar(100) NOT NULL,
  `locked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL,
  `process_id` varchar(50) DEFAULT NULL,
  `status` enum('active','expired','released') DEFAULT 'active',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_lock` (`lock_type`,`lock_key`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_status` (`status`),
  KEY `idx_locked_by` (`locked_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_processing_locks`
--

LOCK TABLES `pv_processing_locks` WRITE;
/*!40000 ALTER TABLE `pv_processing_locks` DISABLE KEYS */;
/*!40000 ALTER TABLE `pv_processing_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_transactions`
--

DROP TABLE IF EXISTS `pv_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `transaction_type` enum('purchase','bonus','manual') NOT NULL,
  `pv_amount` decimal(10,2) NOT NULL,
  `side` enum('left','right') NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `reference_id` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_by_type` enum('admin','franchise','system') DEFAULT 'system',
  `created_by_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `idx_user_side` (`user_id`,`side`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `pv_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `pv_transactions_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_transactions`
--

LOCK TABLES `pv_transactions` WRITE;
/*!40000 ALTER TABLE `pv_transactions` DISABLE KEYS */;
INSERT INTO `pv_transactions` VALUES (7,'SP000001','manual',319.00,'left',NULL,NULL,'Test PV - Left Side','admin',1,'2025-07-28 11:00:07'),(8,'SP000001','manual',403.00,'right',NULL,NULL,'Test PV - Right Side','admin',1,'2025-07-28 11:00:07'),(9,'SP20257568','manual',310.00,'left',NULL,NULL,'Test PV - Left Side','admin',1,'2025-07-28 11:00:07'),(10,'SP20257568','manual',109.00,'right',NULL,NULL,'Test PV - Right Side','admin',1,'2025-07-28 11:00:07'),(11,'SP20256192','manual',177.00,'left',NULL,NULL,'Test PV - Left Side','admin',1,'2025-07-28 11:00:07'),(12,'SP20256192','manual',319.00,'right',NULL,NULL,'Test PV - Right Side','admin',1,'2025-07-28 11:00:07');
/*!40000 ALTER TABLE `pv_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pv_usage_tracking`
--

DROP TABLE IF EXISTS `pv_usage_tracking`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pv_usage_tracking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `pv_transaction_id` int(11) NOT NULL,
  `side` enum('left','right') NOT NULL,
  `original_amount` decimal(10,2) NOT NULL,
  `used_amount` decimal(10,2) DEFAULT 0.00,
  `remaining_amount` decimal(10,2) NOT NULL,
  `week_used` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `pv_transaction_id` (`pv_transaction_id`),
  KEY `idx_user_remaining` (`user_id`,`remaining_amount`),
  KEY `idx_side` (`side`),
  KEY `idx_week_used` (`week_used`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `pv_usage_tracking_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `pv_usage_tracking_ibfk_2` FOREIGN KEY (`pv_transaction_id`) REFERENCES `pv_transactions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pv_usage_tracking`
--

LOCK TABLES `pv_usage_tracking` WRITE;
/*!40000 ALTER TABLE `pv_usage_tracking` DISABLE KEYS */;
INSERT INTO `pv_usage_tracking` VALUES (1,'SP000001',7,'left',319.00,319.00,0.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:52'),(2,'SP000001',8,'right',403.00,319.00,84.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:52'),(3,'SP20257568',9,'left',310.00,109.00,201.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:59'),(4,'SP20257568',10,'right',109.00,109.00,0.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:59'),(5,'SP20256192',11,'left',177.00,177.00,0.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:59'),(6,'SP20256192',12,'right',319.00,177.00,142.00,'2025-07-28','2025-07-28 11:01:15','2025-07-28 11:02:59');
/*!40000 ALTER TABLE `pv_usage_tracking` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_logs`
--

DROP TABLE IF EXISTS `system_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `log_type` enum('info','warning','error','cron') NOT NULL,
  `message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_logs`
--

LOCK TABLES `system_logs` WRITE;
/*!40000 ALTER TABLE `system_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_achievements`
--

DROP TABLE IF EXISTS `user_achievements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_achievements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `achievement_type` enum('level','award') NOT NULL,
  `previous_value` varchar(100) DEFAULT NULL,
  `new_value` varchar(100) NOT NULL,
  `assigned_by` int(11) NOT NULL,
  `admin_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `assigned_by` (`assigned_by`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_achievement_type` (`achievement_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `user_achievements_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `user_achievements_ibfk_2` FOREIGN KEY (`assigned_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_achievements`
--

LOCK TABLES `user_achievements` WRITE;
/*!40000 ALTER TABLE `user_achievements` DISABLE KEYS */;
INSERT INTO `user_achievements` VALUES (1,'SP20257568','award',NULL,'Rising Star',1,'','2025-07-31 08:47:20'),(2,'SP000001','level','Beginner','Intermediate',1,'Test level assignment','2025-07-31 08:49:00'),(3,'SP000001','award',NULL,'Top Performer',1,'Test award assignment','2025-07-31 08:49:00');
/*!40000 ALTER TABLE `user_achievements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `address` text DEFAULT NULL,
  `sponsor_id` varchar(20) DEFAULT NULL,
  `franchise_id` int(11) DEFAULT NULL,
  `placement_side` enum('left','right') DEFAULT NULL,
  `self_pv` decimal(10,2) DEFAULT 0.00,
  `upline_pv` decimal(10,2) DEFAULT 0.00,
  `user_level` enum('Beginner','Intermediate','Expert') DEFAULT 'Beginner',
  `current_award` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `registration_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `email` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `username` (`username`),
  KEY `franchise_id` (`franchise_id`),
  KEY `idx_sponsor` (`sponsor_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_level` (`user_level`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`franchise_id`) REFERENCES `franchise` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'SP000001','master','master123','Master User','+91-9999999999','Master Address',NULL,NULL,NULL,0.00,0.00,'Intermediate','Top Performer','active','2025-07-28 10:42:44','2025-07-28 10:42:44','2025-07-31 08:49:00',NULL),(2,'SP20257568','abhisheksharma276','123456','Abhishek Sharma','7041101901','tHIS IS TEST ADDRESS','SP000001',1,'left',0.00,0.00,'Beginner','Rising Star','active','2025-07-28 10:44:30','2025-07-28 10:44:30','2025-07-31 08:47:20',NULL),(4,'SP20256192','sanjaysharma480','123456','sanjay sharma','7041101901','sdjfnjadsb','SP000001',1,'right',0.00,0.00,'Beginner',NULL,'active','2025-07-28 10:52:36','2025-07-28 10:52:36','2025-07-28 10:52:36','<EMAIL>');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wallet`
--

DROP TABLE IF EXISTS `wallet`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `balance` decimal(12,2) DEFAULT 0.00,
  `total_earned` decimal(12,2) DEFAULT 0.00,
  `total_withdrawn` decimal(12,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_wallet` (`user_id`),
  CONSTRAINT `wallet_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wallet`
--

LOCK TABLES `wallet` WRITE;
/*!40000 ALTER TABLE `wallet` DISABLE KEYS */;
INSERT INTO `wallet` VALUES (1,'SP000001',27.12,27.12,0.00,'2025-07-28 10:42:44','2025-07-28 11:02:52'),(2,'SP20257568',9.27,9.27,0.00,'2025-07-28 10:44:30','2025-07-28 11:02:59'),(3,'SP20256192',15.05,15.05,0.00,'2025-07-28 10:52:36','2025-07-28 11:02:59');
/*!40000 ALTER TABLE `wallet` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wallet_transactions`
--

DROP TABLE IF EXISTS `wallet_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `transaction_type` enum('credit','debit') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `reference_type` enum('pv_matching','withdrawal','bonus','manual') NOT NULL,
  `reference_id` varchar(50) DEFAULT NULL,
  `balance_before` decimal(12,2) NOT NULL,
  `balance_after` decimal(12,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_type` (`user_id`,`transaction_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `wallet_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wallet_transactions`
--

LOCK TABLES `wallet_transactions` WRITE;
/*!40000 ALTER TABLE `wallet_transactions` DISABLE KEYS */;
INSERT INTO `wallet_transactions` VALUES (1,'SP000001','credit',27.12,'Weekly PV Matching Income for week 2025-07-28 to 2025-08-03 (Net after deductions)','pv_matching',NULL,0.00,27.12,'2025-07-28 11:02:52'),(2,'SP20257568','credit',9.27,'Weekly PV Matching Income for week 2025-07-28 to 2025-08-03 (Net after deductions)','pv_matching',NULL,0.00,9.27,'2025-07-28 11:02:59'),(3,'SP20256192','credit',15.05,'Weekly PV Matching Income for week 2025-07-28 to 2025-08-03 (Net after deductions)','pv_matching',NULL,0.00,15.05,'2025-07-28 11:02:59');
/*!40000 ALTER TABLE `wallet_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `weekly_income_logs`
--

DROP TABLE IF EXISTS `weekly_income_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `weekly_income_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `week_start_date` date NOT NULL,
  `week_end_date` date NOT NULL,
  `left_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `right_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `matched_pv` decimal(10,2) NOT NULL DEFAULT 0.00,
  `gross_income_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `service_charge` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tds_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `income_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `weekly_capping_applied` decimal(10,2) DEFAULT 0.00,
  `carry_forward_left` decimal(10,2) DEFAULT 0.00,
  `carry_forward_right` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_week` (`user_id`,`week_start_date`),
  KEY `idx_user_week` (`user_id`,`week_start_date`),
  KEY `idx_week_start_date` (`week_start_date`),
  KEY `idx_income_amount` (`income_amount`),
  CONSTRAINT `weekly_income_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `weekly_income_logs`
--

LOCK TABLES `weekly_income_logs` WRITE;
/*!40000 ALTER TABLE `weekly_income_logs` DISABLE KEYS */;
INSERT INTO `weekly_income_logs` VALUES (1,'SP000001','2025-07-21','2025-07-27',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:30'),(2,'SP20257568','2025-07-21','2025-07-27',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:30'),(3,'SP20256192','2025-07-21','2025-07-27',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:30'),(4,'SP000001','2025-07-07','2025-07-13',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:49'),(5,'SP20257568','2025-07-07','2025-07-13',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:49'),(6,'SP20256192','2025-07-07','2025-07-13',0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,'2025-07-28 10:58:49'),(10,'SP000001','2025-07-28','2025-08-03',319.00,403.00,319.00,31.90,3.19,1.60,27.12,0.00,0.00,84.00,'2025-07-28 11:02:52'),(11,'SP20257568','2025-07-28','2025-08-03',310.00,109.00,109.00,10.90,1.09,0.55,9.27,0.00,201.00,0.00,'2025-07-28 11:02:59'),(12,'SP20256192','2025-07-28','2025-08-03',177.00,319.00,177.00,17.70,1.77,0.89,15.05,0.00,0.00,142.00,'2025-07-28 11:02:59'),(13,'SP000001','2025-08-04','2025-08-10',0.00,168.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,168.00,'2025-07-28 11:03:41'),(14,'SP20257568','2025-08-04','2025-08-10',402.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,402.00,0.00,'2025-07-28 11:03:41'),(15,'SP20256192','2025-08-04','2025-08-10',0.00,284.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,284.00,'2025-07-28 11:03:41');
/*!40000 ALTER TABLE `weekly_income_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `weekly_income_reports`
--

DROP TABLE IF EXISTS `weekly_income_reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `weekly_income_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `week_start_date` date NOT NULL,
  `week_end_date` date NOT NULL,
  `total_users_earned` int(11) NOT NULL DEFAULT 0,
  `total_gross_income` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_service_charge` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_tds_amount` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_income_distributed` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_capping_applied` decimal(12,2) DEFAULT 0.00,
  `report_status` enum('processing','generated','sent','failed') DEFAULT 'processing',
  `report_generated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_week` (`week_start_date`),
  KEY `idx_week_start_date` (`week_start_date`),
  KEY `idx_report_status` (`report_status`),
  KEY `idx_generated_at` (`report_generated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `weekly_income_reports`
--

LOCK TABLES `weekly_income_reports` WRITE;
/*!40000 ALTER TABLE `weekly_income_reports` DISABLE KEYS */;
INSERT INTO `weekly_income_reports` VALUES (1,'2025-07-21','2025-07-27',0,0.00,0.00,0.00,0.00,0.00,'generated','2025-07-28 10:58:30'),(3,'2025-07-07','2025-07-13',0,0.00,0.00,0.00,0.00,0.00,'generated','2025-07-28 11:01:22'),(8,'2025-07-28','2025-08-03',0,0.00,0.00,0.00,0.00,0.00,'generated','2025-08-08 10:28:29'),(9,'2025-08-04','2025-08-10',0,0.00,0.00,0.00,0.00,0.00,'generated','2025-07-28 11:03:41');
/*!40000 ALTER TABLE `weekly_income_reports` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `withdrawals`
--

DROP TABLE IF EXISTS `withdrawals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `withdrawals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `bank_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`bank_details`)),
  `status` enum('pending','approved','rejected','processed') DEFAULT 'pending',
  `admin_notes` text DEFAULT NULL,
  `processed_by` int(11) DEFAULT NULL,
  `requested_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `processed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_status` (`status`),
  KEY `idx_user_status` (`user_id`,`status`),
  CONSTRAINT `withdrawals_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `withdrawals_ibfk_2` FOREIGN KEY (`processed_by`) REFERENCES `admin` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `withdrawals`
--

LOCK TABLES `withdrawals` WRITE;
/*!40000 ALTER TABLE `withdrawals` DISABLE KEYS */;
/*!40000 ALTER TABLE `withdrawals` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-09 21:37:43
