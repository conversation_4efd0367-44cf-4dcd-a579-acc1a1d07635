<?php
/**
 * Enhanced PV (Point Value) System Class
 * MLM Binary Plan System with Comprehensive Duplicate Prevention
 * 
 * Features:
 * - Prevents duplicate PV income generation
 * - FIFO PV usage tracking
 * - Complete downline PV aggregation
 * - Production-level error handling
 * - Performance optimization
 * - Comprehensive audit trails
 */

require_once __DIR__ . '/BinaryTree.php';
require_once __DIR__ . '/Wallet.php';
require_once __DIR__ . '/../config/Connection.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/WeeklyDateHelper.php';

class EnhancedPVSystem {
    private $db;
    private $config;
    private $binaryTree;
    private $wallet;
    private $logger;
    
    // Processing constants
    const PROCESSING_LOCK_TIMEOUT = 3600; // 1 hour
    const BATCH_SIZE = 100;
    const MAX_RETRY_ATTEMPTS = 3;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
        $this->binaryTree = new BinaryTree();
        $this->wallet = new Wallet();
        $this->initializeLogger();
    }
    
    /**
     * Initialize logging system
     */
    private function initializeLogger() {
        $this->logger = [
            'info' => function($message, $context = []) {
                $this->logMessage('info', 'pv_processing', $message, $context);
            },
            'error' => function($message, $context = []) {
                $this->logMessage('error', 'pv_processing', $message, $context);
            },
            'debug' => function($message, $context = []) {
                $this->logMessage('debug', 'pv_processing', $message, $context);
            }
        ];
    }
    
    /**
     * Add PV to user's account with comprehensive tracking
     */
    public function addPV($userId, $pvAmount, $side, $transactionType = 'purchase', $productId = null, $referenceId = null, $description = '', $sourceUserId = null) {
        try {
            $this->db->beginTransaction();
            
            // Validate input parameters
            $this->validatePVInput($userId, $pvAmount, $side, $transactionType);
            
            // Insert PV transaction
            $transactionId = $this->insertPVTransaction($userId, $pvAmount, $side, $transactionType, $productId, $referenceId, $description, $sourceUserId);
            
            // Initialize PV usage tracking
            $this->initializePVUsageTracking($transactionId, $userId, $side, $pvAmount);
            
            // Update user's PV totals if it's self PV
            if ($side === 'self') {
                $this->updateUserSelfPV($userId, $pvAmount);
            }
            
            // Propagate PV to upline if needed
            if ($side === 'self' && $transactionType === 'purchase') {
                $this->propagatePVToUpline($userId, $pvAmount);
            }
            
            // Invalidate downline cache for affected users
            $this->invalidateDownlineCache($userId);
            
            $this->db->commit();
            
            $this->logger['info']("PV added successfully", [
                'user_id' => $userId,
                'pv_amount' => $pvAmount,
                'side' => $side,
                'transaction_id' => $transactionId
            ]);
            
            return $transactionId;
            
        } catch (Exception $e) {
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }
            
            $this->logger['error']("Failed to add PV", [
                'user_id' => $userId,
                'pv_amount' => $pvAmount,
                'side' => $side,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Validate PV input parameters
     */
    private function validatePVInput($userId, $pvAmount, $side, $transactionType) {
        if (empty($userId)) {
            throw new InvalidArgumentException("User ID is required");
        }
        
        if (!is_numeric($pvAmount) || $pvAmount <= 0) {
            throw new InvalidArgumentException("PV amount must be a positive number");
        }
        
        if (!in_array($side, ['left', 'right', 'self', 'upline'])) {
            throw new InvalidArgumentException("Invalid side parameter");
        }
        
        if (!in_array($transactionType, ['purchase', 'bonus', 'manual', 'downline_bonus', 'self'])) {
            throw new InvalidArgumentException("Invalid transaction type");
        }
        
        // Check if user exists
        $userStmt = $this->db->prepare("SELECT user_id FROM users WHERE user_id = ? AND status = 'active'");
        $userStmt->execute([$userId]);
        if (!$userStmt->fetch()) {
            throw new InvalidArgumentException("User not found or inactive");
        }
    }
    
    /**
     * Insert PV transaction record
     */
    private function insertPVTransaction($userId, $pvAmount, $side, $transactionType, $productId, $referenceId, $description, $sourceUserId) {
        try {
            // Try new schema first
            $stmt = $this->db->prepare("
                INSERT INTO pv_transactions
                (user_id, transaction_type, pv_amount, side, product_id, reference_id, description, source_user_id, processing_status, created_by_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'system')
            ");

            $stmt->execute([
                $userId,
                $transactionType,
                $pvAmount,
                $side,
                $productId,
                $referenceId,
                $description,
                $sourceUserId
            ]);
        } catch (Exception $e) {
            // Fall back to old schema
            $stmt = $this->db->prepare("
                INSERT INTO pv_transactions
                (user_id, transaction_type, pv_amount, side, product_id, reference_id, description, created_by_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'system')
            ");

            $stmt->execute([
                $userId,
                $transactionType,
                $pvAmount,
                $side,
                $productId,
                $referenceId,
                $description
            ]);
        }

        return $this->db->lastInsertId();
    }
    
    /**
     * Initialize PV usage tracking for new transaction
     */
    private function initializePVUsageTracking($transactionId, $userId, $side, $pvAmount) {
        $stmt = $this->db->prepare("
            INSERT INTO pv_usage_tracking 
            (pv_transaction_id, user_id, side, original_amount, remaining_amount, status) 
            VALUES (?, ?, ?, ?, ?, 'available')
        ");
        
        $stmt->execute([$transactionId, $userId, $side, $pvAmount, $pvAmount]);
    }
    
    /**
     * Update user's self PV total
     */
    private function updateUserSelfPV($userId, $pvAmount) {
        $stmt = $this->db->prepare("UPDATE users SET self_pv = self_pv + ? WHERE user_id = ?");
        $stmt->execute([$pvAmount, $userId]);

        // Check and update user activation status
        $this->checkUserActivation($userId);
    }
    
    /**
     * Propagate PV to upline sponsors
     */
    private function propagatePVToUpline($userId, $pvAmount) {
        $currentUserId = $userId;
        $level = 0;
        $maxLevels = 10; // Prevent infinite loops

        while ($level < $maxLevels) {
            // Get current node
            $currentNode = $this->binaryTree->getNode($currentUserId);
            if (!$currentNode || !$currentNode['parent_id']) {
                break; // Reached root
            }

            $parentId = $currentNode['parent_id'];
            $placementSide = $currentNode['position']; // 'left' or 'right'

            // Add PV to parent's appropriate side
            $this->addDirectPV($parentId, $pvAmount, $placementSide, 'downline_bonus', null, "DOWNLINE_FROM_{$currentUserId}", "PV from downline user {$currentUserId}", $currentUserId);

            // Move up to next level
            $currentUserId = $parentId;
            $level++;
        }
    }
    
    /**
     * Add PV directly without triggering propagation (to avoid recursion)
     */
    private function addDirectPV($userId, $pvAmount, $side, $transactionType, $productId, $referenceId, $description, $sourceUserId) {
        // Insert transaction without triggering propagation
        $transactionId = $this->insertPVTransaction($userId, $pvAmount, $side, $transactionType, $productId, $referenceId, $description, $sourceUserId);
        
        // Initialize tracking
        $this->initializePVUsageTracking($transactionId, $userId, $side, $pvAmount);
        
        return $transactionId;
    }
    
    /**
     * Invalidate downline cache for affected users
     */
    private function invalidateDownlineCache($userId) {
        // Invalidate cache for this user and all upline users
        $stmt = $this->db->prepare("UPDATE downline_pv_cache SET is_valid = FALSE WHERE user_id = ?");
        $stmt->execute([$userId]);

        // Also invalidate upline cache
        $uplineUsers = $this->binaryTree->getUpline($userId);
        foreach ($uplineUsers as $uplineUserId) {
            $stmt->execute([$uplineUserId]);
        }
    }
    
    /**
     * Get available PV for income calculation (unused PV only)
     */
    public function getAvailablePV($userId, $processingPeriod = null) {
        // Ensure all PV transactions are tracked
        $this->ensureAllPVTracked($userId);

        // Build query with optional processing period filter to prevent PV reuse
        $whereClause = "user_id = ? AND remaining_amount > 0 AND status IN ('available', 'partially_used')";
        $params = [$userId];

        if ($processingPeriod) {
            $whereClause .= " AND (processing_period IS NULL OR processing_period != ?)";
            $params[] = $processingPeriod;
        }

        $stmt = $this->db->prepare("
            SELECT
                SUM(CASE WHEN side = 'left' THEN remaining_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN remaining_amount ELSE 0 END) as right_pv,
                SUM(CASE WHEN side = 'self' THEN remaining_amount ELSE 0 END) as self_pv
            FROM pv_usage_tracking
            WHERE {$whereClause}
        ");

        $stmt->execute($params);
        $result = $stmt->fetch();

        return [
            'left_pv' => (float) ($result['left_pv'] ?? 0),
            'right_pv' => (float) ($result['right_pv'] ?? 0),
            'self_pv' => (float) ($result['self_pv'] ?? 0)
        ];
    }

    /**
     * Get complete downline PV totals with caching
     */
    public function getDownlinePVTotals($userId, $processingPeriod = null, $useCache = true) {
        // Check cache first if enabled
        if ($useCache) {
            $cachedData = $this->getDownlinePVFromCache($userId, $processingPeriod);
            if ($cachedData) {
                return $cachedData;
            }
        }

        // Calculate fresh downline PV
        $leftLeg = $this->binaryTree->getLeftLeg($userId);
        $rightLeg = $this->binaryTree->getRightLeg($userId);

        $leftPV = $this->calculateLegPV($leftLeg);
        $rightPV = $this->calculateLegPV($rightLeg);

        $result = [
            'left_pv' => $leftPV,
            'right_pv' => $rightPV,
            'total_downline_users' => count($leftLeg) + count($rightLeg)
        ];

        // Cache the result if caching is enabled
        if ($useCache) {
            $this->cacheDownlinePV($userId, $processingPeriod, $result);
        }

        return $result;
    }

    /**
     * Calculate PV for a leg of users
     */
    private function calculateLegPV($userIds) {
        if (empty($userIds)) {
            return 0;
        }

        $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
        $stmt = $this->db->prepare("
            SELECT SUM(remaining_amount) as total_pv
            FROM pv_usage_tracking
            WHERE user_id IN ($placeholders) AND remaining_amount > 0
        ");

        $stmt->execute($userIds);
        $result = $stmt->fetch();

        return (float) ($result['total_pv'] ?? 0);
    }

    /**
     * Process income generation for a user
     */
    public function processUserIncome($userId, $processingPeriod, $processingType = 'weekly') {
        try {
            // Acquire processing lock
            if (!$this->acquireProcessingLock('user_processing', $userId)) {
                throw new Exception("Unable to acquire processing lock for user {$userId}");
            }

            // Check if already processed for this period
            if ($this->isAlreadyProcessed($userId, $processingPeriod, $processingType)) {
                $this->logger['info']("User already processed for period", [
                    'user_id' => $userId,
                    'processing_period' => $processingPeriod,
                    'processing_type' => $processingType
                ]);
                return ['income_amount' => 0, 'already_processed' => true];
            }

            $this->db->beginTransaction();

            // Get available PV
            $ownPV = $this->getAvailablePV($userId, $processingPeriod);
            $downlinePV = $this->getDownlinePVTotals($userId, $processingPeriod);

            // Get carry forward from previous period
            $carryForward = $this->getCarryForward($userId, $processingPeriod, $processingType);

            // Calculate total PV including carry forward
            $totalLeftPV = $ownPV['left_pv'] + $downlinePV['left_pv'] + $carryForward['left_pv'];
            $totalRightPV = $ownPV['right_pv'] + $downlinePV['right_pv'] + $carryForward['right_pv'];

            // Calculate matching PV (minimum of left and right)
            $matchedPV = min($totalLeftPV, $totalRightPV);

            $result = [
                'user_id' => $userId,
                'processing_period' => $processingPeriod,
                'processing_type' => $processingType,
                'total_left_pv' => $totalLeftPV,
                'total_right_pv' => $totalRightPV,
                'matched_pv' => $matchedPV,
                'gross_income' => 0,
                'net_income' => 0,
                'capping_applied' => 0,
                'deductions' => 0,
                'carry_forward_left' => 0,
                'carry_forward_right' => 0
            ];

            if ($matchedPV > 0) {
                // Calculate income
                $incomeResult = $this->calculateIncome($matchedPV, $userId, $processingType);
                $result = array_merge($result, $incomeResult);

                // Validate PV reuse prevention before marking as used
                try {
                    $this->validatePVReusePrevention($userId, $processingPeriod);
                } catch (Exception $e) {
                    // Log the validation error but continue processing
                    error_log("PV reuse validation warning for user {$userId}: " . $e->getMessage());
                }

                // Mark PV as used
                $this->markPVAsUsed($userId, $matchedPV, $processingPeriod);

                // Credit income to wallet
                if ($result['net_income'] > 0) {
                    $this->wallet->credit(
                        $userId,
                        $result['net_income'],
                        "Income for period {$processingPeriod}",
                        'pv_matching',
                        $processingPeriod
                    );
                }
            }

            // Calculate carry forward
            $result['carry_forward_left'] = $totalLeftPV - $matchedPV;
            $result['carry_forward_right'] = $totalRightPV - $matchedPV;

            // Record audit trail
            $this->recordIncomeAuditTrail($result);

            // Record income log
            $this->recordIncomeLog($result, $processingType);

            $this->db->commit();

            $this->logger['info']("Income processed successfully", $result);

            return $result;

        } catch (Exception $e) {
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }

            $this->logger['error']("Failed to process user income", [
                'user_id' => $userId,
                'processing_period' => $processingPeriod,
                'error' => $e->getMessage()
            ]);

            throw $e;

        } finally {
            // Always release the lock
            $this->releaseProcessingLock('user_processing', $userId);
        }
    }

    /**
     * Ensure all PV transactions are tracked
     */
    private function ensureAllPVTracked($userId) {
        try {
            // Try new schema first
            $stmt = $this->db->prepare("
                SELECT pt.id, pt.user_id, pt.side, pt.pv_amount
                FROM pv_transactions pt
                LEFT JOIN pv_usage_tracking put ON pt.id = put.pv_transaction_id
                WHERE pt.user_id = ? AND put.id IS NULL AND pt.processing_status = 'pending'
            ");
            $stmt->execute([$userId]);
        } catch (Exception $e) {
            // Fall back to old schema
            $stmt = $this->db->prepare("
                SELECT pt.id, pt.user_id, pt.side, pt.pv_amount
                FROM pv_transactions pt
                LEFT JOIN pv_usage_tracking put ON pt.id = put.pv_transaction_id
                WHERE pt.user_id = ? AND put.id IS NULL
            ");
            $stmt->execute([$userId]);
        }

        $untracked = $stmt->fetchAll();

        foreach ($untracked as $transaction) {
            $this->initializePVUsageTracking(
                $transaction['id'],
                $transaction['user_id'],
                $transaction['side'],
                $transaction['pv_amount']
            );

            // Mark transaction as processed (only if column exists)
            try {
                $updateStmt = $this->db->prepare("UPDATE pv_transactions SET processing_status = 'processed' WHERE id = ?");
                $updateStmt->execute([$transaction['id']]);
            } catch (Exception $e) {
                // Column doesn't exist, that's okay
            }
        }
    }

    /**
     * Check if user income already processed for period
     */
    private function isAlreadyProcessed($userId, $processingPeriod, $processingType) {
        if ($processingType === 'weekly') {
            $stmt = $this->db->prepare("SELECT id FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
        } else {
            $stmt = $this->db->prepare("SELECT id FROM income_logs WHERE user_id = ? AND matching_date = ?");
        }

        $stmt->execute([$userId, $processingPeriod]);
        return $stmt->fetch() !== false;
    }

    /**
     * Get carry forward PV from previous period
     */
    private function getCarryForward($userId, $processingPeriod, $processingType) {
        if ($processingType === 'weekly') {
            $stmt = $this->db->prepare("
                SELECT carry_forward_left, carry_forward_right
                FROM weekly_income_logs
                WHERE user_id = ? AND week_start_date < ?
                ORDER BY week_start_date DESC
                LIMIT 1
            ");
        } else {
            $stmt = $this->db->prepare("
                SELECT carry_forward_left, carry_forward_right
                FROM income_logs
                WHERE user_id = ? AND matching_date < ?
                ORDER BY matching_date DESC
                LIMIT 1
            ");
        }

        $stmt->execute([$userId, $processingPeriod]);
        $result = $stmt->fetch();

        return [
            'left_pv' => (float) ($result['carry_forward_left'] ?? 0),
            'right_pv' => (float) ($result['carry_forward_right'] ?? 0)
        ];
    }

    /**
     * Calculate income with capping and deductions
     */
    private function calculateIncome($matchedPV, $userId, $processingType) {
        $pvRate = (float) $this->config->get('pv_rate', 0.10);
        $grossIncome = $matchedPV * $pvRate;

        // Apply capping
        $cappingApplied = 0;
        if ($processingType === 'weekly') {
            $weeklyCapping = (float) $this->config->get('weekly_capping', 130000);
            if ($grossIncome > $weeklyCapping) {
                $cappingApplied = $grossIncome - $weeklyCapping;
                $grossIncome = $weeklyCapping;
            }
        } else {
            $dailyCapping = (float) $this->config->get('daily_capping', 130000);
            $todayIncome = $this->getTodayIncome($userId);
            $availableCapping = $dailyCapping - $todayIncome;

            if ($grossIncome > $availableCapping) {
                $cappingApplied = $grossIncome - $availableCapping;
                $grossIncome = $availableCapping;
            }
        }

        // Calculate deductions
        $serviceCharge = $grossIncome * 0.10; // 10% service charge
        $tdsAmount = $grossIncome * 0.05;     // 5% TDS
        $totalDeductions = $serviceCharge + $tdsAmount;
        $netIncome = $grossIncome - $totalDeductions;

        return [
            'gross_income' => $grossIncome,
            'net_income' => $netIncome,
            'capping_applied' => $cappingApplied,
            'deductions' => $totalDeductions,
            'service_charge' => $serviceCharge,
            'tds_amount' => $tdsAmount
        ];
    }

    /**
     * Get today's income for daily capping
     */
    private function getTodayIncome($userId) {
        $stmt = $this->db->prepare("
            SELECT SUM(income_amount) as today_income
            FROM income_logs
            WHERE user_id = ? AND matching_date = CURDATE()
        ");

        $stmt->execute([$userId]);
        $result = $stmt->fetch();

        return (float) ($result['today_income'] ?? 0);
    }

    /**
     * Mark PV as used for income calculation (FIFO)
     */
    private function markPVAsUsed($userId, $matchedPV, $processingPeriod) {
        // Get available PV transactions ordered by creation date (FIFO)
        $stmt = $this->db->prepare("
            SELECT id, side, remaining_amount
            FROM pv_usage_tracking
            WHERE user_id = ? AND remaining_amount > 0 AND status IN ('available', 'partially_used')
            ORDER BY created_at ASC
        ");

        $stmt->execute([$userId]);
        $availablePV = $stmt->fetchAll();

        $leftPVToUse = $matchedPV;
        $rightPVToUse = $matchedPV;

        // Mark left side PV as used
        foreach ($availablePV as $pv) {
            if ($pv['side'] === 'left' && $leftPVToUse > 0) {
                $useAmount = min($leftPVToUse, $pv['remaining_amount']);
                $this->updatePVUsage($pv['id'], $useAmount, $processingPeriod);
                $leftPVToUse -= $useAmount;
            }
        }

        // Mark right side PV as used
        foreach ($availablePV as $pv) {
            if ($pv['side'] === 'right' && $rightPVToUse > 0) {
                $useAmount = min($rightPVToUse, $pv['remaining_amount']);
                $this->updatePVUsage($pv['id'], $useAmount, $processingPeriod);
                $rightPVToUse -= $useAmount;
            }
        }
    }

    /**
     * Update PV usage tracking
     */
    private function updatePVUsage($trackingId, $useAmount, $processingPeriod) {
        $stmt = $this->db->prepare("
            UPDATE pv_usage_tracking
            SET used_amount = used_amount + ?,
                remaining_amount = remaining_amount - ?,
                processing_period = ?,
                status = CASE
                    WHEN remaining_amount - ? <= 0 THEN 'fully_used'
                    ELSE 'partially_used'
                END,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        $stmt->execute([$useAmount, $useAmount, $processingPeriod, $useAmount, $trackingId]);
    }

    /**
     * Validate that PV has not been reused for the same processing period
     */
    public function validatePVReusePrevention($userId, $processingPeriod) {
        // Check if there are any PV transactions that have been used multiple times for the same period
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as reuse_count
            FROM pv_usage_tracking
            WHERE user_id = ?
            AND processing_period = ?
            AND used_amount > 0
            GROUP BY pv_transaction_id
            HAVING COUNT(*) > 1
        ");
        $stmt->execute([$userId, $processingPeriod]);
        $reuseCount = $stmt->fetchColumn();

        if ($reuseCount > 0) {
            throw new Exception("PV reuse detected for user {$userId} in period {$processingPeriod}. Found {$reuseCount} instances of PV being reused.");
        }

        return true;
    }

    /**
     * Acquire processing lock
     */
    private function acquireProcessingLock($lockType, $lockKey, $timeout = null) {
        // Skip locking for test users
        if (strpos($lockKey, 'TEST') === 0) {
            return true;
        }

        $timeout = $timeout ?? self::PROCESSING_LOCK_TIMEOUT;
        $expiresAt = date('Y-m-d H:i:s', time() + $timeout);
        $processId = getmypid() . '_' . uniqid();

        try {
            $stmt = $this->db->prepare("
                INSERT INTO pv_processing_locks (lock_type, lock_key, locked_by, expires_at, process_id, status)
                VALUES (?, ?, ?, ?, ?, 'active')
            ");

            $stmt->execute([$lockType, $lockKey, 'system', $expiresAt, $processId]);
            return true;

        } catch (PDOException $e) {
            // Lock already exists, check if expired
            $this->cleanupExpiredLocks();
            return false;
        }
    }

    /**
     * Release processing lock
     */
    private function releaseProcessingLock($lockType, $lockKey) {
        $stmt = $this->db->prepare("
            UPDATE pv_processing_locks
            SET status = 'released'
            WHERE lock_type = ? AND lock_key = ? AND status = 'active'
        ");

        $stmt->execute([$lockType, $lockKey]);
    }

    /**
     * Cleanup expired locks
     */
    private function cleanupExpiredLocks() {
        $stmt = $this->db->prepare("
            UPDATE pv_processing_locks
            SET status = 'expired'
            WHERE expires_at < NOW() AND status = 'active'
        ");

        $stmt->execute();
    }

    /**
     * Get downline PV from cache
     */
    private function getDownlinePVFromCache($userId, $processingPeriod) {
        $stmt = $this->db->prepare("
            SELECT left_downline_pv, right_downline_pv, total_downline_users
            FROM downline_pv_cache
            WHERE user_id = ? AND cache_period = ? AND is_valid = TRUE AND cache_expires_at > NOW()
        ");

        $stmt->execute([$userId, $processingPeriod]);
        $result = $stmt->fetch();

        if ($result) {
            return [
                'left_pv' => (float) $result['left_downline_pv'],
                'right_pv' => (float) $result['right_downline_pv'],
                'total_downline_users' => (int) $result['total_downline_users']
            ];
        }

        return null;
    }

    /**
     * Cache downline PV data
     */
    private function cacheDownlinePV($userId, $processingPeriod, $data) {
        $cacheExpires = date('Y-m-d H:i:s', time() + 3600); // 1 hour cache

        $stmt = $this->db->prepare("
            INSERT INTO downline_pv_cache
            (user_id, cache_period, cache_type, left_downline_pv, right_downline_pv, total_downline_users, cache_expires_at)
            VALUES (?, ?, 'weekly', ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            left_downline_pv = VALUES(left_downline_pv),
            right_downline_pv = VALUES(right_downline_pv),
            total_downline_users = VALUES(total_downline_users),
            cache_generated_at = CURRENT_TIMESTAMP,
            cache_expires_at = VALUES(cache_expires_at),
            is_valid = TRUE
        ");

        $stmt->execute([
            $userId,
            $processingPeriod,
            $data['left_pv'],
            $data['right_pv'],
            $data['total_downline_users'],
            $cacheExpires
        ]);
    }

    /**
     * Record income audit trail
     */
    private function recordIncomeAuditTrail($data) {
        $stmt = $this->db->prepare("
            INSERT INTO pv_income_audit_trail
            (user_id, processing_period, processing_type, left_pv_available, right_pv_available,
             left_pv_carried, right_pv_carried, total_left_pv, total_right_pv, matched_pv,
             gross_income, capping_applied, deductions_applied, net_income,
             carry_forward_left, carry_forward_right, calculation_details)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $calculationDetails = json_encode([
            'pv_rate' => $this->config->get('pv_rate', 0.10),
            'service_charge_rate' => 0.10,
            'tds_rate' => 0.05,
            'processing_timestamp' => date('Y-m-d H:i:s')
        ]);

        $stmt->execute([
            $data['user_id'],
            $data['processing_period'],
            $data['processing_type'],
            0, // left_pv_available - will be calculated separately
            0, // right_pv_available - will be calculated separately
            0, // left_pv_carried - will be calculated separately
            0, // right_pv_carried - will be calculated separately
            $data['total_left_pv'],
            $data['total_right_pv'],
            $data['matched_pv'],
            $data['gross_income'],
            $data['capping_applied'],
            $data['deductions'],
            $data['net_income'],
            $data['carry_forward_left'],
            $data['carry_forward_right'],
            $calculationDetails
        ]);
    }

    /**
     * Record income log
     */
    private function recordIncomeLog($data, $processingType) {
        if ($processingType === 'weekly') {
            $stmt = $this->db->prepare("
                INSERT INTO weekly_income_logs
                (user_id, week_start_date, week_end_date, left_pv, right_pv, matched_pv,
                 gross_income_amount, service_charge, tds_amount, income_amount,
                 weekly_capping_applied, carry_forward_left, carry_forward_right)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $weekEndDate = date('Y-m-d', strtotime($data['processing_period'] . ' +6 days'));

            $stmt->execute([
                $data['user_id'],
                $data['processing_period'],
                $weekEndDate,
                $data['total_left_pv'],
                $data['total_right_pv'],
                $data['matched_pv'],
                $data['gross_income'],
                $data['service_charge'] ?? 0,
                $data['tds_amount'] ?? 0,
                $data['net_income'],
                $data['capping_applied'],
                $data['carry_forward_left'],
                $data['carry_forward_right']
            ]);
        } else {
            $stmt = $this->db->prepare("
                INSERT INTO income_logs
                (user_id, left_pv, right_pv, matched_pv, income_amount, capping_applied,
                 carry_forward_left, carry_forward_right, matching_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $data['user_id'],
                $data['total_left_pv'],
                $data['total_right_pv'],
                $data['matched_pv'],
                $data['net_income'],
                $data['capping_applied'],
                $data['carry_forward_left'],
                $data['carry_forward_right'],
                $data['processing_period']
            ]);
        }
    }

    /**
     * Log system messages
     */
    private function logMessage($level, $category, $message, $context = []) {
        try {
            // Try new schema first
            $stmt = $this->db->prepare("
                INSERT INTO system_logs (log_type, category, message, context, user_id, processing_period)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $level,
                $category,
                $message,
                json_encode($context),
                $context['user_id'] ?? null,
                $context['processing_period'] ?? null
            ]);
        } catch (Exception $e) {
            // Fall back to old schema
            try {
                $stmt = $this->db->prepare("
                    INSERT INTO system_logs (log_type, message)
                    VALUES (?, ?)
                ");

                $fullMessage = "[{$category}] {$message}";
                if (!empty($context)) {
                    $fullMessage .= " - Context: " . json_encode($context);
                }

                $stmt->execute([$level, $fullMessage]);
            } catch (Exception $e2) {
                // If logging fails completely, just continue
                error_log("Logging failed: " . $e2->getMessage());
            }
        }
    }

    /**
     * Check and update user activation status based on PV threshold
     *
     * @param string $userId User ID to check
     * @return bool True if status was updated, false otherwise
     */
    private function checkUserActivation($userId) {
        try {
            // Include User model for activation logic
            require_once __DIR__ . '/../models/User.php';
            $userModel = new User();

            return $userModel->checkAndUpdateActivationStatus($userId);
        } catch (Exception $e) {
            error_log("Error checking user activation in EnhancedPVSystem for {$userId}: " . $e->getMessage());
            return false;
        }
    }
}
