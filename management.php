<?php
/**
 * Management Page
 * ShaktiPure Industries Pvt Ltd
 */

require_once 'includes/header.php';
renderHeader('Management - ShaktiPure Industries Pvt Ltd');
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="page-header-content">
            <h1>Management</h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Management</span>
            </nav>
        </div>
    </div>
</section>

<!-- Management Content -->
<section class="management-section">
    <div class="container">
        <div class="management-intro">
            <h2>Our Leadership Team</h2>
            <p>Meet the visionary leaders who drive ShaktiPure Industries towards excellence in water purification technology and sustainable business practices.</p>
        </div>

        <div class="management-grid">
            <!-- CEO -->
            <div class="management-card">
                <div class="management-image">
                    <img src="assets/images/ceo.png" alt="CEO">
                </div>
                <div class="management-info">
                    <h3>Mr. <PERSON><PERSON></h3>
                    <p class="position">CEO & Founder</p>
                    <p class="description">With over 25 years of experience in the water treatment industry, Mr. <PERSON> leads our company with a vision of providing pure water solutions to every household in India.</p>
                    <div class="management-contact">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    </div>
                </div>
            </div>

            <!-- CTO -->
            <div class="management-card">
                <div class="management-image">
                    <img src="assets/images/cto.jpg" alt="CTO">
                </div>
                <div class="management-info">
                    <h3>Mr. Abhishek Sharma</h3>
                    <p class="position">Chief Technology Officer</p>
                    <p class="description">Mr. Sharma brings cutting-edge innovation to our products with her expertise in water purification technology and environmental engineering.</p>
                    <div class="management-contact">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    </div>
                </div>
            </div>

        
        </div>

        <!-- Company Values -->
        <div class="company-values">
            <h2>Our Core Values</h2>
            <div class="values-grid">
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Customer First</h3>
                    <p>We prioritize customer satisfaction and strive to exceed expectations in every interaction.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3>Sustainability</h3>
                    <p>We are committed to environmental responsibility and sustainable business practices.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3>Innovation</h3>
                    <p>We continuously innovate to provide cutting-edge water purification solutions.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Quality</h3>
                    <p>We maintain the highest quality standards in all our products and services.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php renderFooter(); ?>

<style>
/* Page Header */
.page-header {
    background: var(--gradient-primary);
    padding: 60px 0;
    color: white;
}

.page-header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

/* Management Section */
.management-section {
    padding: 80px 0;
}

.management-intro {
    text-align: center;
    margin-bottom: 60px;
}

.management-intro h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-bottom: 80px;
}

.management-card {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.management-card:hover {
    transform: translateY(-5px);
}

.management-image {
    margin-bottom: 20px;
}

.management-image img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
}

.management-info h3 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.position {
    color: var(--primary-green);
    font-weight: 600;
    margin-bottom: 15px;
}

.description {
    color: var(--text-gray-light);
    line-height: 1.6;
    margin-bottom: 20px;
}

.management-contact {
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

/* Company Values */
.company-values {
    text-align: center;
}

.company-values h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 40px;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.value-card {
    background: white;
    padding: 40px 20px;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.value-icon {
    font-size: 3rem;
    color: var(--primary-green);
    margin-bottom: 20px;
}

.value-card h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.value-card p {
    color: var(--text-gray-light);
    line-height: 1.6;
}
</style>
