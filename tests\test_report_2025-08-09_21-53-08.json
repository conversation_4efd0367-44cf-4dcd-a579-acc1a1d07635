{"timestamp": "2025-08-09 21:53:08", "summary": {"total": 16, "passed": 16, "failed": 0, "errors": 0, "success_rate": 100, "total_duration_ms": 763.52}, "tests": [{"name": "Add PV Transaction", "result": "PASS", "duration": 13.08}, {"name": "Get Available PV", "result": "PASS", "duration": 1.42}, {"name": "PV Usage Tracking", "result": "PASS", "duration": 0.55}, {"name": "Input Validation", "result": "PASS", "duration": 2.9}, {"name": "Prevent Duplicate Income Processing", "result": "PASS", "duration": 15.11}, {"name": "PV Usage Tracking Consistency", "result": "PASS", "duration": 9.67}, {"name": "FIFO PV Usage", "result": "PASS", "duration": 33.07}, {"name": "System Data Integrity", "result": "PASS", "duration": 5.54}, {"name": "Transaction Consistency", "result": "PASS", "duration": 0.6}, {"name": "No Negative Values", "result": "PASS", "duration": 0.46}, {"name": "Bulk PV Addition Performance", "result": "PASS", "duration": 608.63}, {"name": "Income Processing Performance", "result": "PASS", "duration": 12.98}, {"name": "Zero PV Amount Handling", "result": "PASS", "duration": 1.88}, {"name": "Large PV Amount Handling", "result": "PASS", "duration": 5.51}, {"name": "Invalid User ID Handling", "result": "PASS", "duration": 2.37}, {"name": "Complete Income Generation Workflow", "result": "PASS", "duration": 49.75}]}