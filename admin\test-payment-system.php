<?php
/**
 * Test Payment System
 * Quick test to verify payment system functionality
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PaymentProcessor.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Get database instance
$db = Database::getInstance();
$config = Config::getInstance();
$paymentProcessor = new PaymentProcessor();

echo "<h2>Payment System Test</h2>";
echo "<p>Testing payment system components...</p>";

// Test 1: Database Tables
echo "<h3>1. Database Tables Test</h3>";

$tables = ['payment_batches', 'payment_transactions', 'weekly_income_logs', 'config'];
foreach ($tables as $table) {
    try {
        $result = $db->query("SELECT COUNT(*) FROM {$table}");
        $count = $result->fetchColumn();
        echo "✓ Table {$table}: {$count} records<br>";
    } catch (Exception $e) {
        echo "❌ Table {$table}: Error - " . $e->getMessage() . "<br>";
    }
}

// Test 2: Configuration
echo "<h3>2. Configuration Test</h3>";

$configKeys = [
    'auto_payment_enabled',
    'auto_payment_threshold', 
    'payment_processing_day',
    'payment_processing_time',
    'payment_batch_size'
];

foreach ($configKeys as $key) {
    $value = $config->get($key, 'NOT_SET');
    echo "- {$key}: {$value}<br>";
}

// Test 3: PaymentProcessor Class
echo "<h3>3. PaymentProcessor Class Test</h3>";

try {
    // Test getting payment stats for a recent week
    $recentWeek = date('Y-m-d', strtotime('monday last week'));
    $stats = $paymentProcessor->getWeeklyPaymentStats($recentWeek);
    
    echo "Payment stats for week {$recentWeek}:<br>";
    echo "- Total logs: {$stats['total_logs']}<br>";
    echo "- Paid: {$stats['paid_count']} (₹" . number_format($stats['paid_amount'] ?? 0, 2) . ")<br>";
    echo "- Pending: {$stats['pending_count']} (₹" . number_format($stats['pending_amount'] ?? 0, 2) . ")<br>";
    echo "- Failed: {$stats['failed_count']}<br>";
    echo "✓ PaymentProcessor working correctly<br>";
    
} catch (Exception $e) {
    echo "❌ PaymentProcessor error: " . $e->getMessage() . "<br>";
}

// Test 4: Sample Payment Processing (Dry Run)
echo "<h3>4. Sample Payment Processing Test</h3>";

try {
    // Get a sample week with income logs
    $sampleWeekStmt = $db->query("
        SELECT week_start_date, COUNT(*) as log_count, SUM(income_amount) as total_amount
        FROM weekly_income_logs 
        WHERE income_amount > 0 
        GROUP BY week_start_date 
        ORDER BY week_start_date DESC 
        LIMIT 1
    ");
    $sampleWeek = $sampleWeekStmt->fetch();
    
    if ($sampleWeek) {
        echo "Sample week: {$sampleWeek['week_start_date']}<br>";
        echo "Income logs: {$sampleWeek['log_count']}<br>";
        echo "Total amount: ₹" . number_format($sampleWeek['total_amount'], 2) . "<br>";
        
        // Check payment status breakdown
        $statusStmt = $db->prepare("
            SELECT payment_status, COUNT(*) as count, SUM(income_amount) as amount
            FROM weekly_income_logs 
            WHERE week_start_date = ? AND income_amount > 0
            GROUP BY payment_status
        ");
        $statusStmt->execute([$sampleWeek['week_start_date']]);
        $statusBreakdown = $statusStmt->fetchAll();
        
        echo "Payment status breakdown:<br>";
        foreach ($statusBreakdown as $status) {
            $statusName = $status['payment_status'] ?: 'pending';
            echo "- {$statusName}: {$status['count']} logs, ₹" . number_format($status['amount'], 2) . "<br>";
        }
        
        echo "✓ Sample data available for testing<br>";
    } else {
        echo "ℹ No income logs found for testing<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Sample data test error: " . $e->getMessage() . "<br>";
}

// Test 5: Admin Interface Links
echo "<h3>5. Admin Interface Test</h3>";

$adminPages = [
    'payment-management.php' => 'Payment Management',
    'weekly-income-reports.php' => 'Weekly Income Reports',
    'test-enhanced-weekly-system.php' => 'Enhanced System Test'
];

foreach ($adminPages as $page => $title) {
    if (file_exists($page)) {
        echo "✓ <a href='{$page}' target='_blank'>{$title}</a><br>";
    } else {
        echo "❌ {$title} (file missing)<br>";
    }
}

echo "<h3>✅ Payment System Test Completed</h3>";
echo "<p>Review the results above to ensure all components are working correctly.</p>";

echo "<div style='margin-top: 30px;'>";
echo "<a href='payment-management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Payment Management</a>";
echo "<a href='weekly-income-reports.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Weekly Reports</a>";
echo "<a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Dashboard</a>";
echo "</div>";
?>
