<?php
/**
 * Migration: Enhanced Weekly Income System with Automatic Payments
 * This migration updates the weekly income system to support:
 * 1. Multiple reports per week
 * 2. User-level duplicate prevention
 * 3. Enhanced reporting features
 * 4. Automatic payment system integration
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();

    echo "=== Enhanced Weekly Income System Migration ===\n\n";

    // 1. Update weekly_income_reports table structure for multiple reports per week
    echo "1. Updating weekly_income_reports table structure...\n";

    // Check if the table needs updating
    $checkReportsTable = $db->query("DESCRIBE weekly_income_reports");
    $existingColumns = [];
    while ($row = $checkReportsTable->fetch()) {
        $existingColumns[] = $row['Field'];
    }

    if (!in_array('report_id', $existingColumns)) {
        // Drop the unique constraint on week_start_date first
        try {
            $db->exec("ALTER TABLE weekly_income_reports DROP INDEX unique_week");
            echo "✓ Removed unique week constraint\n";
        } catch (Exception $e) {
            echo "ℹ Unique week constraint not found or already removed\n";
        }

        // Add new columns for enhanced reporting
        $db->exec("
            ALTER TABLE weekly_income_reports
            ADD COLUMN report_id VARCHAR(30) UNIQUE NOT NULL DEFAULT '' AFTER id,
            ADD COLUMN report_number INT NOT NULL DEFAULT 1 AFTER week_end_date,
            ADD COLUMN total_users_processed INT NOT NULL DEFAULT 0 AFTER report_number,
            ADD COLUMN total_users_skipped INT NOT NULL DEFAULT 0 AFTER total_users_earned,
            ADD COLUMN total_gross_income DECIMAL(12,2) NOT NULL DEFAULT 0.00 AFTER total_users_skipped,
            ADD COLUMN total_service_charge DECIMAL(12,2) NOT NULL DEFAULT 0.00 AFTER total_gross_income,
            ADD COLUMN total_tds_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00 AFTER total_service_charge,
            ADD COLUMN processing_time_seconds INT DEFAULT 0 AFTER total_capping_applied,
            ADD COLUMN report_file_path VARCHAR(255) NULL AFTER report_status,
            ADD COLUMN generated_by INT NULL AFTER report_file_path,
            ADD INDEX idx_report_id (report_id),
            ADD INDEX idx_week_report_number (week_start_date, report_number)
        ");

        // Update report_status enum to include 'archived'
        $db->exec("
            ALTER TABLE weekly_income_reports
            MODIFY COLUMN report_status ENUM('processing', 'generated', 'sent', 'failed', 'archived') DEFAULT 'processing'
        ");

        echo "✓ Updated weekly_income_reports table structure\n";

        // Generate report_id for existing reports
        $existingReports = $db->query("SELECT id, week_start_date FROM weekly_income_reports WHERE report_id = ''");
        $reportCount = 0;
        while ($report = $existingReports->fetch()) {
            $reportNumber = 1; // Existing reports are considered #1
            $reportId = 'WR' . date('Ymd', strtotime($report['week_start_date'])) . '_' . str_pad($reportNumber, 2, '0', STR_PAD_LEFT);

            $updateStmt = $db->prepare("UPDATE weekly_income_reports SET report_id = ?, report_number = ? WHERE id = ?");
            $updateStmt->execute([$reportId, $reportNumber, $report['id']]);
            $reportCount++;
        }
        echo "✓ Generated report IDs for {$reportCount} existing reports\n";
    } else {
        echo "ℹ weekly_income_reports table already has enhanced structure\n";
    }

    // 2. Update weekly_income_logs table structure
    echo "\n2. Updating weekly_income_logs table structure...\n";

    $checkLogsTable = $db->query("DESCRIBE weekly_income_logs");
    $existingLogColumns = [];
    while ($row = $checkLogsTable->fetch()) {
        $existingLogColumns[] = $row['Field'];
    }

    if (!in_array('processing_status', $existingLogColumns)) {
        $db->exec("
            ALTER TABLE weekly_income_logs
            ADD COLUMN processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' AFTER carry_forward_right,
            ADD COLUMN processed_at TIMESTAMP NULL AFTER processing_status,
            ADD COLUMN processing_time_seconds INT DEFAULT 0 AFTER processed_at,
            ADD INDEX idx_processing_status (processing_status)
        ");
        echo "✓ Added processing status fields to weekly_income_logs\n";
    } else {
        echo "ℹ weekly_income_logs table already has processing status fields\n";
    }

    // 3. Add payment status to weekly_income_logs
    echo "\n3. Adding payment status to weekly_income_logs table...\n";

    if (!in_array('payment_status', $existingLogColumns)) {
        $db->exec("
            ALTER TABLE weekly_income_logs
            ADD COLUMN payment_status ENUM('pending', 'paid', 'failed', 'processing') DEFAULT 'pending' AFTER income_amount,
            ADD COLUMN payment_date TIMESTAMP NULL AFTER payment_status,
            ADD COLUMN payment_reference VARCHAR(100) NULL AFTER payment_date,
            ADD COLUMN payment_method ENUM('bank_transfer', 'upi', 'wallet', 'manual') DEFAULT 'bank_transfer' AFTER payment_reference,
            ADD COLUMN payment_notes TEXT NULL AFTER payment_method,
            ADD COLUMN processed_by INT NULL AFTER payment_notes,
            ADD INDEX idx_payment_status (payment_status),
            ADD INDEX idx_payment_date (payment_date),
            ADD INDEX idx_week_payment (week_start_date, payment_status)
        ");
        echo "✓ Added payment status fields to weekly_income_logs\n";
    } else {
        echo "ℹ weekly_income_logs table already has payment status fields\n";
    }

    // 4. Create payment_batches table for batch processing
    echo "\n4. Creating payment_batches table...\n";
    
    $db->exec("
        CREATE TABLE IF NOT EXISTS payment_batches (
            id INT PRIMARY KEY AUTO_INCREMENT,
            batch_id VARCHAR(50) UNIQUE NOT NULL,
            week_start_date DATE NOT NULL,
            week_end_date DATE NOT NULL,
            total_payments INT NOT NULL DEFAULT 0,
            total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            successful_payments INT NOT NULL DEFAULT 0,
            failed_payments INT NOT NULL DEFAULT 0,
            batch_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            created_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            completed_at TIMESTAMP NULL,
            notes TEXT NULL,
            INDEX idx_batch_status (batch_status),
            INDEX idx_week_dates (week_start_date, week_end_date),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB
    ");
    echo "✓ Created payment_batches table\n";
    
    // 5. Create payment_transactions table for detailed tracking
    echo "\n5. Creating payment_transactions table...\n";
    
    $db->exec("
        CREATE TABLE IF NOT EXISTS payment_transactions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            transaction_id VARCHAR(100) UNIQUE NOT NULL,
            batch_id VARCHAR(50) NULL,
            income_log_id INT NOT NULL,
            user_id VARCHAR(20) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method ENUM('bank_transfer', 'upi', 'wallet', 'manual') DEFAULT 'bank_transfer',
            payment_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
            bank_details JSON NULL,
            payment_reference VARCHAR(100) NULL,
            gateway_response JSON NULL,
            failure_reason TEXT NULL,
            retry_count INT DEFAULT 0,
            max_retries INT DEFAULT 3,
            processed_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP NULL,
            completed_at TIMESTAMP NULL,
            FOREIGN KEY (income_log_id) REFERENCES weekly_income_logs(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_transaction_status (payment_status),
            INDEX idx_user_payment (user_id, payment_status),
            INDEX idx_batch_id (batch_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB
    ");
    echo "✓ Created payment_transactions table\n";

    // 6. Create binary_reports table if missing
    echo "\n6. Creating binary_reports table if missing...\n";

    $db->exec("
        CREATE TABLE IF NOT EXISTS binary_reports (
            id INT PRIMARY KEY AUTO_INCREMENT,
            report_id VARCHAR(50) UNIQUE NOT NULL,
            report_date DATE NOT NULL,
            users_processed INT NOT NULL DEFAULT 0,
            total_income_generated DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            total_pv_matched DECIMAL(12,2) NOT NULL DEFAULT 0.00,
            report_status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
            generated_by INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP NULL,
            INDEX idx_report_date (report_date),
            INDEX idx_report_status (report_status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB
    ");
    echo "✓ Created binary_reports table\n";

    // 7. Fix users table structure if needed
    echo "\n7. Checking users table structure...\n";

    // Check if status column exists in users table
    $usersColumns = $db->query("DESCRIBE users")->fetchAll();
    $hasStatusColumn = false;
    foreach ($usersColumns as $col) {
        if ($col['Field'] === 'status') {
            $hasStatusColumn = true;
            break;
        }
    }

    if (!$hasStatusColumn) {
        $db->exec("
            ALTER TABLE users
            ADD COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' AFTER current_award
        ");
        echo "✓ Added status column to users table\n";

        // Set all existing users to active
        $db->exec("UPDATE users SET status = 'active' WHERE status IS NULL");
        echo "✓ Set all existing users to active status\n";
    } else {
        echo "ℹ Users table already has status column\n";
    }

    // 8. Update existing income logs to have 'pending' status
    echo "\n6. Updating existing income logs with pending payment status...\n";
    
    $result = $db->exec("
        UPDATE weekly_income_logs 
        SET payment_status = 'pending' 
        WHERE income_amount > 0 AND payment_status IS NULL
    ");
    echo "✓ Updated {$result} existing income logs with pending status\n";
    
    // 9. Add configuration for automatic payments
    echo "\n9. Adding automatic payment configuration...\n";
    
    $configs = [
        ['auto_payment_enabled', 'true', 'boolean', 'payment', 'Enable automatic payment processing', false],
        ['auto_payment_threshold', '500.00', 'number', 'payment', 'Minimum amount for automatic payment', false],
        ['payment_processing_day', '6', 'number', 'payment', 'Day of week for payment processing (6=Saturday)', false],
        ['payment_processing_time', '10:00', 'string', 'payment', 'Time for payment processing (HH:MM)', false],
        ['default_payment_method', 'bank_transfer', 'string', 'payment', 'Default payment method', false],
        ['payment_batch_size', '100', 'number', 'payment', 'Number of payments to process in one batch', false],
        ['payment_retry_attempts', '3', 'number', 'payment', 'Maximum retry attempts for failed payments', false]
    ];
    
    $configStmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES (?, ?, ?, ?, ?, ?) 
        ON DUPLICATE KEY UPDATE config_value = VALUES(config_value)
    ");
    
    foreach ($configs as $config) {
        $configStmt->execute($config);
    }
    echo "✓ Added automatic payment configuration\n";
    
    echo "\n✅ Enhanced Weekly Income System Migration Completed Successfully!\n";
    echo "\nNew Features Added:\n";
    echo "- Multiple reports per week support\n";
    echo "- User-level duplicate prevention with UNIQUE constraints\n";
    echo "- Enhanced reporting with detailed breakdowns\n";
    echo "- Report numbering and unique report IDs\n";
    echo "- Payment status tracking in weekly_income_logs\n";
    echo "- Payment batches for organized processing\n";
    echo "- Detailed payment transaction tracking\n";
    echo "- Automatic payment configuration\n";
    echo "\nDatabase Structure Updates:\n";
    echo "- weekly_income_reports: Added report_id, report_number, detailed income fields\n";
    echo "- weekly_income_logs: Added processing_status and payment_status fields\n";
    echo "- Removed unique week constraint to allow multiple reports per week\n";
    echo "- Added proper indexing for performance\n";
    echo "\nNext Steps:\n";
    echo "- Test the enhanced weekly income processing\n";
    echo "- Configure payment settings in admin panel\n";
    echo "- Set up payment gateway integration\n";
    echo "- Update admin interfaces to show new features\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
