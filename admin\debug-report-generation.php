<?php
/**
 * Debug Report Generation
 * This script helps debug why report generation is not working
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>🔍 Debug Report Generation</h2>\n";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .debug-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
    .debug-header { background-color: #f0f0f0; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 5px 5px 0 0; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

try {
    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>1. Loading Required Files</h3></div>";
    
    // Load files one by one to catch any errors
    echo "<p>Loading includes/header.php...</p>";
    require_once '../includes/header.php';
    echo "<span class='success'>✓ header.php loaded</span><br>";
    
    echo "<p>Loading includes/Auth.php...</p>";
    require_once '../includes/Auth.php';
    echo "<span class='success'>✓ Auth.php loaded</span><br>";
    
    echo "<p>Loading includes/PVSystem.php...</p>";
    require_once '../includes/PVSystem.php';
    echo "<span class='success'>✓ PVSystem.php loaded</span><br>";
    
    echo "<p>Loading includes/ReportLogger.php...</p>";
    require_once '../includes/ReportLogger.php';
    echo "<span class='success'>✓ ReportLogger.php loaded</span><br>";
    
    echo "<p>Loading config/config.php...</p>";
    require_once '../config/config.php';
    echo "<span class='success'>✓ config.php loaded</span><br>";
    
    echo "<p>Loading includes/WeeklyDateHelper.php...</p>";
    require_once '../includes/WeeklyDateHelper.php';
    echo "<span class='success'>✓ WeeklyDateHelper.php loaded</span><br>";
    
    echo "</div>";

    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>2. Authentication Check</h3></div>";
    
    // Check authentication
    if (class_exists('Auth')) {
        echo "<span class='success'>✓ Auth class exists</span><br>";
        
        // Check if user is logged in
        if (method_exists('Auth', 'check') && Auth::check()) {
            echo "<span class='success'>✓ User is authenticated</span><br>";
            $currentUser = Auth::user();
            echo "<p>Current user: {$currentUser['full_name']} (ID: {$currentUser['user_id']})</p>";
            
            // Check if user is admin
            if (method_exists('Auth', 'isAdmin') && Auth::isAdmin()) {
                echo "<span class='success'>✓ User has admin privileges</span><br>";
            } else {
                echo "<span class='error'>✗ User does not have admin privileges</span><br>";
            }
        } else {
            echo "<span class='error'>✗ User is not authenticated</span><br>";
        }
    } else {
        echo "<span class='error'>✗ Auth class not found</span><br>";
    }
    echo "</div>";

    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>3. Class Initialization</h3></div>";
    
    // Initialize classes
    echo "<p>Initializing PVSystem...</p>";
    $pvSystem = new PVSystem();
    echo "<span class='success'>✓ PVSystem initialized</span><br>";
    
    echo "<p>Initializing Config...</p>";
    $config = Config::getInstance();
    echo "<span class='success'>✓ Config initialized</span><br>";
    
    echo "<p>Getting Database instance...</p>";
    $db = Database::getInstance();
    echo "<span class='success'>✓ Database instance obtained</span><br>";
    
    echo "<p>Initializing ReportLogger...</p>";
    $logger = new ReportLogger('debug_test');
    echo "<span class='success'>✓ ReportLogger initialized</span><br>";
    
    echo "</div>";

    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>4. Database Connection Test</h3></div>";
    
    // Test database connection
    try {
        $testQuery = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
        $userCount = $testQuery->fetch()['count'];
        echo "<span class='success'>✓ Database connection working</span><br>";
        echo "<p>Active users found: {$userCount}</p>";
    } catch (Exception $e) {
        echo "<span class='error'>✗ Database connection failed: " . $e->getMessage() . "</span><br>";
    }
    echo "</div>";

    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>5. Weekly Date Helper Test</h3></div>";
    
    // Test WeeklyDateHelper
    try {
        $currentWeek = WeeklyDateHelper::getCurrentWeek();
        echo "<span class='success'>✓ WeeklyDateHelper working</span><br>";
        echo "<p>Current week: {$currentWeek['start']} to {$currentWeek['end']}</p>";
        
        $previousWeek = WeeklyDateHelper::getPreviousWeek();
        echo "<p>Previous week: {$previousWeek['start']} to {$previousWeek['end']}</p>";
    } catch (Exception $e) {
        echo "<span class='error'>✗ WeeklyDateHelper failed: " . $e->getMessage() . "</span><br>";
    }
    echo "</div>";

    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>6. Report Generation Test</h3></div>";
    
    // Test report generation with previous week
    try {
        $previousWeek = WeeklyDateHelper::getPreviousWeek();
        $weekStart = $previousWeek['start'];
        $weekEnd = $previousWeek['end'];
        
        echo "<p>Testing report generation for week: {$weekStart} to {$weekEnd}</p>";
        
        // Check if runWeeklyMatching method exists
        if (method_exists($pvSystem, 'runWeeklyMatching')) {
            echo "<span class='success'>✓ runWeeklyMatching method exists</span><br>";
            
            // Test the method call
            echo "<p>Calling runWeeklyMatching...</p>";
            $result = $pvSystem->runWeeklyMatching($weekStart, $weekEnd, false, 'DEBUG_TEST');
            
            if ($result !== false) {
                echo "<span class='success'>✓ runWeeklyMatching executed successfully</span><br>";
                echo "<pre>" . print_r($result, true) . "</pre>";
            } else {
                echo "<span class='error'>✗ runWeeklyMatching returned false</span><br>";
            }
        } else {
            echo "<span class='error'>✗ runWeeklyMatching method not found</span><br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>✗ Report generation test failed: " . $e->getMessage() . "</span><br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    echo "</div>";

    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>7. Form Submission Simulation</h3></div>";
    
    // Simulate form submission
    echo "<p>Simulating form submission...</p>";
    $_POST['action'] = 'generate_report';
    $_POST['week_start'] = $previousWeek['start'];
    $_POST['week_end'] = $previousWeek['end'];
    
    echo "<p>POST data set:</p>";
    echo "<ul>";
    echo "<li>action: {$_POST['action']}</li>";
    echo "<li>week_start: {$_POST['week_start']}</li>";
    echo "<li>week_end: {$_POST['week_end']}</li>";
    echo "</ul>";
    
    // Test the form processing logic
    $action = $_POST['action'] ?? '';
    if ($action === 'generate_report') {
        echo "<span class='success'>✓ Action detected correctly</span><br>";
        
        $weekStart = $_POST['week_start'] ?? '';
        $weekEnd = $_POST['week_end'] ?? '';
        
        if ($weekStart && $weekEnd) {
            echo "<span class='success'>✓ Week dates received correctly</span><br>";
            
            // Validate dates
            $startDate = new DateTime($weekStart);
            $endDate = new DateTime($weekEnd);
            
            if ($startDate <= $endDate) {
                echo "<span class='success'>✓ Date validation passed</span><br>";
            } else {
                echo "<span class='error'>✗ Date validation failed</span><br>";
            }
        } else {
            echo "<span class='error'>✗ Week dates not received</span><br>";
        }
    } else {
        echo "<span class='error'>✗ Action not detected</span><br>";
    }
    echo "</div>";

    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>8. PHP Error Log Check</h3></div>";
    
    // Check for PHP errors
    $errorLogPath = ini_get('error_log');
    echo "<p>PHP error log path: {$errorLogPath}</p>";
    
    if (file_exists($errorLogPath)) {
        $errorLog = file_get_contents($errorLogPath);
        $recentErrors = array_slice(explode("\n", $errorLog), -10);
        echo "<p>Recent PHP errors:</p>";
        echo "<pre>" . implode("\n", $recentErrors) . "</pre>";
    } else {
        echo "<p>No PHP error log found or accessible</p>";
    }
    echo "</div>";

    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h2>🎯 Debug Summary</h2></div>";
    echo "<p class='info'>Debug completed. Check the results above to identify any issues.</p>";
    echo "<p><strong>Common Issues to Check:</strong></p>";
    echo "<ul>";
    echo "<li>Authentication problems</li>";
    echo "<li>Database connection issues</li>";
    echo "<li>Missing or corrupted class files</li>";
    echo "<li>PHP errors in error log</li>";
    echo "<li>Form submission problems</li>";
    echo "<li>Method execution failures</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='debug-section'>";
    echo "<div class='debug-header'><h3>❌ Critical Error</h3></div>";
    echo "<p class='error'>Critical error during debug: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}
?>
