<?php
/**
 * Fix PV Tracking Issues
 * Initialize PV usage tracking for all users and fix common issues
 */

require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../config/config.php';

// Initialize classes
$db = Database::getInstance();
$pvSystem = new PVSystem();

echo "<h2>PV Tracking Fix Utility</h2>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
</style>";

// Check current state
echo "<h3>1. Current PV Tracking State:</h3>";
try {
    // Count untracked PV transactions
    $untrackedStmt = $db->query("
        SELECT COUNT(*) as count
        FROM pv_transactions pt
        LEFT JOIN pv_usage_tracking put ON pt.id = put.pv_transaction_id
        WHERE put.id IS NULL
    ");
    $untrackedCount = $untrackedStmt->fetch()['count'];
    
    echo "<p>Untracked PV Transactions: <strong>{$untrackedCount}</strong></p>";
    
    if ($untrackedCount > 0) {
        echo "<p class='warning'>⚠ Found {$untrackedCount} PV transactions without tracking records!</p>";
        
        // Initialize tracking for all users
        echo "<h3>2. Initializing PV Tracking:</h3>";
        
        $userStmt = $db->query("SELECT DISTINCT user_id FROM pv_transactions");
        $users = $userStmt->fetchAll();
        
        $initializedCount = 0;
        foreach ($users as $user) {
            try {
                $pvSystem->initializePVUsageTracking($user['user_id']);
                $initializedCount++;
                echo "<p class='success'>✓ Initialized tracking for user: {$user['user_id']}</p>";
            } catch (Exception $e) {
                echo "<p class='error'>✗ Failed to initialize tracking for user {$user['user_id']}: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p class='success'><strong>Initialized tracking for {$initializedCount} users</strong></p>";
        
    } else {
        echo "<p class='success'>✓ All PV transactions are properly tracked</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking tracking state: " . $e->getMessage() . "</p>";
}

// Check for negative remaining amounts
echo "<h3>3. Checking for Data Integrity Issues:</h3>";
try {
    $negativeStmt = $db->query("
        SELECT COUNT(*) as count
        FROM pv_usage_tracking
        WHERE remaining_amount < 0
    ");
    $negativeCount = $negativeStmt->fetch()['count'];
    
    if ($negativeCount > 0) {
        echo "<p class='warning'>⚠ Found {$negativeCount} records with negative remaining amounts</p>";
        
        // Fix negative amounts
        $fixStmt = $db->prepare("
            UPDATE pv_usage_tracking
            SET remaining_amount = 0,
                used_amount = original_amount
            WHERE remaining_amount < 0
        ");
        $fixStmt->execute();
        
        echo "<p class='success'>✓ Fixed negative remaining amounts</p>";
    } else {
        echo "<p class='success'>✓ No negative remaining amounts found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking data integrity: " . $e->getMessage() . "</p>";
}

// Verify wallet table exists and is properly structured
echo "<h3>4. Checking Wallet System:</h3>";
try {
    $walletCheckStmt = $db->query("SHOW TABLES LIKE 'wallet'");
    if ($walletCheckStmt->fetch()) {
        echo "<p class='success'>✓ Wallet table exists</p>";
        
        // Check if all users have wallet records
        $missingWalletStmt = $db->query("
            SELECT COUNT(*) as count
            FROM users u
            LEFT JOIN wallet w ON u.user_id = w.user_id
            WHERE u.status = 'active' AND w.user_id IS NULL
        ");
        $missingWallets = $missingWalletStmt->fetch()['count'];
        
        if ($missingWallets > 0) {
            echo "<p class='warning'>⚠ Found {$missingWallets} active users without wallet records</p>";
            
            // Create missing wallet records
            $createWalletStmt = $db->prepare("
                INSERT INTO wallet (user_id, balance, total_earned)
                SELECT u.user_id, 0, 0
                FROM users u
                LEFT JOIN wallet w ON u.user_id = w.user_id
                WHERE u.status = 'active' AND w.user_id IS NULL
            ");
            $createWalletStmt->execute();
            
            echo "<p class='success'>✓ Created missing wallet records</p>";
        } else {
            echo "<p class='success'>✓ All active users have wallet records</p>";
        }
        
    } else {
        echo "<p class='error'>✗ Wallet table does not exist!</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error checking wallet system: " . $e->getMessage() . "</p>";
}

// Test income calculation for a sample user
echo "<h3>5. Testing Income Calculation:</h3>";
try {
    $sampleUserStmt = $db->query("
        SELECT u.user_id, u.full_name
        FROM users u
        JOIN pv_transactions pt ON u.user_id = pt.user_id
        WHERE u.status = 'active'
        GROUP BY u.user_id
        HAVING COUNT(pt.id) > 0
        LIMIT 1
    ");
    $sampleUser = $sampleUserStmt->fetch();
    
    if ($sampleUser) {
        echo "<p>Testing with user: {$sampleUser['full_name']} ({$sampleUser['user_id']})</p>";
        
        $weekStart = date('Y-m-d', strtotime('monday this week'));
        $availablePV = $pvSystem->getAvailablePVForWeek($sampleUser['user_id'], $weekStart);
        
        echo "<ul>";
        echo "<li>Available Left PV: " . number_format($availablePV['left_pv'], 2) . "</li>";
        echo "<li>Available Right PV: " . number_format($availablePV['right_pv'], 2) . "</li>";
        echo "<li>Potential Matched PV: " . number_format(min($availablePV['left_pv'], $availablePV['right_pv']), 2) . "</li>";
        echo "</ul>";
        
        if ($availablePV['left_pv'] > 0 || $availablePV['right_pv'] > 0) {
            echo "<p class='success'>✓ PV calculation working correctly</p>";
        } else {
            echo "<p class='warning'>⚠ No available PV found for test user</p>";
        }
        
    } else {
        echo "<p class='warning'>⚠ No users with PV transactions found for testing</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error testing income calculation: " . $e->getMessage() . "</p>";
}

// Summary and recommendations
echo "<h3>6. Summary and Recommendations:</h3>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
echo "<h4>Common Issues Fixed:</h4>";
echo "<ul>";
echo "<li>✓ Initialized PV usage tracking for all users</li>";
echo "<li>✓ Fixed negative remaining amounts in tracking</li>";
echo "<li>✓ Created missing wallet records</li>";
echo "<li>✓ Verified system integrity</li>";
echo "</ul>";

echo "<h4>Next Steps:</h4>";
echo "<ol>";
echo "<li>Go to <strong>Admin → Weekly Income Reports</strong></li>";
echo "<li>Click <strong>Generate Report</strong></li>";
echo "<li>Select the desired week period</li>";
echo "<li>Submit the form to generate income</li>";
echo "</ol>";

echo "<h4>If Issues Persist:</h4>";
echo "<ul>";
echo "<li>Run the diagnostic script: <code>/admin/diagnose-income-issue.php</code></li>";
echo "<li>Check the logs directory for detailed error information</li>";
echo "<li>Verify that users have sufficient PV on both left and right sides</li>";
echo "</ul>";
echo "</div>";

echo "<p class='success'><strong>PV Tracking Fix Complete!</strong></p>";
?>
