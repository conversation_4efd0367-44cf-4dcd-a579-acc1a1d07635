<?php
/**
 * Migration: Change Processing Day to Friday at 11:59 PM
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "Updating processing day to Friday at 11:59 PM...\n";
    
    // Update or insert weekly processing day to Friday (5)
    $stmt = $db->prepare("INSERT INTO config (config_key, config_value, config_type, category, description, is_public) VALUES ('weekly_processing_day', '5', 'number', 'business', 'Weekly processing day (5=Friday)', false) ON DUPLICATE KEY UPDATE config_value = '5'");
    $stmt->execute();
    echo "✓ Updated weekly processing day to Friday (5)\n";

    // Also update weekly_matching_day for backward compatibility
    $stmt = $db->prepare("INSERT INTO config (config_key, config_value, config_type, category, description, is_public) VALUES ('weekly_matching_day', '5', 'number', 'business', 'Weekly matching day (5=Friday)', false) ON DUPLICATE KEY UPDATE config_value = '5'");
    $stmt->execute();
    echo "✓ Updated weekly matching day to Friday (5)\n";

    // Update weekly matching time to 11:59 PM
    $stmt = $db->prepare("INSERT INTO config (config_key, config_value, config_type, category, description, is_public) VALUES ('weekly_matching_time', '23:59', 'string', 'business', 'Weekly matching time (HH:MM)', false) ON DUPLICATE KEY UPDATE config_value = '23:59'");
    $stmt->execute();
    echo "✓ Updated weekly matching time to 23:59\n";

    // Update descriptions
    $stmt = $db->prepare("UPDATE config SET description = 'Weekly processing day (5=Friday)' WHERE config_key = 'weekly_processing_day'");
    $stmt->execute();
    $stmt = $db->prepare("UPDATE config SET description = 'Weekly matching day (5=Friday)' WHERE config_key = 'weekly_matching_day'");
    $stmt->execute();
    echo "✓ Updated descriptions\n";
    
    echo "\n✅ Processing day successfully changed to Friday at 11:59 PM!\n";
    echo "New cron schedule: 59 23 * * 5\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
}
?>