# Saturday-Friday Weekly Income Generation System

## Overview

The system has been updated to use **Saturday-Friday weeks** instead of the previous Monday-Sunday weeks for all income generation and processing logic. This change affects all weekly calculations, processing schedules, and reporting.

## New Weekly Schedule

### Week Definition
- **Week Start**: Saturday at 12:00 AM (00:00)
- **Week End**: Friday at 11:59 PM (23:59)
- **Duration**: 7 days (Saturday through Friday)

### Processing Schedule
- **Income Processing**: Friday at 11:59 PM (end of week)
- **Payment Processing**: Saturday at 10:00 AM (day after week ends)
- **Reports Available**: Saturday morning after processing

## Key Changes Made

### 1. Core Date Calculation Functions
- **New File**: `includes/WeeklyDateHelper.php`
- Provides centralized functions for all Saturday-Friday week calculations
- Replaces scattered `strtotime('monday this week')` calls throughout the system

### 2. Updated Processing Scripts
- **`cron/weekly-matching.php`** - Updated to use Saturday-Friday weeks
- **`cron/optimized-weekly-matching.php`** - Updated to use Saturday-Friday weeks  
- **`cron/weekly-payment-processor.php`** - Updated to use Saturday-Friday weeks
- **`cron/enhanced-weekly-income-processor.php`** - Updated to use Saturday-Friday weeks

### 3. Updated PV System Classes
- **`includes/PVSystem.php`** - Updated `runWeeklyMatching()` method
- **`includes/EnhancedPVSystem.php`** - Added WeeklyDateHelper integration

### 4. Updated Admin Interfaces
- **`admin/weekly-income-reports.php`** - Updated week calculations
- **`admin/payment-management.php`** - Updated default week selection
- **`admin/test-income-generation.php`** - Updated test parameters
- **`admin/diagnose-income-issue.php`** - Updated diagnostic dates

### 5. Configuration Updates
- **`weekly_processing_day`**: Changed to 5 (Friday)
- **`weekly_matching_time`**: Set to 23:59 (11:59 PM)
- **`payment_processing_day`**: Set to 6 (Saturday)
- **`week_definition`**: Added as 'saturday_friday'

## WeeklyDateHelper Functions

### Core Functions
```php
WeeklyDateHelper::getWeekStart($date = null)     // Get Saturday start date
WeeklyDateHelper::getWeekEnd($date = null)       // Get Friday end date
WeeklyDateHelper::getCurrentWeek()               // Current Saturday-Friday week
WeeklyDateHelper::getPreviousWeek()              // Previous Saturday-Friday week
WeeklyDateHelper::getNextWeek()                  // Next Saturday-Friday week
```

### Utility Functions
```php
WeeklyDateHelper::getProcessingDay()             // Returns 5 (Friday)
WeeklyDateHelper::getProcessingTime()            // Returns '23:59'
WeeklyDateHelper::isProcessingDay()              // True if today is Friday
WeeklyDateHelper::isPastProcessingTime()         // True if past 11:59 PM
WeeklyDateHelper::formatWeekRange($start, $end)  // Format week range string
```

## Migration Process

### 1. Run Migration Script
```bash
php migrate-saturday-friday-weeks.php
```

### 2. Update Cron Jobs
```bash
# Weekly Income Processing (Friday 11:59 PM)
59 23 * * 5 /usr/bin/php /path/to/cron/weekly-matching.php

# Payment Processing (Saturday 10:00 AM)  
0 10 * * 6 /usr/bin/php /path/to/cron/weekly-payment-processor.php
```

### 3. Test the Changes
```bash
# Test the new weekly logic
php admin/test-saturday-friday-weeks.php
```

## Example Week Scenarios

### Scenario 1: Current Week
- **Today**: Wednesday, August 21, 2025
- **Current Week**: Saturday Aug 16 - Friday Aug 22, 2025
- **Previous Week**: Saturday Aug 9 - Friday Aug 15, 2025

### Scenario 2: Processing Day
- **Today**: Friday, August 22, 2025 at 11:59 PM
- **Action**: Process income for week Aug 16-22
- **Next**: Saturday Aug 23 at 10:00 AM - Process payments

### Scenario 3: Payment Day
- **Today**: Saturday, August 23, 2025 at 10:00 AM
- **Action**: Process payments for week Aug 16-22
- **Status**: Week Aug 16-22 is complete

## Benefits of Saturday-Friday Weeks

1. **Weekend Processing**: Income processing happens at end of business week
2. **Payment Timing**: Payments processed on Saturday, ready for Monday
3. **Business Alignment**: Better alignment with business operations
4. **User Experience**: Users receive payments over weekend
5. **Administrative**: Easier weekend monitoring and support

## Backward Compatibility

- **Existing Data**: All existing weekly income logs remain valid
- **Historical Reports**: Previous Monday-Sunday data is preserved
- **Gradual Transition**: Only new processing uses Saturday-Friday logic
- **No Data Loss**: No existing data is modified or lost

## Testing and Validation

### Test Scripts Available
1. **`admin/test-saturday-friday-weeks.php`** - Comprehensive logic testing
2. **`admin/test-income-generation.php`** - Updated for Saturday-Friday weeks
3. **`admin/verify-pv-fix.php`** - PV reuse prevention testing

### Validation Checklist
- [ ] Week calculations return Saturday-Friday ranges
- [ ] Processing day is correctly identified as Friday
- [ ] Payment processing uses Saturday schedule
- [ ] Admin interfaces show correct week ranges
- [ ] Cron jobs updated to new schedule
- [ ] Configuration values updated
- [ ] Test scripts pass all validations

## Troubleshooting

### Common Issues
1. **Wrong Week Dates**: Ensure WeeklyDateHelper is included in all files
2. **Processing Day Errors**: Verify `weekly_processing_day` config is set to 5
3. **Cron Job Issues**: Update cron schedule to Friday 23:59 and Saturday 10:00
4. **Old Logic**: Search for `strtotime('monday')` and replace with WeeklyDateHelper

### Debug Commands
```php
// Check current week calculation
$week = WeeklyDateHelper::getCurrentWeek();
var_dump($week);

// Verify processing day
echo "Processing Day: " . WeeklyDateHelper::getProcessingDay();
echo "Is Processing Day: " . (WeeklyDateHelper::isProcessingDay() ? 'Yes' : 'No');
```

## Support and Monitoring

### First Week Monitoring
- Monitor processing on first Friday at 11:59 PM
- Verify payments process correctly on Saturday
- Check all reports show correct week ranges
- Validate user income calculations

### Ongoing Maintenance
- Weekly verification of processing schedule
- Monitor for any date calculation errors
- Regular testing of WeeklyDateHelper functions
- User feedback on new payment timing

## Conclusion

The Saturday-Friday weekly system provides better business alignment and user experience while maintaining all existing functionality. The centralized WeeklyDateHelper class ensures consistent date calculations across the entire system.
