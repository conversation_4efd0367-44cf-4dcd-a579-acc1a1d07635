<?php
/**
 * Migration Script: Update User Activation Based on PV Threshold
 * 
 * This script updates existing users to follow the new activation rules:
 * - Users with less than 500 PV should be set to 'inactive'
 * - Users with 500 PV or more should be set to 'active'
 * - Suspended users remain suspended (manual admin action required)
 */

require_once 'config/Connection.php';
require_once 'models/User.php';

echo "<h2>User Activation Migration Script</h2>\n";
echo "<p>Updating user activation status based on 500 PV threshold...</p>\n";

try {
    $db = Database::getInstance();
    $userModel = new User();
    
    // Get all users except suspended ones
    $stmt = $db->query("
        SELECT user_id, full_name, status, self_pv 
        FROM users 
        WHERE status != 'suspended'
        ORDER BY user_id
    ");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: orange;'>No users found to process.</p>\n";
        exit;
    }
    
    echo "<h3>Processing " . count($users) . " users...</h3>\n";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>User ID</th><th>Full Name</th><th>Current Status</th><th>Self PV</th><th>New Status</th><th>Action</th>";
    echo "</tr>\n";
    
    $activationThreshold = 500.0;
    $updatedCount = 0;
    $noChangeCount = 0;
    $errorCount = 0;
    
    foreach ($users as $user) {
        $userId = $user['user_id'];
        $fullName = htmlspecialchars($user['full_name']);
        $currentStatus = $user['status'];
        $selfPV = floatval($user['self_pv'] ?? 0);
        
        // Determine new status based on PV threshold
        $newStatus = ($selfPV >= $activationThreshold) ? 'active' : 'inactive';
        
        echo "<tr>";
        echo "<td>{$userId}</td>";
        echo "<td>{$fullName}</td>";
        echo "<td style='color: " . ($currentStatus === 'active' ? 'green' : 'red') . ";'>{$currentStatus}</td>";
        echo "<td>" . number_format($selfPV, 2) . "</td>";
        echo "<td style='color: " . ($newStatus === 'active' ? 'green' : 'red') . ";'>{$newStatus}</td>";
        
        if ($currentStatus === $newStatus) {
            echo "<td style='color: gray;'>No change needed</td>";
            $noChangeCount++;
        } else {
            try {
                // Update user status
                $updateStmt = $db->prepare("UPDATE users SET status = ? WHERE user_id = ?");
                $result = $updateStmt->execute([$newStatus, $userId]);
                
                if ($result) {
                    echo "<td style='color: blue;'>Updated: {$currentStatus} → {$newStatus}</td>";
                    $updatedCount++;
                } else {
                    echo "<td style='color: red;'>Failed to update</td>";
                    $errorCount++;
                }
            } catch (Exception $e) {
                echo "<td style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</td>";
                $errorCount++;
            }
        }
        
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    echo "<h3>Migration Summary</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Total Users Processed:</strong> " . count($users) . "</li>\n";
    echo "<li><strong>Users Updated:</strong> {$updatedCount}</li>\n";
    echo "<li><strong>No Change Needed:</strong> {$noChangeCount}</li>\n";
    echo "<li><strong>Errors:</strong> {$errorCount}</li>\n";
    echo "</ul>\n";
    
    if ($updatedCount > 0) {
        echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<strong>✓ Migration completed successfully!</strong><br>\n";
        echo "Updated {$updatedCount} users based on the 500 PV activation threshold.\n";
        echo "</div>\n";
    }
    
    if ($errorCount > 0) {
        echo "<div style='background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<strong>⚠ Warning:</strong> {$errorCount} users could not be updated. Please check the errors above.\n";
        echo "</div>\n";
    }
    
    echo "<h3>New Activation Rules</h3>\n";
    echo "<div style='background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<strong>ℹ Information:</strong><br>\n";
    echo "• Users need <strong>500 PV or more</strong> to be activated<br>\n";
    echo "• New users will start as <strong>inactive</strong> until they reach 500 PV<br>\n";
    echo "• Users will be automatically activated when they reach the threshold<br>\n";
    echo "• Inactive users will be displayed in <strong>red color</strong> in the Node view<br>\n";
    echo "• The tree view will show PV progress for inactive users\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "\n";
    echo "</div>\n";
    error_log("User activation migration error: " . $e->getMessage());
}

echo "<hr>\n";
echo "<p><em>Migration script completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
