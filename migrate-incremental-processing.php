<?php
/**
 * Migration: Enable Incremental PV Processing
 * 
 * This migration modifies the database to allow multiple weekly income logs
 * per user per week for incremental processing of unused PVs.
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "=== Enabling Incremental PV Processing ===\n";
    echo "Start time: " . date('Y-m-d H:i:s') . "\n\n";
    
    // Step 1: Check if unique constraint exists
    echo "1. Checking existing constraints on weekly_income_logs...\n";
    $constraintCheck = $db->query("
        SELECT CONSTRAINT_NAME 
        FROM information_schema.TABLE_CONSTRAINTS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'weekly_income_logs' 
        AND CONSTRAINT_TYPE = 'UNIQUE'
        AND CONSTRAINT_NAME = 'unique_user_week'
    ");
    $constraintExists = $constraintCheck->fetch();
    
    if ($constraintExists) {
        echo "✓ Found unique_user_week constraint\n";
        
        // Step 2: Drop the unique constraint
        echo "2. Dropping unique constraint to allow multiple entries per user per week...\n";
        $db->exec("ALTER TABLE weekly_income_logs DROP INDEX unique_user_week");
        echo "✓ Dropped unique_user_week constraint\n";
    } else {
        echo "ℹ No unique_user_week constraint found (already removed or doesn't exist)\n";
    }
    
    // Step 3: Add new columns for incremental processing tracking
    echo "3. Adding columns for incremental processing tracking...\n";
    
    // Check if report_sequence column exists
    $columnCheck = $db->query("
        SELECT COLUMN_NAME 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'weekly_income_logs' 
        AND COLUMN_NAME = 'report_sequence'
    ");
    
    if (!$columnCheck->fetch()) {
        $db->exec("ALTER TABLE weekly_income_logs ADD COLUMN report_sequence INT DEFAULT 1 AFTER week_end_date");
        echo "✓ Added report_sequence column\n";
    } else {
        echo "ℹ report_sequence column already exists\n";
    }
    
    // Check if processing_type column exists
    $columnCheck2 = $db->query("
        SELECT COLUMN_NAME 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'weekly_income_logs' 
        AND COLUMN_NAME = 'processing_type'
    ");
    
    if (!$columnCheck2->fetch()) {
        $db->exec("ALTER TABLE weekly_income_logs ADD COLUMN processing_type ENUM('initial', 'incremental') DEFAULT 'initial' AFTER report_sequence");
        echo "✓ Added processing_type column\n";
    } else {
        echo "ℹ processing_type column already exists\n";
    }
    
    // Check if report_id column exists
    $columnCheck3 = $db->query("
        SELECT COLUMN_NAME 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'weekly_income_logs' 
        AND COLUMN_NAME = 'report_id'
    ");
    
    if (!$columnCheck3->fetch()) {
        $db->exec("ALTER TABLE weekly_income_logs ADD COLUMN report_id VARCHAR(30) NULL AFTER processing_type");
        echo "✓ Added report_id column\n";
    } else {
        echo "ℹ report_id column already exists\n";
    }
    
    // Step 4: Add new indexes for efficient querying
    echo "4. Adding indexes for incremental processing...\n";
    
    try {
        $db->exec("CREATE INDEX idx_user_week_sequence ON weekly_income_logs (user_id, week_start_date, report_sequence)");
        echo "✓ Added idx_user_week_sequence index\n";
    } catch (Exception $e) {
        echo "ℹ idx_user_week_sequence index already exists or failed to create\n";
    }
    
    try {
        $db->exec("CREATE INDEX idx_processing_type ON weekly_income_logs (processing_type)");
        echo "✓ Added idx_processing_type index\n";
    } catch (Exception $e) {
        echo "ℹ idx_processing_type index already exists or failed to create\n";
    }
    
    try {
        $db->exec("CREATE INDEX idx_report_id ON weekly_income_logs (report_id)");
        echo "✓ Added idx_report_id index\n";
    } catch (Exception $e) {
        echo "ℹ idx_report_id index already exists or failed to create\n";
    }
    
    // Step 5: Update existing records
    echo "5. Updating existing records with default values...\n";
    $updateStmt = $db->prepare("
        UPDATE weekly_income_logs 
        SET report_sequence = 1, processing_type = 'initial' 
        WHERE report_sequence IS NULL OR processing_type IS NULL
    ");
    $updateStmt->execute();
    $updatedRows = $updateStmt->rowCount();
    echo "✓ Updated {$updatedRows} existing records\n";
    
    // Step 6: Add configuration for incremental processing
    echo "6. Adding configuration for incremental processing...\n";
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, config_type, category, description, is_public) 
        VALUES ('incremental_processing_enabled', 'true', 'boolean', 'business', 'Enable incremental processing of unused PVs within the same week', false) 
        ON DUPLICATE KEY UPDATE 
        config_value = 'true',
        description = 'Enable incremental processing of unused PVs within the same week'
    ");
    $stmt->execute();
    echo "✓ Added incremental processing configuration\n";
    
    // Step 7: Log the migration
    echo "7. Logging migration...\n";
    $stmt = $db->prepare("
        INSERT INTO system_logs (log_type, category, message, context, created_at) 
        VALUES ('info', 'migration', 'Enabled incremental PV processing', ?, NOW())
    ");
    $context = json_encode([
        'migration' => 'incremental-processing',
        'changes' => [
            'removed_constraint' => 'unique_user_week',
            'added_columns' => ['report_sequence', 'processing_type', 'report_id'],
            'added_indexes' => ['idx_user_week_sequence', 'idx_processing_type', 'idx_report_id'],
            'updated_records' => $updatedRows
        ],
        'features' => [
            'multiple_reports_per_week' => true,
            'incremental_pv_processing' => true,
            'unused_pv_only' => true
        ]
    ]);
    $stmt->execute([$context]);
    echo "✓ Migration logged to system_logs\n";
    
    echo "\n=== Migration Completed Successfully ===\n";
    echo "End time: " . date('Y-m-d H:i:s') . "\n\n";
    
    echo "CHANGES MADE:\n";
    echo "1. ✅ Removed unique constraint on (user_id, week_start_date, week_end_date)\n";
    echo "2. ✅ Added report_sequence column to track multiple reports per week\n";
    echo "3. ✅ Added processing_type column (initial/incremental)\n";
    echo "4. ✅ Added report_id column to link with weekly_income_reports\n";
    echo "5. ✅ Added indexes for efficient querying\n";
    echo "6. ✅ Updated existing records with default values\n\n";
    
    echo "NEW BEHAVIOR:\n";
    echo "• Users can be processed multiple times within the same week\n";
    echo "• Only unused PVs will be processed in subsequent reports\n";
    echo "• Each processing creates a new weekly_income_logs entry\n";
    echo "• report_sequence tracks the order of processing (1, 2, 3, etc.)\n";
    echo "• processing_type indicates 'initial' or 'incremental' processing\n\n";

} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
