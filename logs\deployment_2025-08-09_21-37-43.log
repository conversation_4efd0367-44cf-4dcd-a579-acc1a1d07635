[2025-08-09 21:37:43] Starting Enhanced PV System Deployment
[2025-08-09 21:37:43] ==================================================
[2025-08-09 21:37:43] Step 1: Pre-deployment validation
[2025-08-09 21:37:43] ✓ Database connectivity verified
[2025-08-09 21:37:43] ✓ Required PHP extensions verified
[2025-08-09 21:37:43] ✓ File permissions verified
[2025-08-09 21:37:43] ✓ Base system tables verified
[2025-08-09 21:37:43] Step 2: Creating database backup
[2025-08-09 21:37:43] ✓ Database backup created: pre_deployment_backup_2025-08-09_21-37-43.sql
[2025-08-09 21:37:43] Step 3: Updating database schema
[2025-08-09 21:37:43] ✓ Database schema updated
[2025-08-09 21:37:43] Schema output: ✅ Database created/selected successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Table created successfully.
✅ Setup completed successfully.
🔐 Default Admin:
Username: admin
Password: admin123 (please change after login)

[2025-08-09 21:37:43] Step 4: Migrating existing data
[2025-08-09 21:37:43] ✓ Migrated 0 PV transactions to usage tracking
[2025-08-09 21:37:43] ℹ Processing status column not found in existing schema (this is normal for upgrades)
[2025-08-09 21:37:43] Step 5: Validating data integrity
[2025-08-09 21:37:43] ✓ Data integrity validation passed
[2025-08-09 21:37:43] Step 6: Running system tests
[2025-08-09 21:37:44] ❌ Deployment failed: System tests failed with return code: 1
[2025-08-09 21:37:44] Rolling back changes...
[2025-08-09 21:37:44] Attempting to rollback changes...
[2025-08-09 21:37:44] ⚠ Backup file exists at: C:\xampp\htdocs\shaktipure/backups/pre_deployment_backup_2025-08-09_21-37-43.sql
[2025-08-09 21:37:44] ⚠ Manual database restore may be required
[2025-08-09 21:37:44] ⚠ You can restore using: mysql -u root database_name < backup_file.sql
