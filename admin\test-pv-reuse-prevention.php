<?php
require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';
require_once '../config/config.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering for clean HTML
ob_start();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PV Reuse Prevention</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .test-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .test-header { background-color: #f0f0f0; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 5px 5px 0 0; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔒 PV Reuse Prevention Test</h1>
    <p>This test verifies that PV cannot be reused when multiple reports are generated for the same week.</p>

<?php
try {
    // Initialize classes
    $db = Database::getInstance();
    $pvSystem = new PVSystem();
    $wallet = new Wallet();
    $config = Config::getInstance();

    // Test configuration
    $testWeekStart = '2025-08-12'; // Monday
    $testWeekEnd = '2025-08-18';   // Sunday
    
    echo "<div class='test-section'>";
    echo "<div class='test-header'><h2>📋 Test Configuration</h2></div>";
    echo "<ul>";
    echo "<li><strong>Test Week:</strong> {$testWeekStart} to {$testWeekEnd}</li>";
    echo "<li><strong>Test Date:</strong> " . date('Y-m-d H:i:s') . "</li>";
    echo "</ul>";
    echo "</div>";

    // Find a test user with available PV
    echo "<div class='test-section'>";
    echo "<div class='test-header'><h3>👤 Finding Test User</h3></div>";
    
    $testUserStmt = $db->query("
        SELECT u.user_id, u.full_name, u.email,
               SUM(CASE WHEN put.side = 'left' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) as left_pv,
               SUM(CASE WHEN put.side = 'right' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) as right_pv
        FROM users u
        JOIN pv_usage_tracking put ON u.user_id = put.user_id
        WHERE u.status = 'active' 
        AND put.remaining_amount > 0
        AND (put.week_used IS NULL OR put.week_used != '{$testWeekStart}')
        GROUP BY u.user_id, u.full_name, u.email
        HAVING SUM(CASE WHEN put.side = 'left' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) > 0
           AND SUM(CASE WHEN put.side = 'right' AND put.remaining_amount > 0 THEN put.remaining_amount ELSE 0 END) > 0
        LIMIT 1
    ");
    
    $testUser = $testUserStmt->fetch();
    
    if (!$testUser) {
        echo "<p class='error'>❌ No suitable test user found with available PV for both sides.</p>";
        echo "</div></body></html>";
        exit;
    }
    
    echo "<p class='success'>✅ Found test user: <strong>{$testUser['full_name']}</strong> (ID: {$testUser['user_id']})</p>";
    echo "<ul>";
    echo "<li>Available Left PV: " . number_format($testUser['left_pv'], 2) . "</li>";
    echo "<li>Available Right PV: " . number_format($testUser['right_pv'], 2) . "</li>";
    echo "<li>Potential Matched PV: " . number_format(min($testUser['left_pv'], $testUser['right_pv']), 2) . "</li>";
    echo "</ul>";
    echo "</div>";

    // Test 1: First processing attempt
    echo "<div class='test-section'>";
    echo "<div class='test-header'><h3>🔄 Test 1: First Processing Attempt</h3></div>";
    
    // Check if user was already processed for this week
    $existingStmt = $db->prepare("SELECT id FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
    $existingStmt->execute([$testUser['user_id'], $testWeekStart]);
    $existing = $existingStmt->fetch();
    
    if ($existing) {
        echo "<p class='info'>ℹ️ User already processed for this week. Deleting existing record for test...</p>";
        $deleteStmt = $db->prepare("DELETE FROM weekly_income_logs WHERE user_id = ? AND week_start_date = ?");
        $deleteStmt->execute([$testUser['user_id'], $testWeekStart]);
        
        // Reset PV usage for this week
        $resetStmt = $db->prepare("
            UPDATE pv_usage_tracking 
            SET week_used = NULL, processing_period = NULL, status = 'available',
                used_amount = 0, remaining_amount = original_amount
            WHERE user_id = ? AND week_used = ?
        ");
        $resetStmt->execute([$testUser['user_id'], $testWeekStart]);
        echo "<p class='info'>ℹ️ Reset PV usage for test week.</p>";
    }
    
    // Get wallet balance before
    $walletBefore = $wallet->getWallet($testUser['user_id']);
    $balanceBefore = $walletBefore['balance'];
    echo "<p>💰 Wallet balance before: ₹" . number_format($balanceBefore, 2) . "</p>";
    
    // Process first time
    $result1 = $pvSystem->processWeeklyPVMatching($testUser['user_id'], $testWeekStart, $testWeekEnd);
    
    if ($result1 && !isset($result1['already_processed'])) {
        echo "<p class='success'>✅ First processing completed successfully!</p>";
        echo "<ul>";
        echo "<li>Income Amount: ₹" . number_format($result1['income_amount'], 2) . "</li>";
        echo "<li>Capping Applied: ₹" . number_format($result1['capping_applied'], 2) . "</li>";
        echo "</ul>";
        
        // Get wallet balance after
        $walletAfter = $wallet->getWallet($testUser['user_id']);
        $balanceAfter = $walletAfter['balance'];
        echo "<p>💰 Wallet balance after: ₹" . number_format($balanceAfter, 2) . " (Increase: ₹" . number_format($balanceAfter - $balanceBefore, 2) . ")</p>";
    } else {
        echo "<p class='warning'>⚠️ First processing returned: " . json_encode($result1) . "</p>";
    }
    echo "</div>";

    // Test 2: Second processing attempt (should be blocked)
    echo "<div class='test-section'>";
    echo "<div class='test-header'><h3>🚫 Test 2: Second Processing Attempt (Should be Blocked)</h3></div>";
    
    $result2 = $pvSystem->processWeeklyPVMatching($testUser['user_id'], $testWeekStart, $testWeekEnd);
    
    if ($result2 && isset($result2['already_processed']) && $result2['already_processed']) {
        echo "<p class='success'>✅ Second processing correctly blocked! User already processed for this week.</p>";
        echo "<ul>";
        echo "<li>Income Amount: ₹" . number_format($result2['income_amount'], 2) . "</li>";
        echo "<li>Already Processed: " . ($result2['already_processed'] ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
    } else {
        echo "<p class='error'>❌ Second processing was NOT blocked! This indicates a problem.</p>";
        echo "<pre>" . json_encode($result2, JSON_PRETTY_PRINT) . "</pre>";
    }
    echo "</div>";

    // Test 3: Check PV usage status
    echo "<div class='test-section'>";
    echo "<div class='test-header'><h3>📊 Test 3: PV Usage Status Check</h3></div>";
    
    $pvUsageStmt = $db->prepare("
        SELECT side, 
               SUM(original_amount) as total_original,
               SUM(used_amount) as total_used,
               SUM(remaining_amount) as total_remaining,
               COUNT(*) as transaction_count
        FROM pv_usage_tracking
        WHERE user_id = ? AND week_used = ?
        GROUP BY side
    ");
    $pvUsageStmt->execute([$testUser['user_id'], $testWeekStart]);
    $pvUsage = $pvUsageStmt->fetchAll();
    
    if ($pvUsage) {
        echo "<p class='success'>✅ PV usage tracking found for this week:</p>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Side</th><th>Original</th><th>Used</th><th>Remaining</th><th>Transactions</th></tr>";
        foreach ($pvUsage as $usage) {
            echo "<tr>";
            echo "<td>{$usage['side']}</td>";
            echo "<td>" . number_format($usage['total_original'], 2) . "</td>";
            echo "<td>" . number_format($usage['total_used'], 2) . "</td>";
            echo "<td>" . number_format($usage['total_remaining'], 2) . "</td>";
            echo "<td>{$usage['transaction_count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ No PV usage found for this week.</p>";
    }
    echo "</div>";

    // Test 4: Available PV check
    echo "<div class='test-section'>";
    echo "<div class='test-header'><h3>🔍 Test 4: Available PV Check</h3></div>";
    
    $availablePV = $pvSystem->getAvailablePVForWeek($testUser['user_id'], $testWeekStart);
    echo "<p>Available PV for week {$testWeekStart}:</p>";
    echo "<ul>";
    echo "<li>Left PV: " . number_format($availablePV['left_pv'], 2) . "</li>";
    echo "<li>Right PV: " . number_format($availablePV['right_pv'], 2) . "</li>";
    echo "</ul>";
    
    if ($availablePV['left_pv'] == 0 && $availablePV['right_pv'] == 0) {
        echo "<p class='success'>✅ No available PV for this week - correctly preventing reuse!</p>";
    } else {
        echo "<p class='warning'>⚠️ Available PV found - this might indicate PV could be reused.</p>";
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<div class='test-header'><h2>🎯 Test Summary</h2></div>";
    echo "<p class='success'>✅ PV reuse prevention test completed successfully!</p>";
    echo "<p>The system correctly:</p>";
    echo "<ul>";
    echo "<li>✅ Processed the user for the first time</li>";
    echo "<li>✅ Blocked the second processing attempt</li>";
    echo "<li>✅ Tracked PV usage properly</li>";
    echo "<li>✅ Prevented PV reuse for the same week</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='test-section'>";
    echo "<div class='test-header'><h3>❌ Error</h3></div>";
    echo "<p class='error'>Error during testing: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}
?>

</body>
</html>
