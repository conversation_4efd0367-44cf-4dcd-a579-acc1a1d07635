<?php
/**
 * ID Card Download Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

// Get user details
$db = Database::getInstance();
$userStmt = $db->prepare("SELECT * FROM users WHERE user_id = ?");
$userStmt->execute([$userId]);
$userDetails = $userStmt->fetch();

if (!$userDetails) {
    Response::redirect('dashboard.php');
}

// Check if download is requested
$download = isset($_GET['download']);

if ($download) {
    // Set headers for PDF download
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="id-card-' . $userId . '.pdf"');
    // Note: For actual PDF generation, you would use a library like TCPDF or mPDF
    // For now, we'll show the HTML version that can be printed as PDF
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ID Card - <?php echo htmlspecialchars($userDetails['full_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { background: white !important; }
            .container { max-width: none !important; }
        }
        
        .id-card-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 60vh;
            padding: 40px 0;
        }
        
        @media (max-width: 768px) {
            .id-card {
                width: 300px;
                height: 190px;
            }
            
            .id-card-logo {
                font-size: 20px;
            }
            
            .user-info h5 {
                font-size: 16px;
            }
            
            .qr-placeholder {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation (hidden when printing) -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark no-print">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <img src="../assets/images/logo.png" alt="" style="height:50px; width:auto;">
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Action Buttons -->
        <div class="row no-print mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h3><i class="fas fa-id-card me-2"></i>Member ID Card</h3>
                    <div>
                        <button onclick="window.print()" class="btn btn-primary me-2">
                            <i class="fas fa-print me-1"></i>Print ID Card
                        </button>
                        <a href="?download=1" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- ID Card -->
        <div class="id-card-container">
            <div class="id-card">
                <div class="id-card-header">
                    <div class="id-card-logo"><?php echo SITE_NAME; ?></div>
                    <div class="id-card-subtitle">Member Identification Card</div>
                </div>
                <div class="id-card-body">
                    <div class="id-card-info">
                        <div class="user-info">
                            <h5><?php echo htmlspecialchars($userDetails['full_name']); ?></h5>
                            <div class="user-id">ID: <?php echo htmlspecialchars($userId); ?></div>
                            <div class="user-level">
                                Level: <?php echo htmlspecialchars($userDetails['user_level'] ?? 'Beginner'); ?>
                            </div>
                        </div>
                        <div class="qr-placeholder">
                            <div>
                                <i class="fas fa-qrcode"></i><br>
                                <small>QR Code</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Information -->
        <div class="row no-print">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Card Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td><?php echo htmlspecialchars($userDetails['full_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>User ID:</strong></td>
                                        <td><?php echo htmlspecialchars($userId); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><?php echo htmlspecialchars($userDetails['email']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td><?php echo htmlspecialchars($userDetails['phone']); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Level:</strong></td>
                                        <td><?php echo htmlspecialchars($userDetails['user_level'] ?? 'Beginner'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="status-badge status-<?php echo $userDetails['status']; ?>">
                                                <?php echo ucfirst($userDetails['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Join Date:</strong></td>
                                        <td><?php echo date('d M Y', strtotime($userDetails['created_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Placement:</strong></td>
                                        <td>
                                            <span class="badge bg-<?php echo $userDetails['placement_side'] === 'left' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($userDetails['placement_side'] ?? 'N/A'); ?> Side
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> This ID card serves as your official member identification. 
                            Please keep it safe and present it when required for verification purposes.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
