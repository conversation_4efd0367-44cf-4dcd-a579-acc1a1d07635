@echo off
REM Enhanced PV System - Windows Task Scheduler Setup
REM Run this batch file as Administrator to setup scheduled tasks

echo Setting up Windows Scheduled Tasks for Enhanced PV System...
echo.

REM Set the path to your PHP executable and project directory
set PHP_PATH=C:\xampp\php\php.exe
set PROJECT_PATH=C:\xampp\htdocs\shaktipure

REM Create weekly income processing task (every Friday at 11:59 PM)
echo Creating Weekly Income Processing task...
schtasks /create /tn "ShaktiPure Weekly Income Processing" /tr "%PHP_PATH% %PROJECT_PATH%\cron\enhanced-weekly-income-processor.php" /sc weekly /d FRI /st 23:59 /f

REM Create daily maintenance task (every day at 2 AM)
echo Creating Daily Maintenance task...
schtasks /create /tn "ShaktiPure Daily Maintenance" /tr "%PHP_PATH% %PROJECT_PATH%\cron\daily-maintenance.php" /sc daily /st 02:00 /f

REM Create system health check task (every hour)
echo Creating System Health Check task...
schtasks /create /tn "ShaktiPure System Health Check" /tr "%PHP_PATH% %PROJECT_PATH%\cron\system-health-check.php" /sc hourly /st 00:00 /f

echo.
echo Scheduled tasks created successfully!
echo.
echo To view created tasks, run: schtasks /query /tn "ShaktiPure*"
echo To delete tasks, run: schtasks /delete /tn "ShaktiPure*" /f
echo.
pause
