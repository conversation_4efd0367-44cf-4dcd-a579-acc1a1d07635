# 🚀 Production Deployment Guide
## ShaktiPure MLM Binary Plan System

This guide provides step-by-step instructions for deploying the MLM system to a production environment with optimal performance and security.

## 📋 Pre-Deployment Checklist

### Server Requirements
- **PHP**: 7.4+ with required extensions
  - PDO, PDO_MySQL, JSON, mbstring, OpenSSL, cURL, GD, Zip
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **Web Server**: Apache 2.4+ with mod_rewrite or Nginx 1.18+
- **Memory**: 512MB+ RAM (1GB+ recommended for cron jobs)
- **Storage**: 10GB+ available disk space
- **SSL Certificate**: Required for production

### Required PHP Extensions
```bash
# Check required extensions
php -m | grep -E "(pdo|mysql|json|mbstring|openssl|curl|gd|zip)"
```

## 🔧 Step 1: Server Preparation

### 1.1 Update System Packages
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 1.2 Install Required Software
```bash
# Ubuntu/Debian
sudo apt install apache2 mysql-server php7.4 php7.4-mysql php7.4-gd php7.4-curl php7.4-mbstring php7.4-zip php7.4-xml -y

# Enable Apache modules
sudo a2enmod rewrite
sudo a2enmod ssl
sudo a2enmod headers
```

### 1.3 Configure MySQL
```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create production database and user
mysql -u root -p
```

```sql
CREATE DATABASE shaktipure_mlm_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'mlm_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, INDEX ON shaktipure_mlm_prod.* TO 'mlm_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 📁 Step 2: File Deployment

### 2.1 Upload Files
```bash
# Upload files to web directory
sudo mkdir -p /var/www/html/shaktipure
sudo chown -R www-data:www-data /var/www/html/shaktipure

# Upload your files via SCP, FTP, or Git
# Example with Git:
cd /var/www/html
sudo git clone https://github.com/yourusername/shaktipure.git
sudo chown -R www-data:www-data shaktipure
```

### 2.2 Set File Permissions
```bash
cd /var/www/html/shaktipure

# Set directory permissions
sudo find . -type d -exec chmod 755 {} \;

# Set file permissions
sudo find . -type f -exec chmod 644 {} \;

# Make specific directories writable
sudo chmod 755 uploads/ logs/ config/
sudo chmod -R 755 uploads/
sudo chmod -R 755 logs/

# Secure sensitive files
sudo chmod 600 config/database.php config/production.php
```

## ⚙️ Step 3: Configuration

### 3.1 Database Configuration
```bash
# Copy and edit database configuration
sudo cp config/database.php.example config/database.php
sudo nano config/database.php
```

Update with production values:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'shaktipure_mlm_prod');
define('DB_USER', 'mlm_user');
define('DB_PASS', 'secure_password_here');
define('ENVIRONMENT', 'production');
```

### 3.2 Production Configuration
```bash
# Copy and edit production configuration
sudo cp config/production.php.example config/production.php
sudo nano config/production.php
```

Update production settings:
- Database credentials
- Email SMTP settings
- Razorpay live keys
- Site URL and SSL settings
- Security configurations

### 3.3 Initialize Database
```bash
# Run database setup
php setup.php

# Run database optimization
php optimize_database.php
```

## 🔒 Step 4: Security Configuration

### 4.1 Apache Virtual Host
```bash
sudo nano /etc/apache2/sites-available/shaktipure.conf
```

```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    ServerAlias www.yourdomain.com
    DocumentRoot /var/www/html/shaktipure
    
    # Redirect HTTP to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

<VirtualHost *:443>
    ServerName yourdomain.com
    ServerAlias www.yourdomain.com
    DocumentRoot /var/www/html/shaktipure
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    SSLCertificateChainFile /path/to/your/ca-bundle.crt
    
    # Security Headers
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # Directory Configuration
    <Directory /var/www/html/shaktipure>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Error and Access Logs
    ErrorLog ${APACHE_LOG_DIR}/shaktipure_error.log
    CustomLog ${APACHE_LOG_DIR}/shaktipure_access.log combined
</VirtualHost>
```

### 4.2 Enable Site and SSL
```bash
# Enable site
sudo a2ensite shaktipure.conf
sudo a2dissite 000-default.conf

# Restart Apache
sudo systemctl restart apache2
```

### 4.3 Firewall Configuration
```bash
# Configure UFW firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## ⏰ Step 5: Cron Job Setup

### 5.1 Create Cron Jobs
```bash
# Edit crontab for www-data user
sudo crontab -u www-data -e
```

Add the following cron jobs:
```bash
# Weekly PV matching (every Friday at 11:59 PM)
59 23 * * 5 /usr/bin/php /var/www/html/shaktipure/cron/optimized-weekly-matching.php >> /var/www/html/shaktipure/logs/cron.log 2>&1

# Daily maintenance (every day at 2 AM)
0 2 * * * /usr/bin/php /var/www/html/shaktipure/cron/daily-maintenance.php >> /var/www/html/shaktipure/logs/maintenance.log 2>&1

# Log rotation (every week on Saturday)
0 3 * * 6 /usr/bin/find /var/www/html/shaktipure/logs -name "*.log" -mtime +30 -delete
```

### 5.2 Test Cron Jobs
```bash
# Test weekly matching manually
sudo -u www-data php /var/www/html/shaktipure/cron/optimized-weekly-matching.php

# Test daily maintenance manually
sudo -u www-data php /var/www/html/shaktipure/cron/daily-maintenance.php
```

## 📊 Step 6: Monitoring Setup

### 6.1 Health Check Monitoring
Set up monitoring for the health check endpoint:
```bash
# Test health check
curl https://yourdomain.com/health-check.php
```

### 6.2 Log Monitoring
```bash
# Monitor error logs
sudo tail -f /var/log/apache2/shaktipure_error.log
sudo tail -f /var/www/html/shaktipure/logs/error.log

# Monitor cron logs
sudo tail -f /var/www/html/shaktipure/logs/cron.log
```

### 6.3 Performance Monitoring
```bash
# Monitor system resources
htop
df -h
free -m

# Monitor MySQL performance
mysql -u root -p -e "SHOW PROCESSLIST;"
mysql -u root -p -e "SHOW STATUS LIKE 'Slow_queries';"
```

## 🔧 Step 7: Performance Optimization

### 7.1 PHP Configuration
```bash
sudo nano /etc/php/7.4/apache2/php.ini
```

Optimize PHP settings:
```ini
memory_limit = 256M
max_execution_time = 30
max_input_time = 60
post_max_size = 10M
upload_max_filesize = 2M
session.cookie_httponly = 1
session.cookie_secure = 1
expose_php = Off
```

### 7.2 MySQL Optimization
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

Add optimization settings:
```ini
[mysqld]
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
query_cache_type = 1
query_cache_size = 32M
max_connections = 100
```

### 7.3 Apache Optimization
```bash
sudo nano /etc/apache2/apache2.conf
```

Add performance settings:
```apache
# Enable compression
LoadModule deflate_module modules/mod_deflate.so
<Location />
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
</Location>

# Enable caching
LoadModule expires_module modules/mod_expires.so
ExpiresActive On
ExpiresByType text/css "access plus 1 month"
ExpiresByType application/javascript "access plus 1 month"
ExpiresByType image/png "access plus 1 month"
ExpiresByType image/jpg "access plus 1 month"
ExpiresByType image/jpeg "access plus 1 month"
```

## 🧪 Step 8: Testing

### 8.1 Functionality Testing
1. Test admin login and dashboard
2. Test user registration and login
3. Test franchise functionality
4. Test product management
5. Test PV transactions
6. Test payment processing
7. Test binary tree visualization
8. Test weekly matching calculation

### 8.2 Performance Testing
```bash
# Test page load times
curl -w "@curl-format.txt" -o /dev/null -s https://yourdomain.com/

# Test database performance
mysql -u mlm_user -p shaktipure_mlm_prod -e "EXPLAIN SELECT * FROM users WHERE sponsor_id = 'USR123';"
```

### 8.3 Security Testing
1. Test SSL certificate
2. Verify security headers
3. Test file upload restrictions
4. Verify database access controls
5. Test session security

## 🚨 Step 9: Backup Strategy

### 9.1 Database Backup
```bash
# Create backup script
sudo nano /usr/local/bin/backup-mlm-db.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/mlm"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="shaktipure_mlm_prod"
DB_USER="mlm_user"
DB_PASS="secure_password_here"

mkdir -p $BACKUP_DIR
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/mlm_backup_$DATE.sql.gz

# Keep only last 30 days of backups
find $BACKUP_DIR -name "mlm_backup_*.sql.gz" -mtime +30 -delete
```

```bash
# Make executable and add to cron
sudo chmod +x /usr/local/bin/backup-mlm-db.sh
sudo crontab -e
```

Add to crontab:
```bash
# Daily database backup at 1 AM
0 1 * * * /usr/local/bin/backup-mlm-db.sh
```

### 9.2 File Backup
```bash
# Create file backup script
sudo nano /usr/local/bin/backup-mlm-files.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/mlm"
DATE=$(date +%Y%m%d_%H%M%S)
SOURCE_DIR="/var/www/html/shaktipure"

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/mlm_files_$DATE.tar.gz -C /var/www/html shaktipure

# Keep only last 7 days of file backups
find $BACKUP_DIR -name "mlm_files_*.tar.gz" -mtime +7 -delete
```

## 📞 Step 10: Support and Maintenance

### 10.1 Regular Maintenance Tasks
- Monitor system resources daily
- Review error logs weekly
- Update system packages monthly
- Review security settings quarterly
- Test backup restoration quarterly

### 10.2 Emergency Procedures
1. **Database Issues**: Restore from latest backup
2. **File Corruption**: Restore files from backup
3. **Performance Issues**: Check system resources and optimize
4. **Security Breach**: Change all passwords, review logs, update system

### 10.3 Contact Information
- System Administrator: [Your Email]
- Hosting Provider: [Provider Contact]
- Domain Registrar: [Registrar Contact]
- SSL Certificate Provider: [SSL Provider Contact]

## ✅ Deployment Verification

After completing all steps, verify the deployment:

1. ✅ Website loads correctly over HTTPS
2. ✅ Admin panel is accessible and functional
3. ✅ User registration and login work
4. ✅ Database connections are secure
5. ✅ Cron jobs are running successfully
6. ✅ Health check endpoint returns healthy status
7. ✅ Error logging is working
8. ✅ Backups are being created
9. ✅ SSL certificate is valid
10. ✅ Security headers are present

## 🎉 Congratulations!

Your MLM Binary Plan System is now successfully deployed to production with enterprise-level security, performance, and monitoring capabilities.
