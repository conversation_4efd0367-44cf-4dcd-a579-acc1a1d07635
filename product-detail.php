<?php
/**
 * Product Detail Page
 * MLM Binary Plan System
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/FileUpload.php';

// Get product ID from URL
$productId = $_GET['id'] ?? null;

if (!$productId) {
    header("Location: products.php");
    exit();
}

// Get product details
$db = Database::getInstance();
$productStmt = $db->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
$productStmt->execute([$productId]);
$product = $productStmt->fetch();

if (!$product) {
    header("Location: products.php");
    exit();
}

$fileUpload = new FileUpload();

// Check if user is logged in
$isLoggedIn = isLoggedIn();
$userType = getCurrentUserType();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($product['name']); ?> - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
            border-color: #0d6efd;
        }

        .card-img-top {
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .product-card:hover .card-title {
            color: #0d6efd;
        }

        .product-card:active {
            transform: translateY(-2px);
        }

        .product-image-container img {
            transition: transform 0.3s ease;
        }

        .product-image-container:hover img {
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-leaf me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">Products</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($isLoggedIn): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $userType; ?>/dashboard.php">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="user/login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                <li class="breadcrumb-item"><a href="products.php">Products</a></li>
                <li class="breadcrumb-item active"><?php echo htmlspecialchars($product['name']); ?></li>
            </ol>
        </nav>

        <!-- Product Detail -->
        <div class="row">
            <div class="col-md-6">
                <!-- Product Image -->
                <div class="card">
                    <div class="product-image-container" style="height: 400px; overflow: hidden;">
                        <?php if ($product['image']): ?>
                            <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($product['image'])); ?>" 
                                 class="card-img-top" 
                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                 style="width: 100%; height: 100%; object-fit: cover;">
                        <?php else: ?>
                            <div class="bg-light d-flex align-items-center justify-content-center h-100">
                                <i class="fas fa-image fa-5x text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <!-- Product Information -->
                <div class="card">
                    <div class="card-body">
                        <h1 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h1>
                        <p class="text-muted mb-3">Product Code: <?php echo htmlspecialchars($product['product_code']); ?></p>
                        
                        <div class="row mb-4">
                            <div class="col-6">
                                <h3 class="text-success">₹<?php echo number_format($product['price'], 2); ?></h3>
                                <small class="text-muted">Price</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-primary"><?php echo formatPV($product['pv_value']); ?> PV</h4>
                                <small class="text-muted">Point Value</small>
                            </div>
                        </div>
                        
                        <!-- Product Details -->
                        <div class="mb-4">
                            <h5><i class="fas fa-info-circle me-2"></i>Product Information</h5>
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="border rounded p-3 mb-3">
                                        <h6 class="text-primary mb-2"><i class="fas fa-barcode me-1"></i>Product Code</h6>
                                        <p class="mb-0 fw-bold"><?php echo htmlspecialchars($product['product_code']); ?></p>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="border rounded p-3 mb-3">
                                        <h6 class="text-success mb-2"><i class="fas fa-check-circle me-1"></i>Status</h6>
                                        <p class="mb-0">
                                            <span class="badge bg-success">Available</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h5><i class="fas fa-align-left me-2"></i>Description</h5>
                            <div class="border rounded p-3 bg-light">
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                            </div>
                        </div>

                        <!-- Benefits Section -->
                        <div class="mb-4">
                            <h5><i class="fas fa-star me-2"></i>Benefits</h5>
                            <div class="border rounded p-3">
                                <ul class="list-unstyled mb-0">
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Earn <?php echo formatPV($product['pv_value']); ?> PV points with this purchase</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Contribute to your monthly PV target</li>
                                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Help build your downline PV volume</li>
                                    <li class="mb-0"><i class="fas fa-check text-success me-2"></i>Quality product from trusted source</li>
                                </ul>
                            </div>
                        </div>
                        
                        <?php if ($isLoggedIn && $userType === 'user'): ?>
                            <!-- Purchase Options for Logged-in Users -->
                            <div class="d-grid gap-2">
                                <button class="btn btn-success btn-lg" onclick="buyNow(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-shopping-cart me-2"></i>Buy Now
                                </button>
                                <button class="btn btn-outline-primary" onclick="addToCart(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-plus me-2"></i>Add to Cart
                                </button>
                            </div>
                        <?php else: ?>
                            <!-- Login Required Message -->
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Login Required</strong><br>
                                Please <a href="user/login.php" class="alert-link">login to your account</a> to purchase this product.
                            </div>
                            <div class="d-grid gap-2">
                                <a href="user/login.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login to Purchase
                                </a>
                                <a href="user/register.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        <div class="row mt-5">
            <div class="col-12">
                <h3>Related Products</h3>
                <hr>
            </div>
        </div>
        
        <?php
        // Get related products (other active products)
        $relatedStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' AND id != ? ORDER BY RAND() LIMIT 3");
        $relatedStmt->execute([$productId]);
        $relatedProducts = $relatedStmt->fetchAll();
        ?>
        
        <?php if (!empty($relatedProducts)): ?>
            <div class="row">
                <?php foreach ($relatedProducts as $relatedProduct): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card product-card h-100 shadow-sm" onclick="window.location.href='product-detail.php?id=<?php echo $relatedProduct['id']; ?>'" style="cursor: pointer;">
                            <div class="card-img-container" style="height: 200px; overflow: hidden;">
                                <?php if ($relatedProduct['image']): ?>
                                    <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($relatedProduct['image'])); ?>"
                                         class="card-img-top"
                                         alt="<?php echo htmlspecialchars($relatedProduct['name']); ?>"
                                         style="width: 100%; height: 100%; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light d-flex align-items-center justify-content-center h-100">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title"><?php echo htmlspecialchars($relatedProduct['name']); ?></h6>
                                <p class="card-text text-muted mb-3">
                                    <small><i class="fas fa-tag me-1"></i>Code: <?php echo htmlspecialchars($relatedProduct['product_code']); ?></small>
                                </p>
                                <div class="row mb-3 mt-auto">
                                    <div class="col-6">
                                        <strong class="text-success">₹<?php echo number_format($relatedProduct['price'], 2); ?></strong>
                                        <br><small class="text-muted">Price</small>
                                    </div>
                                    <div class="col-6 text-end">
                                        <span class="badge bg-primary"><?php echo formatPV($relatedProduct['pv_value']); ?> PV</span>
                                        <br><small class="text-muted">Point Value</small>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <small class="text-primary">
                                        <i class="fas fa-mouse-pointer me-1"></i>Click to view details
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 <?php echo SITE_NAME; ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>Powered by Advanced Experts</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
        
        .product-image-container img {
            transition: transform 0.3s ease;
        }
        
        .product-image-container:hover img {
            transform: scale(1.05);
        }
    </style>
    
    <script>
        function buyNow(productId) {
            // Add to cart and redirect to checkout
            window.location.href = 'user/cart.php?add=' + productId + '&checkout=1';
        }

        function addToCart(productId) {
            // Add to cart
            window.location.href = 'user/cart.php?add=' + productId;
        }
    </script>
</body>
</html>
