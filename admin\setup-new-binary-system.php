<?php
/**
 * Setup New Binary PV System
 * Creates required database tables for the new binary matching system
 */

require_once '../config/Connection.php';
require_once '../config/config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_id'])) {
    die("Access denied. Admin login required.");
}

$db = Database::getInstance();

echo "<h2>Setting up New Binary PV System</h2>";

try {
    // Create binary_reports table
    echo "<h3>Creating binary_reports table...</h3>";
    $createReportsTable = "
    CREATE TABLE IF NOT EXISTS binary_reports (
        id INT PRIMARY KEY AUTO_INCREMENT,
        report_id VARCHAR(50) UNIQUE NOT NULL,
        report_date DATE NOT NULL,
        users_processed INT NOT NULL DEFAULT 0,
        total_income_generated DECIMAL(12,2) NOT NULL DEFAULT 0.00,
        status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_report_id (report_id),
        INDEX idx_report_date (report_date),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB";
    
    $db->exec($createReportsTable);
    echo "<p style='color: green;'>✓ binary_reports table created successfully</p>";

    // Create binary_income_logs table
    echo "<h3>Creating binary_income_logs table...</h3>";
    $createIncomeLogsTable = "
    CREATE TABLE IF NOT EXISTS binary_income_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id VARCHAR(20) NOT NULL,
        report_id VARCHAR(50) NOT NULL,
        report_date DATE NOT NULL,
        left_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        right_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        matched_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        income_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (report_id) REFERENCES binary_reports(report_id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_report_id (report_id),
        INDEX idx_report_date (report_date),
        INDEX idx_income_amount (income_amount),
        INDEX idx_created_at (created_at),
        UNIQUE KEY unique_user_report (user_id, report_id)
    ) ENGINE=InnoDB";
    
    $db->exec($createIncomeLogsTable);
    echo "<p style='color: green;'>✓ binary_income_logs table created successfully</p>";

    // Update pv_transactions table to ensure processing_status column exists
    echo "<h3>Updating pv_transactions table...</h3>";
    try {
        // Check if processing_status column exists
        $checkColumn = $db->query("SHOW COLUMNS FROM pv_transactions LIKE 'processing_status'");
        if (!$checkColumn->fetch()) {
            $addColumn = "ALTER TABLE pv_transactions ADD COLUMN processing_status ENUM('pending', 'processed', 'cancelled', 'failed') DEFAULT 'pending'";
            $db->exec($addColumn);
            echo "<p style='color: green;'>✓ Added processing_status column to pv_transactions</p>";
        } else {
            echo "<p style='color: blue;'>ℹ processing_status column already exists in pv_transactions</p>";
        }

        // Check if processed_at column exists
        $checkProcessedAt = $db->query("SHOW COLUMNS FROM pv_transactions LIKE 'processed_at'");
        if (!$checkProcessedAt->fetch()) {
            $addProcessedAt = "ALTER TABLE pv_transactions ADD COLUMN processed_at TIMESTAMP NULL";
            $db->exec($addProcessedAt);
            echo "<p style='color: green;'>✓ Added processed_at column to pv_transactions</p>";
        } else {
            echo "<p style='color: blue;'>ℹ processed_at column already exists in pv_transactions</p>";
        }

    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ pv_transactions table update: " . $e->getMessage() . "</p>";
    }

    // Reset all existing PV transactions to pending status
    echo "<h3>Resetting PV transaction status...</h3>";
    $resetPVs = "UPDATE pv_transactions SET processing_status = 'pending', processed_at = NULL WHERE processing_status != 'pending'";
    $result = $db->exec($resetPVs);
    echo "<p style='color: green;'>✓ Reset $result PV transactions to pending status</p>";

    // Clean up old system tables (optional - commented out for safety)
    echo "<h3>Cleanup Options</h3>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7;'>";
    echo "<h4>⚠ Optional Cleanup (Manual)</h4>";
    echo "<p>The following tables from the old system can be cleaned up manually if desired:</p>";
    echo "<ul>";
    echo "<li><code>weekly_income_reports</code> - Old weekly reports</li>";
    echo "<li><code>weekly_income_logs</code> - Old weekly income logs</li>";
    echo "<li><code>pv_usage_tracking</code> - Old PV usage tracking</li>";
    echo "<li><code>income_logs</code> - Old daily income logs</li>";
    echo "</ul>";
    echo "<p><strong>Note:</strong> These tables are not automatically deleted to preserve historical data.</p>";
    echo "</div>";

    // Verify setup
    echo "<h3>Verification</h3>";
    
    // Check binary_reports table
    $reportsCount = $db->query("SELECT COUNT(*) as count FROM binary_reports")->fetch()['count'];
    echo "<p>✓ binary_reports table: $reportsCount records</p>";
    
    // Check binary_income_logs table
    $logsCount = $db->query("SELECT COUNT(*) as count FROM binary_income_logs")->fetch()['count'];
    echo "<p>✓ binary_income_logs table: $logsCount records</p>";
    
    // Check pending PVs
    $pendingPVs = $db->query("SELECT COUNT(*) as count FROM pv_transactions WHERE processing_status = 'pending'")->fetch()['count'];
    echo "<p>✓ Pending PV transactions: $pendingPVs records</p>";

    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb; margin-top: 20px;'>";
    echo "<h4 style='color: #155724;'>✅ Setup Complete!</h4>";
    echo "<p style='color: #155724;'>The new binary PV system has been set up successfully. You can now:</p>";
    echo "<ol style='color: #155724;'>";
    echo "<li>Go to the admin panel</li>";
    echo "<li>Use the new 'Generate Binary Report' feature</li>";
    echo "<li>All existing PVs are ready for processing</li>";
    echo "</ol>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h4 style='color: #721c24;'>❌ Setup Failed</h4>";
    echo "<p style='color: #721c24;'>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Binary PV System Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        p { margin: 10px 0; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <div style="margin-top: 30px;">
        <a href="dashboard.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">← Back to Admin Dashboard</a>
    </div>
</body>
</html>
