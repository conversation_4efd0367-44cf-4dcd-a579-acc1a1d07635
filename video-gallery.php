<?php
/**
 * Video Gallery Page
 * ShaktiPure Industries Pvt Ltd
 */

require_once 'includes/header.php';
renderHeader('Video Gallery - ShaktiPure Industries Pvt Ltd');
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="page-header-content">
            <h1>Video Gallery</h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Video Gallery</span>
            </nav>
        </div>
    </div>
</section>

<!-- Video Gallery Content -->
<section class="video-gallery-section">
    <div class="container">
        <div class="gallery-intro">
            <h2>Explore Our Video Collection</h2>
            <p>Watch our comprehensive collection of product demonstrations, installation guides, customer testimonials, and company insights. Learn more about our water purification technology and see our products in action.</p>
        </div>

        <!-- Featured Video -->
        <div class="featured-video">
            <h3>Featured Video</h3>
            <div class="video-container">
                <iframe src="https://www.youtube.com/embed/tHXnjnELK1I" frameborder="0" style="width: 100%; height: 315px;"></iframe>
            </div>
        </div>

        <!-- Video Categories -->
        <div class="video-categories">
            <!-- Product Demonstrations -->
            <div class="video-category">
                <h3>Product Demonstrations</h3>
                <div class="videos-grid">
                    <div class="video-item">
                        <iframe src="https://www.youtube.com/embed/pnTDNmcUQOU" frameborder="0" style="width: 100%; height: 315px;"></iframe>
                    </div>

                    <div class="video-item">
                        <iframe src="https://www.youtube.com/embed/fiZ35Yzkqq4" frameborder="0" style="width: 100%; height: 315px;"></iframe>
                    </div>

                    <div class="video-item">
                        <iframe src="https://www.youtube.com/embed/KsMDKtis-K4" frameborder="0" style="width: 100%; height: 315px;"></iframe>
                    </div>
                </div>
            </div>

            <!-- Installation Guides -->
            <!-- <div class="video-category">
                <h3>Installation Guides</h3>
            
            </div> -->

            <!-- Customer Testimonials -->
            <div class="video-category">
                <h3>Customer Testimonials</h3>
                <div class="videos-grid">
                  <iframe src="https://www.youtube.com/embed/-PY1qUGXIOQ" frameborder="0" style="width: 100%; height: 315px;"></iframe>

                <iframe src="https://www.youtube.com/embed/O8gOHE7DhBE" frameborder="0" style="width: 100%; height: 315px;"></iframe>
                </div>
            </div>
        </div>

        <!-- Video Notice -->
        <div class="video-notice">
            <div class="notice-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="notice-content">
                <h4>Video Streaming Notice</h4>
                <p>Our video content is optimized for all devices. For the best viewing experience, we recommend a stable internet connection. All videos are available in HD quality.</p>
            </div>
        </div>
    </div>
</section>

<?php renderFooter(); ?>

<style>
/* Page Header */
.page-header {
    background: var(--gradient-primary);
    padding: 60px 0;
    color: white;
}

.page-header-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

/* Video Gallery Section */
.video-gallery-section {
    padding: 80px 0;
}

.gallery-intro {
    text-align: center;
    margin-bottom: 60px;
}

.gallery-intro h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 20px;
}

.gallery-intro p {
    font-size: 16px;
    color: var(--text-gray-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Featured Video */
.featured-video {
    margin-bottom: 80px;
}

.featured-video h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 30px;
    text-align: center;
}

.video-container {
    max-width: 800px;
    margin: 0 auto;
}

.video-placeholder {
    background: var(--gradient-card);
    border-radius: var(--card-border-radius);
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.video-placeholder:hover {
    transform: translateY(-5px);
}

.video-icon {
    font-size: 4rem;
    color: var(--primary-green);
    margin-bottom: 20px;
}

.video-info h4 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.video-info p {
    color: var(--text-gray-light);
    line-height: 1.6;
}

/* Video Categories */
.video-categories {
    margin-bottom: 60px;
}

.video-category {
    margin-bottom: 60px;
}

.video-category h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-green);
}

.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.video-item {
    background: white;
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.video-item:hover {
    transform: translateY(-5px);
}

.video-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-item:hover .play-button {
    background: var(--primary-green);
    transform: translate(-50%, -50%) scale(1.1);
}

.video-details {
    padding: 20px;
}

.video-details h4 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.video-details p {
    color: var(--text-gray-light);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.video-duration {
    background: var(--primary-green);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Video Notice */
.video-notice {
    background: var(--background-light);
    border-radius: var(--card-border-radius);
    padding: 30px;
    display: flex;
    align-items: flex-start;
    gap: 20px;
    border-left: 4px solid var(--secondary-blue);
}

.notice-icon {
    font-size: 2rem;
    color: var(--secondary-blue);
    margin-top: 5px;
}

.notice-content h4 {
    color: var(--text-dark);
    margin-bottom: 10px;
}

.notice-content p {
    color: var(--text-gray-light);
    line-height: 1.6;
    margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .videos-grid {
        grid-template-columns: 1fr;
    }
    
    .video-placeholder {
        padding: 40px 20px;
    }
    
    .video-notice {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<script>
// Video item click handlers
document.addEventListener('DOMContentLoaded', function() {
    const videoItems = document.querySelectorAll('.video-item, .video-placeholder');
    
    videoItems.forEach(item => {
        item.addEventListener('click', function() {
            alert('Video player will be integrated soon! This will open the selected video.');
        });
    });
});
</script>
