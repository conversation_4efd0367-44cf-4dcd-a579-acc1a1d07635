<?php
/**
 * Weekly Income Reports - Admin Panel
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/ReportLogger.php';
require_once '../config/config.php';
require_once '../includes/WeeklyDateHelper.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$pvSystem = new PVSystem();
$config = Config::getInstance();

// Get database instance
$db = Database::getInstance();

// Handle actions
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$message = '';
$messageType = '';

if ($action === 'generate_report') {
    // Debug logging
    error_log("Report generation requested - Action: {$action}");
    error_log("POST data: " . print_r($_POST, true));

    $weekStart = $_POST['week_start'] ?? '';
    $weekEnd = $_POST['week_end'] ?? '';

    error_log("Week start: {$weekStart}, Week end: {$weekEnd}");

    if ($weekStart && $weekEnd) {
        try {
            // Initialize logger
            $logger = new ReportLogger('weekly_report_generation');
            $logger->info("Starting weekly report generation", [
                'week_start' => $weekStart,
                'week_end' => $weekEnd,
                'admin_id' => $adminId
            ]);

            // Validate dates
            $startDate = new DateTime($weekStart);
            $endDate = new DateTime($weekEnd);

            if ($startDate > $endDate) {
                throw new Exception("Start date cannot be after end date.");
            }

            // Check if report already exists
            $existingReportStmt = $db->prepare("SELECT id, total_users_earned, total_income_distributed FROM weekly_income_reports WHERE week_start_date = ?");
            $existingReportStmt->execute([$weekStart]);
            $existingReport = $existingReportStmt->fetch();

            $incrementalProcessing = false;
            if ($existingReport) {
                $logger->warning("Report already exists for this week", ['existing_report_id' => $existingReport['id']]);
                $incrementalProcessing = true;
            }

            // Start timing and processing
            $logger->startTimer('report_generation');

            // Start output buffering to capture any echo statements
            ob_start();

            // Debug: Log before calling runWeeklyMatching
            error_log("Calling runWeeklyMatching with: weekStart={$weekStart}, weekEnd={$weekEnd}, incremental={$incrementalProcessing}");

            $result = $pvSystem->runWeeklyMatching($weekStart, $weekEnd, $incrementalProcessing);
            $output = ob_get_clean();

            // Debug: Log the result
            error_log("runWeeklyMatching result: " . print_r($result, true));
            if ($output) {
                error_log("runWeeklyMatching output: " . $output);
            }

            $logger->endTimer('report_generation');

            // Log the result
            $logger->logReportSummary($weekStart, $weekEnd, $result);

            if ($result !== false && is_array($result)) {
                if ($incrementalProcessing) {
                    $message = "Incremental report processing completed! Processed remaining PVs for {$result['processed']} users, distributed additional ₹" . number_format($result['total_income'], 2) . " (net after deductions)";
                    if ($result['users_with_income'] > 0) {
                        $message .= " to {$result['users_with_income']} users.";
                    }
                    if (isset($result['skipped_users']) && $result['skipped_users'] > 0) {
                        $message .= " <br><small class='text-info'><i class='fas fa-info-circle'></i> {$result['skipped_users']} users were already processed and skipped.</small>";
                    }
                } else {
                    $message = "Weekly report generated successfully! Processed {$result['processed']} users, distributed ₹" . number_format($result['total_income'], 2) . " (net after deductions)";
                    if ($result['users_with_income'] > 0) {
                        $message .= " to {$result['users_with_income']} users.";
                    }
                }

                // Add error information if any
                if (isset($result['error_count']) && $result['error_count'] > 0) {
                    $message .= " <br><small class='text-warning'><i class='fas fa-exclamation-triangle'></i> {$result['error_count']} users had processing errors.</small>";
                    $messageType = 'warning';
                } else {
                    $messageType = 'success';
                }
            } else {
                $message = "Failed to generate weekly report. Please check the system logs for details.";
                $messageType = 'danger';
                $logger->error("Weekly report generation failed", ['week_start' => $weekStart, 'week_end' => $weekEnd]);
            }

        } catch (Exception $e) {
            $message = "Error generating report: " . $e->getMessage();
            $messageType = 'danger';

            if (isset($logger)) {
                $logger->error("Weekly report generation exception", [
                    'error_message' => $e->getMessage(),
                    'error_trace' => $e->getTraceAsString(),
                    'week_start' => $weekStart,
                    'week_end' => $weekEnd
                ]);
            } else {
                error_log("Weekly report generation error: " . $e->getMessage());
            }
        }
    } else {
        $message = "Please provide both start and end dates.";
        $messageType = 'warning';
    }
}

// Get weekly reports
$reportsStmt = $db->query("
    SELECT * FROM weekly_income_reports 
    ORDER BY week_start_date DESC 
    LIMIT 20
");
$reports = $reportsStmt->fetchAll();

// Get current week dates (Saturday-Friday weeks)
$currentWeek = WeeklyDateHelper::getCurrentWeek();
$currentWeekStart = $currentWeek['start'];
$currentWeekEnd = $currentWeek['end'];
$lastWeek = WeeklyDateHelper::getPreviousWeek();
$lastWeekStart = $lastWeek['start'];
$lastWeekEnd = $lastWeek['end'];

// Debug: Log the week dates
error_log("Current week: {$currentWeekStart} to {$currentWeekEnd}");
error_log("Last week: {$lastWeekStart} to {$lastWeekEnd}");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Income Reports - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-calendar-week me-2"></i>Weekly Income Reports</h2>
                    <div>
                        <a href="fix-pv-tracking.php" class="btn btn-warning me-2" target="_blank">
                            <i class="fas fa-wrench me-1"></i>Fix PV Tracking
                        </a>
                        <a href="simple-income-diagnostic.php" class="btn btn-info me-2" target="_blank">
                            <i class="fas fa-stethoscope me-1"></i>Diagnose Issues
                        </a>
                        <a href="test-incremental-processing.php" class="btn btn-secondary me-2" target="_blank">
                            <i class="fas fa-flask me-1"></i>Test Incremental
                        </a>
                        <a href="payment-management.php" class="btn btn-success me-2">
                            <i class="fas fa-credit-card me-1"></i>Manage Payments
                        </a>
                        <a href="simple-report-generator.php" class="btn btn-success me-2">
                            <i class="fas fa-plus me-1"></i>Click Here to Genreate Report
                        </a>
                        <!-- <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#generateReportModal">
                            <i class="fas fa-plus me-1"></i>Generate Report
                        </button> -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Total Reports</h6>
                                        <h3><?php echo count($reports); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-file-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Weekly Cap</h6>
                                        <h3>₹<?php echo number_format($config->get('weekly_capping', 130000), 0); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-hand-holding-usd fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">PV Rate</h6>
                                        <h3>₹<?php echo $config->getPVRate(); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-coins fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Processing Day</h6>
                                        <h3><?php 
                                            $days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                                            echo $days[$config->get('weekly_processing_day', 0)]; 
                                        ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Weekly Income Reports</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($reports)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Reports Generated Yet</h5>
                                <p class="text-muted">Generate your first weekly income report using the button above.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Week Period</th>
                                            <th>Report #</th>
                                            <th>Users Processed</th>
                                            <th>Users Earned</th>
                                            <th>Users Skipped</th>
                                            <th>Gross Income</th>
                                            <th>Net Income</th>
                                            <th>Status</th>
                                            <th>Generated</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reports as $report): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo date('M d', strtotime($report['week_start_date'])); ?> - <?php echo date('M d, Y', strtotime($report['week_end_date'])); ?></strong>
                                                    <?php if (!empty($report['report_id'])): ?>
                                                        <br><small class="text-muted">ID: <?php echo $report['report_id']; ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">#<?php echo $report['report_number'] ?? 1; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo $report['total_users_processed'] ?? $report['total_users_earned']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success"><?php echo $report['total_users_earned']; ?></span>
                                                </td>
                                                <td>
                                                    <?php if (isset($report['total_users_skipped']) && $report['total_users_skipped'] > 0): ?>
                                                        <span class="badge bg-warning"><?php echo $report['total_users_skipped']; ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">0</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong class="text-primary">₹<?php echo number_format($report['total_gross_income'] ?? $report['total_income_distributed'], 2); ?></strong>
                                                    <?php
                                                    $totalDeductions = ($report['total_service_charge'] ?? 0) + ($report['total_tds_amount'] ?? 0);
                                                    if ($totalDeductions > 0): ?>
                                                        <br><small class="text-danger">-₹<?php echo number_format($totalDeductions, 2); ?> deductions</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong class="text-success">₹<?php echo number_format($report['total_income_distributed'], 2); ?></strong>
                                                    <?php if ($report['total_capping_applied'] > 0): ?>
                                                        <br><small class="text-warning">₹<?php echo number_format($report['total_capping_applied'], 2); ?> capped</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = [
                                                        'generated' => 'bg-warning',
                                                        'sent' => 'bg-success',
                                                        'failed' => 'bg-danger'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $statusClass[$report['report_status']] ?? 'bg-secondary'; ?>">
                                                        <?php echo ucfirst($report['report_status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo date('M d, Y H:i', strtotime($report['report_generated_at'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="weekly-income-details.php?week=<?php echo $report['week_start_date']; ?>"
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> View Details
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Generate Report Modal -->
    <div class="modal fade" id="generateReportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>" id="generateReportForm">
                    <div class="modal-header">
                        <h5 class="modal-title">Generate Weekly Income Report</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="generate_report">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="week_start" class="form-label">Week Start Date</label>
                                <input type="date" class="form-control" id="week_start" name="week_start"
                                       value="<?php echo $lastWeekStart; ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="week_end" class="form-label">Week End Date</label>
                                <input type="date" class="form-control" id="week_end" name="week_end"
                                       value="<?php echo $lastWeekEnd; ?>" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Generate income report for the specified week. This will process PV matching for all users.
                            </small>
                        </div>
                        <div class="mt-3" id="progressContainer" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%">
                                    Processing...
                                </div>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                <i class="fas fa-spinner fa-spin"></i>
                                Generating report, please wait...
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelBtn">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="generateBtn">
                            <i class="fas fa-play me-1"></i>Generate Report
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('generateReportForm');
            const generateBtn = document.getElementById('generateBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            const progressContainer = document.getElementById('progressContainer');
            const weekStartInput = document.getElementById('week_start');
            const weekEndInput = document.getElementById('week_end');

            // Auto-calculate week end date when start date changes
            weekStartInput.addEventListener('change', function() {
                if (this.value) {
                    const startDate = new Date(this.value);
                    const endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 6);

                    const year = endDate.getFullYear();
                    const month = String(endDate.getMonth() + 1).padStart(2, '0');
                    const day = String(endDate.getDate()).padStart(2, '0');

                    weekEndInput.value = `${year}-${month}-${day}`;
                }
            });

            // Single form submit handler with validation and progress
            form.addEventListener('submit', function(e) {
                console.log('Form submit triggered');

                // Validate dates first
                const startDate = new Date(weekStartInput.value);
                const endDate = new Date(weekEndInput.value);

                if (startDate > endDate) {
                    e.preventDefault();
                    alert('Start date cannot be after end date.');
                    return false;
                }

                // Validate required fields
                if (!weekStartInput.value || !weekEndInput.value) {
                    e.preventDefault();
                    alert('Please select both start and end dates.');
                    return false;
                }

                console.log('Validation passed, showing progress');

                // Show progress and disable form
                progressContainer.style.display = 'block';
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
                cancelBtn.disabled = true;

                // Disable form inputs
                const inputs = form.querySelectorAll('input, select');
                inputs.forEach(input => input.disabled = true);

                // Allow form to submit
                return true;
            });

            // Debug: Log form data on button click
            generateBtn.addEventListener('click', function() {
                console.log('Generate button clicked');
                console.log('Week start:', weekStartInput.value);
                console.log('Week end:', weekEndInput.value);
            });
        });
    </script>
</body>
</html>
