<?php
/**
 * Common Header File
 * ShaktiPure Industries Website
 * Include this file at the top of every page
 */

// Start output buffering
ob_start();

// Include configuration and database
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/config/Connection.php';
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/includes/session.php';
require_once dirname(__DIR__) . '/includes/functions.php';
require_once dirname(__DIR__) . '/includes/FileUpload.php';
require_once dirname(__DIR__) . '/includes/Validator.php';
require_once dirname(__DIR__) . '/includes/Response.php';
require_once dirname(__DIR__) . '/includes/Auth.php';
require_once dirname(__DIR__) . '/includes/Wallet.php';

// Set error reporting based on environment
if (!defined('ENVIRONMENT') || ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// CSRF token generation for forms
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

/**
 * Get base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path;
}

/**
 * Get current URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Include CSS file
 */
function includeCSS($file) {
    $baseUrl = getBaseUrl();
    echo "<link rel='stylesheet' href='{$baseUrl}/assets/css/{$file}'>";
}

/**
 * Include JS file
 */
function includeJS($file) {
    $baseUrl = getBaseUrl();
    echo "<script src='{$baseUrl}/assets/js/{$file}'></script>";
}

/**
 * Display flash messages
 */
function displayFlashMessages() {
    $messages = getFlashMessages();
    if (!empty($messages)) {
        foreach ($messages as $message) {
            $alertClass = '';
            switch ($message['type']) {
                case 'success':
                    $alertClass = 'alert-success';
                    break;
                case 'error':
                    $alertClass = 'alert-danger';
                    break;
                case 'warning':
                    $alertClass = 'alert-warning';
                    break;
                case 'info':
                default:
                    $alertClass = 'alert-info';
                    break;
            }
            
            echo "<div class='alert {$alertClass} alert-dismissible fade show' role='alert'>";
            echo htmlspecialchars($message['message']);
            echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
            echo "</div>";
        }
    }
}

/**
 * Check if current page is active
 */
function isActivePage($page) {
    $currentPage = basename($_SERVER['PHP_SELF']);
    return $currentPage === $page ? 'active' : '';
}

/**
 * Generate CSRF token input
 */
function csrfTokenInput() {
    return "<input type='hidden' name='csrf_token' value='" . $_SESSION['csrf_token'] . "'>";
}

/**
 * Verify CSRF token
 */
function verifyCsrfToken() {
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        die('CSRF token mismatch');
    }
}

/**
 * Render common header HTML
 */
function renderHeader($pageTitle = 'ShaktiPure Industries Pvt Ltd', $showCarousel = false) {
    $baseUrl = getBaseUrl();
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="Shaktipure Industries Pvt Ltd is a leading manufacturer and exporter of high-quality water purifiers, water treatment plants, and water testing equipment.">
        <title><?php echo htmlspecialchars($pageTitle); ?></title>
        <link rel="icon" type="image/png" href="<?php echo $baseUrl; ?>/assets/images/onlylogo.png">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
        <link href="<?php echo $baseUrl; ?>/assets/css/homepage.css" rel="stylesheet">
    </head>
    <body>
        <!-- Top Header -->
        <div class="top-header">
            <div class="container">
                <div class="top-header-left">
                    🔥 Welcome to Shaktipure Industries PVT. LTD.
                </div>
                <div class="top-header-right">
                    <a href="tel:+91-8460203679"><i class="fas fa-phone"></i> +91-8460203679</a>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <header class="main-header">
            <div class="container">
                <div class="header-content">
                    <a href="<?php echo $baseUrl; ?>/index.php">
                        <img src="<?php echo $baseUrl; ?>/assets/images/logo.png" alt="ShaktiPure" style="height: 40px; margin-right: 10px;" id="logo">
                    </a>
                    <style>
                        @media (max-width: 770px) {
  #logo {
    display: none;
  }
}

@media (min-width: 770px) {
  #logo2 {
    display: none;
  }
}
                    </style>
                    <div class="search-bar">
                        <form action="<?php echo $baseUrl; ?>/products.php" method="GET">
                            <input type="text" name="search" placeholder="Search for products, brands and more...">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>

                    <div class="header-actions">
                        <img src="<?php echo $baseUrl; ?>/assets/images/logo.png" alt="ShaktiPure" style="height: 40px; margin-right: 10px;" id="logo2">
                        <a href="<?php echo $baseUrl; ?>/user/login.php" class="header-btn">
                            <i class="fas fa-user"></i>
                            <span>Account</span>
                        </a>
                        <a href="#" class="header-btn">
                            <i class="fas fa-heart"></i>
                            <span>Wishlist</span>
                        </a>
                        <a href="#" class="header-btn">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Cart</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navigation">
            <div class="container">
                <div class="nav-content">
                    <button class="categories-btn">
                        <i class="fas fa-bars"></i>
                        PRODUCT CATEGORIES
                    </button>
                    <ul class="nav-menu">
                        <li><a href="<?php echo $baseUrl; ?>/index.php" class="<?php echo isActivePage('index.php'); ?>">Home</a></li>
                        <li><a href="<?php echo $baseUrl; ?>/index.php#categories">Categories</a></li>
                        <li><a href="<?php echo $baseUrl; ?>/products.php">Products</a></li>
                        <li><a href="<?php echo $baseUrl; ?>/about.php">About</a></li>
                        <li><a href="<?php echo $baseUrl; ?>/contact.php">Contact</a></li>
                        <li><a href="<?php echo $baseUrl; ?>/user/register.php">Register</a></li>
                        <li><a href="<?php echo $baseUrl; ?>/user/login.php">Login</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    <?php
}

/**
 * Render common footer HTML
 */
function renderFooter() {
    $baseUrl = getBaseUrl();
    ?>
        <!-- Footer - RootPure Style -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3>ShaktiPure Industries</h3>
                        <p>Welcome to ShaktiPure Industries Pvt. Ltd. We would like to give you an excellent insight about our company and its capabilities. Our overall business attitude is shaped by our overall value care.</p>
                        <div class="footer-contact">
                            <p><strong>Address:</strong> D-224, Udhana Complex, Udhana, Surat-394210, Gujarat, India</p>
                            <p><strong>Phone:</strong> +91-8460203679</p>
                            <p><strong>Email:</strong> <EMAIL></p>
                        </div>
                    </div>
                    <div class="footer-section">
                        <h3>Information</h3>
                        <ul>
                            <li><a href="<?php echo $baseUrl; ?>/management.php">Management</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/about.php">About Us</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/privacy-policy.php">Privacy Policy</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/terms-conditions.php">Terms & Conditions</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/downloads.php">Downloads</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/bankers.php">Our bankers</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>My Account</h3>
                        <ul>
                            <li><a href="<?php echo $baseUrl; ?>/video-gallery.php">Video Gallery</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/branches.php">Branches</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/stockist.php">Stockist</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/shipping-policy.php">Shipping Policy</a></li>
                            <li><a href="<?php echo $baseUrl; ?>/refund-policy.php">Refund Policy</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>Follow Us</h3>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-facebook"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                            <a href="#"><i class="fab fa-youtube"></i></a>
                            <a href="#"><i class="fab fa-linkedin"></i></a>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2025 ShaktiPure Industries Pvt. Ltd. All Rights Reserved</p>
                </div>
            </div>
        </footer>

        <!-- Common JavaScript -->
        <script>
            // Categories button functionality
            document.querySelector('.categories-btn').addEventListener('click', function() {
                alert('Product categories feature coming soon!');
            });

            // Add to cart functionality
            document.querySelectorAll('.btn-add-cart').forEach(button => {
                button.addEventListener('click', function() {
                    alert('Add to cart functionality will be implemented soon!');
                });
            });

            // Wishlist functionality
            document.querySelectorAll('.btn-wishlist').forEach(button => {
                button.addEventListener('click', function() {
                    this.style.color = this.style.color === 'red' ? '' : 'red';
                });
            });

            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        </script>
    </body>
    </html>
    <?php
}
?>
