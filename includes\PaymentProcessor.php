<?php
/**
 * Payment Processor for Weekly Income System
 * Handles automatic payment processing for weekly income logs
 */

class PaymentProcessor {
    private $db;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
    }
    
    /**
     * Process payments for a specific week
     */
    public function processWeeklyPayments($weekStartDate, $weekEndDate = null) {
        try {
            if (!$weekEndDate) {
                $weekEndDate = date('Y-m-d', strtotime($weekStartDate . ' +6 days'));
            }
            
            // Check if auto payment is enabled
            if (!$this->config->get('auto_payment_enabled', false)) {
                throw new Exception("Automatic payment processing is disabled");
            }
            
            $minThreshold = $this->config->get('auto_payment_threshold', 500);
            $batchSize = $this->config->get('payment_batch_size', 100);
            
            // Create payment batch
            $batchId = $this->createPaymentBatch($weekStartDate, $weekEndDate);
            
            // Get pending income logs for payment
            $stmt = $this->db->prepare("
                SELECT wil.*, u.full_name, u.email, u.phone 
                FROM weekly_income_logs wil
                JOIN users u ON wil.user_id = u.user_id
                WHERE wil.week_start_date = ? 
                AND wil.income_amount >= ?
                AND (wil.payment_status IS NULL OR wil.payment_status = 'pending')
                ORDER BY wil.income_amount DESC
                LIMIT ?
            ");
            
            $stmt->execute([$weekStartDate, $minThreshold, $batchSize]);
            $pendingPayments = $stmt->fetchAll();
            
            $processedCount = 0;
            $successfulCount = 0;
            $failedCount = 0;
            $totalAmount = 0;
            
            foreach ($pendingPayments as $payment) {
                $result = $this->processIndividualPayment($payment, $batchId);
                $processedCount++;
                
                if ($result['success']) {
                    $successfulCount++;
                    $totalAmount += $payment['income_amount'];
                } else {
                    $failedCount++;
                }
            }
            
            // Update batch status
            $this->updatePaymentBatch($batchId, $processedCount, $successfulCount, $failedCount, $totalAmount);
            
            return [
                'batch_id' => $batchId,
                'processed' => $processedCount,
                'successful' => $successfulCount,
                'failed' => $failedCount,
                'total_amount' => $totalAmount
            ];
            
        } catch (Exception $e) {
            error_log("Payment processing error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new payment batch
     */
    private function createPaymentBatch($weekStartDate, $weekEndDate) {
        $batchId = 'BATCH_' . date('YmdHis') . '_' . uniqid();
        
        $stmt = $this->db->prepare("
            INSERT INTO payment_batches 
            (batch_id, week_start_date, week_end_date, batch_status, created_at)
            VALUES (?, ?, ?, 'processing', NOW())
        ");
        
        $stmt->execute([$batchId, $weekStartDate, $weekEndDate]);
        
        return $batchId;
    }
    
    /**
     * Process individual payment
     */
    private function processIndividualPayment($payment, $batchId) {
        try {
            // Generate transaction ID
            $transactionId = 'TXN_' . date('YmdHis') . '_' . $payment['user_id'];
            
            // Update payment status to processing
            $this->updatePaymentStatus($payment['id'], 'processing');
            
            // Simulate payment processing (replace with actual payment gateway integration)
            $paymentResult = $this->simulatePaymentGateway($payment);
            
            if ($paymentResult['success']) {
                // Create successful transaction record
                $this->createPaymentTransaction(
                    $transactionId,
                    $batchId,
                    $payment['id'],
                    $payment['user_id'],
                    $payment['income_amount'],
                    'completed',
                    $paymentResult['reference']
                );
                
                // Update payment status to paid
                $this->updatePaymentStatus(
                    $payment['id'], 
                    'paid', 
                    $paymentResult['reference'],
                    'bank_transfer'
                );
                
                return ['success' => true, 'transaction_id' => $transactionId];
                
            } else {
                // Create failed transaction record
                $this->createPaymentTransaction(
                    $transactionId,
                    $batchId,
                    $payment['id'],
                    $payment['user_id'],
                    $payment['income_amount'],
                    'failed',
                    null,
                    $paymentResult['error']
                );
                
                // Update payment status to failed
                $this->updatePaymentStatus($payment['id'], 'failed');
                
                return ['success' => false, 'error' => $paymentResult['error']];
            }
            
        } catch (Exception $e) {
            error_log("Individual payment processing error: " . $e->getMessage());
            $this->updatePaymentStatus($payment['id'], 'failed');
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Simulate payment gateway (replace with actual implementation)
     */
    private function simulatePaymentGateway($payment) {
        // Simulate 95% success rate
        $success = (rand(1, 100) <= 95);
        
        if ($success) {
            return [
                'success' => true,
                'reference' => 'REF_' . date('YmdHis') . '_' . rand(1000, 9999)
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Simulated payment gateway failure'
            ];
        }
    }
    
    /**
     * Update payment status in weekly_income_logs
     */
    private function updatePaymentStatus($logId, $status, $reference = null, $method = null) {
        $sql = "UPDATE weekly_income_logs SET payment_status = ?";
        $params = [$status];
        
        if ($status === 'paid') {
            $sql .= ", payment_date = NOW()";
        }
        
        if ($reference) {
            $sql .= ", payment_reference = ?";
            $params[] = $reference;
        }
        
        if ($method) {
            $sql .= ", payment_method = ?";
            $params[] = $method;
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $logId;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
    }
    
    /**
     * Create payment transaction record
     */
    private function createPaymentTransaction($transactionId, $batchId, $incomeLogId, $userId, $amount, $status, $reference = null, $failureReason = null) {
        $stmt = $this->db->prepare("
            INSERT INTO payment_transactions 
            (transaction_id, batch_id, income_log_id, user_id, amount, payment_status, payment_reference, failure_reason, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $transactionId,
            $batchId,
            $incomeLogId,
            $userId,
            $amount,
            $status,
            $reference,
            $failureReason
        ]);
    }
    
    /**
     * Update payment batch with final statistics
     */
    private function updatePaymentBatch($batchId, $totalPayments, $successful, $failed, $totalAmount) {
        $status = ($failed > 0) ? 'completed' : 'completed';
        
        $stmt = $this->db->prepare("
            UPDATE payment_batches 
            SET total_payments = ?, successful_payments = ?, failed_payments = ?, 
                total_amount = ?, batch_status = ?, completed_at = NOW()
            WHERE batch_id = ?
        ");
        
        $stmt->execute([
            $totalPayments,
            $successful,
            $failed,
            $totalAmount,
            $status,
            $batchId
        ]);
    }
    
    /**
     * Get payment statistics for a week
     */
    public function getWeeklyPaymentStats($weekStartDate) {
        $stmt = $this->db->prepare("
            SELECT
                COUNT(*) as total_logs,
                COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_count,
                COUNT(CASE WHEN payment_status = 'pending' OR payment_status IS NULL THEN 1 END) as pending_count,
                COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_count,
                COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN income_amount ELSE 0 END), 0) as paid_amount,
                COALESCE(SUM(CASE WHEN payment_status = 'pending' OR payment_status IS NULL THEN income_amount ELSE 0 END), 0) as pending_amount
            FROM weekly_income_logs
            WHERE week_start_date = ? AND income_amount > 0
        ");

        $stmt->execute([$weekStartDate]);
        return $stmt->fetch();
    }
}
?>
