<?php
/**
 * Binary PV Reports - Admin Panel
 * New MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/NewBinaryPVSystem.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$binaryPVSystem = new NewBinaryPVSystem();
$config = Config::getInstance();

// Get database instance
$db = Database::getInstance();

// Handle actions
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$message = '';
$messageType = '';

if ($action === 'generate_report') {
    try {
        $reportDate = $_POST['report_date'] ?? date('Y-m-d');
        
        // Validate date
        $dateObj = new DateTime($reportDate);
        if ($dateObj > new DateTime()) {
            throw new Exception("Cannot generate report for future dates.");
        }
        
        // Check if report already exists for this date
        $existingReportStmt = $db->prepare("SELECT report_id, users_processed, total_income_generated FROM binary_reports WHERE report_date = ?");
        $existingReportStmt->execute([$reportDate]);
        $existingReport = $existingReportStmt->fetch();
        
        if ($existingReport) {
            $message = "Report already exists for " . date('M d, Y', strtotime($reportDate)) . " (Report ID: {$existingReport['report_id']}). Processed {$existingReport['users_processed']} users with total income of ₹" . number_format($existingReport['total_income_generated'], 2);
            $messageType = 'warning';
        } else {
            // Generate new report
            $result = $binaryPVSystem->generateReport($reportDate);
            
            if ($result['success']) {
                $message = "Binary report generated successfully! Report ID: {$result['report_id']}. Processed {$result['users_processed']} users and distributed ₹" . number_format($result['total_income'], 2) . " in total income.";
                $messageType = 'success';
            } else {
                $message = "Report generation failed: " . $result['error'];
                $messageType = 'danger';
            }
        }
        
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// Get recent reports
$recentReports = $binaryPVSystem->getReportHistory(10);

// Get system statistics
$totalPendingPV = $db->query("SELECT COUNT(*) as count, SUM(pv_amount) as total FROM pv_transactions WHERE processing_status = 'pending'")->fetch();
$totalUsers = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'")->fetch()['count'];
$pvRate = $config->getPVRate();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary PV Reports - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Binary PV Reports</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#generateReportModal">
                                <i class="fas fa-plus"></i> Generate Report
                            </button>
                        </div>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- System Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title">Active Users</h5>
                                        <h3><?php echo number_format($totalUsers); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title">Pending PVs</h5>
                                        <h3><?php echo number_format($totalPendingPV['count']); ?></h3>
                                        <small>Total: <?php echo number_format($totalPendingPV['total'], 2); ?> PV</small>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title">PV Rate</h5>
                                        <h3>₹<?php echo number_format($pvRate, 2); ?></h3>
                                        <small>Per PV</small>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-rupee-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title">Potential Income</h5>
                                        <h3>₹<?php echo number_format($totalPendingPV['total'] * $pvRate, 2); ?></h3>
                                        <small>If all PVs matched</small>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calculator fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Recent Binary Reports</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentReports)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Reports Generated Yet</h5>
                                <p class="text-muted">Generate your first binary PV report using the button above.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Report ID</th>
                                            <th>Date</th>
                                            <th>Users Processed</th>
                                            <th>Total Income</th>
                                            <th>Status</th>
                                            <th>Generated</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentReports as $report): ?>
                                            <tr>
                                                <td>
                                                    <code><?php echo htmlspecialchars($report['report_id']); ?></code>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($report['report_date'])); ?></td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo number_format($report['users_processed']); ?></span>
                                                </td>
                                                <td>
                                                    <strong>₹<?php echo number_format($report['total_income_generated'], 2); ?></strong>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = [
                                                        'completed' => 'success',
                                                        'processing' => 'warning',
                                                        'failed' => 'danger'
                                                    ];
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass[$report['status']] ?? 'secondary'; ?>">
                                                        <?php echo ucfirst($report['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo date('M d, Y H:i', strtotime($report['created_at'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="binary-report-details.php?report_id=<?php echo urlencode($report['report_id']); ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> View Details
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Generate Report Modal -->
    <div class="modal fade" id="generateReportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Generate Binary PV Report</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="generate_report">
                        
                        <div class="mb-3">
                            <label for="report_date" class="form-label">Report Date</label>
                            <input type="date" class="form-control" id="report_date" name="report_date" 
                                   value="<?php echo date('Y-m-d'); ?>" max="<?php echo date('Y-m-d'); ?>" required>
                            <div class="form-text">Select the date for which to generate the binary matching report.</div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> How Binary Matching Works:</h6>
                            <ul class="mb-0">
                                <li>System calculates left and right leg PV for each user</li>
                                <li>Income = MIN(Left PV, Right PV) × PV Rate (₹<?php echo number_format($pvRate, 2); ?>)</li>
                                <li>Used PVs are marked as processed to prevent double-counting</li>
                                <li>Income is credited directly to user wallets</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-cogs"></i> Generate Report
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
