<?php
/**
 * Check Database Structure
 * Verify that all required tables and columns exist
 */

require_once '../config/Connection.php';

echo "<h2>🔍 Database Structure Check</h2>\n";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .check-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
    .check-header { background-color: #f0f0f0; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 5px 5px 0 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

try {
    $db = Database::getInstance();
    
    echo "<div class='check-section'>";
    echo "<div class='check-header'><h3>📋 Weekly Income Reports Table</h3></div>";
    
    // Check if weekly_income_reports table exists
    $tableCheck = $db->query("SHOW TABLES LIKE 'weekly_income_reports'");
    if ($tableCheck->rowCount() > 0) {
        echo "<p class='success'>✅ weekly_income_reports table exists</p>";
        
        // Get table structure
        $columns = $db->query("DESCRIBE weekly_income_reports");
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($column = $columns->fetch()) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for required columns
        $requiredColumns = ['report_id', 'report_number'];
        foreach ($requiredColumns as $col) {
            $colCheck = $db->query("SHOW COLUMNS FROM weekly_income_reports LIKE '{$col}'");
            if ($colCheck->rowCount() > 0) {
                echo "<p class='success'>✅ Column '{$col}' exists</p>";
            } else {
                echo "<p class='error'>❌ Column '{$col}' missing</p>";
            }
        }
    } else {
        echo "<p class='error'>❌ weekly_income_reports table does not exist</p>";
    }
    echo "</div>";
    
    echo "<div class='check-section'>";
    echo "<div class='check-header'><h3>📋 Weekly Income Logs Table</h3></div>";
    
    // Check if weekly_income_logs table exists
    $tableCheck2 = $db->query("SHOW TABLES LIKE 'weekly_income_logs'");
    if ($tableCheck2->rowCount() > 0) {
        echo "<p class='success'>✅ weekly_income_logs table exists</p>";
        
        // Get table structure
        $columns2 = $db->query("DESCRIBE weekly_income_logs");
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($column = $columns2->fetch()) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for required columns
        $requiredColumns2 = ['report_sequence', 'processing_type', 'report_id'];
        foreach ($requiredColumns2 as $col) {
            $colCheck = $db->query("SHOW COLUMNS FROM weekly_income_logs LIKE '{$col}'");
            if ($colCheck->rowCount() > 0) {
                echo "<p class='success'>✅ Column '{$col}' exists</p>";
            } else {
                echo "<p class='error'>❌ Column '{$col}' missing</p>";
            }
        }
        
        // Check constraints
        $constraints = $db->query("
            SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE 
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'weekly_income_logs'
        ");
        echo "<p><strong>Constraints:</strong></p>";
        echo "<ul>";
        while ($constraint = $constraints->fetch()) {
            echo "<li>{$constraint['CONSTRAINT_NAME']} ({$constraint['CONSTRAINT_TYPE']})</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p class='error'>❌ weekly_income_logs table does not exist</p>";
    }
    echo "</div>";
    
    echo "<div class='check-section'>";
    echo "<div class='check-header'><h3>📋 PV Usage Tracking Table</h3></div>";
    
    // Check if pv_usage_tracking table exists
    $tableCheck3 = $db->query("SHOW TABLES LIKE 'pv_usage_tracking'");
    if ($tableCheck3->rowCount() > 0) {
        echo "<p class='success'>✅ pv_usage_tracking table exists</p>";
        
        // Check sample data
        $sampleData = $db->query("SELECT COUNT(*) as count FROM pv_usage_tracking");
        $count = $sampleData->fetch()['count'];
        echo "<p>Total PV tracking records: {$count}</p>";
        
        if ($count > 0) {
            $sampleRecord = $db->query("SELECT * FROM pv_usage_tracking LIMIT 1");
            $record = $sampleRecord->fetch();
            echo "<p><strong>Sample record:</strong></p>";
            echo "<pre>" . print_r($record, true) . "</pre>";
        }
    } else {
        echo "<p class='error'>❌ pv_usage_tracking table does not exist</p>";
    }
    echo "</div>";
    
    echo "<div class='check-section'>";
    echo "<div class='check-header'><h3>📋 Users Table</h3></div>";
    
    // Check active users
    $activeUsers = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $activeCount = $activeUsers->fetch()['count'];
    echo "<p>Active users: {$activeCount}</p>";
    
    if ($activeCount > 0) {
        echo "<p class='success'>✅ Active users found for processing</p>";
    } else {
        echo "<p class='warning'>⚠️ No active users found</p>";
    }
    echo "</div>";
    
    echo "<div class='check-section'>";
    echo "<div class='check-header'><h3>📋 Configuration Check</h3></div>";
    
    // Check configuration
    $configCheck = $db->query("SELECT * FROM config WHERE config_key IN ('incremental_processing_enabled', 'weekly_processing_day', 'week_definition')");
    echo "<table>";
    echo "<tr><th>Config Key</th><th>Value</th><th>Type</th></tr>";
    while ($config = $configCheck->fetch()) {
        echo "<tr>";
        echo "<td>{$config['config_key']}</td>";
        echo "<td>{$config['config_value']}</td>";
        echo "<td>{$config['config_type']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "<div class='check-section'>";
    echo "<div class='check-header'><h3>📋 Error Log Check</h3></div>";
    
    // Check for recent errors in system_logs
    $errorCheck = $db->query("
        SELECT * FROM system_logs 
        WHERE log_type = 'error' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    $errors = $errorCheck->fetchAll();
    if (count($errors) > 0) {
        echo "<p class='warning'>⚠️ Recent errors found:</p>";
        echo "<table>";
        echo "<tr><th>Time</th><th>Category</th><th>Message</th></tr>";
        foreach ($errors as $error) {
            echo "<tr>";
            echo "<td>{$error['created_at']}</td>";
            echo "<td>{$error['category']}</td>";
            echo "<td>" . htmlspecialchars($error['message']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='success'>✅ No recent errors found</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='check-section'>";
    echo "<div class='check-header'><h3>❌ Error</h3></div>";
    echo "<p class='error'>Error checking database structure: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
