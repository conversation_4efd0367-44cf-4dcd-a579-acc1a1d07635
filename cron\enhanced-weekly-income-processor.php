<?php
/**
 * Enhanced Weekly Income Processor
 * Production-Level Automated Income Processing System
 * 
 * Features:
 * - Comprehensive duplicate prevention
 * - Robust error handling and recovery
 * - Performance monitoring
 * - Detailed logging and audit trails
 * - Automatic failure recovery
 * - Memory and time optimization
 */

// Prevent direct browser access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

// Set error reporting and logging
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/cron_errors.log');

// Include required files
require_once __DIR__ . '/../config/Connection.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/IncomeGenerationEngine.php';
require_once __DIR__ . '/../includes/PVValidationSystem.php';
require_once __DIR__ . '/../includes/WeeklyDateHelper.php';

// Set execution limits
set_time_limit(7200); // 2 hours
ini_set('memory_limit', '1024M'); // 1GB

class EnhancedWeeklyIncomeProcessor {
    private $db;
    private $config;
    private $incomeEngine;
    private $validator;
    private $logger;
    private $startTime;
    private $processId;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
        $this->incomeEngine = new IncomeGenerationEngine();
        $this->validator = new PVValidationSystem();
        $this->startTime = microtime(true);
        $this->processId = 'weekly_' . date('Y-m-d_H-i-s') . '_' . getmypid();
        
        $this->initializeLogger();
    }
    
    /**
     * Initialize logging system
     */
    private function initializeLogger() {
        $this->logger = [
            'info' => function($message, $context = []) {
                $this->logMessage('info', $message, $context);
            },
            'error' => function($message, $context = []) {
                $this->logMessage('error', $message, $context);
            },
            'warning' => function($message, $context = []) {
                $this->logMessage('warning', $message, $context);
            }
        ];
    }
    
    /**
     * Main processing method
     */
    public function process() {
        try {
            $this->logger['info']("Starting enhanced weekly income processing", [
                'process_id' => $this->processId,
                'memory_limit' => ini_get('memory_limit'),
                'time_limit' => ini_get('max_execution_time')
            ]);
            
            // Determine processing period
            $processingPeriod = $this->determineProcessingPeriod();
            
            // Validate system state
            $this->validateSystemState();
            
            // Check if processing already completed
            if ($this->isProcessingCompleted($processingPeriod)) {
                $this->logger['info']("Processing already completed for period", [
                    'processing_period' => $processingPeriod
                ]);
                return $this->generateSuccessResponse($processingPeriod, 0, 0, 0, true);
            }
            
            // Perform pre-processing validation
            $this->performPreProcessingValidation();
            
            // Execute income processing
            $result = $this->incomeEngine->processAllUsersIncome($processingPeriod, 'weekly');
            
            // Perform post-processing validation
            $this->performPostProcessingValidation($processingPeriod);
            
            // Generate completion report
            $this->generateCompletionReport($result);
            
            $processingTime = microtime(true) - $this->startTime;
            
            $this->logger['info']("Weekly income processing completed successfully", [
                'process_id' => $this->processId,
                'processing_period' => $processingPeriod,
                'total_time_seconds' => round($processingTime, 2),
                'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'result' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            $this->handleProcessingError($e);
            throw $e;
        }
    }
    
    /**
     * Determine the processing period (Saturday-Friday week start date)
     */
    private function determineProcessingPeriod() {
        // Get the Saturday of last week (Saturday-Friday weeks)
        $previousWeek = WeeklyDateHelper::getPreviousWeek();
        $lastSaturday = $previousWeek['start'];

        // Check if we should process - runs on Friday at 11:59 PM
        $processingDay = WeeklyDateHelper::getProcessingDay(); // 5 = Friday
        $currentDayOfWeek = (int) date('w'); // 0 = Sunday, 1 = Monday, etc.

        if ($currentDayOfWeek === $processingDay) {
            return $lastSaturday;
        } else {
            throw new Exception("Not the designated processing day. Current day: {$currentDayOfWeek}, Processing day: {$processingDay} (Friday)");
        }
    }
    
    /**
     * Validate system state before processing
     */
    private function validateSystemState() {
        // Check database connectivity
        try {
            $this->db->query("SELECT 1");
        } catch (Exception $e) {
            throw new Exception("Database connectivity check failed: " . $e->getMessage());
        }
        
        // Check system health
        $healthCheck = $this->validator->validateDataIntegrity();
        if (!$healthCheck['healthy']) {
            $this->logger['warning']("System health issues detected", $healthCheck['issues']);
            
            // Decide whether to proceed or abort based on severity
            $criticalIssues = $this->assessCriticalIssues($healthCheck['issues']);
            if ($criticalIssues) {
                throw new Exception("Critical system health issues prevent processing: " . json_encode($criticalIssues));
            }
        }
        
        // Check for active processing locks
        $activeLocks = $this->checkActiveProcessingLocks();
        if (!empty($activeLocks)) {
            throw new Exception("Active processing locks detected: " . json_encode($activeLocks));
        }
        
        // Check available disk space
        $freeSpace = disk_free_space(__DIR__);
        $requiredSpace = 100 * 1024 * 1024; // 100MB minimum
        
        if ($freeSpace < $requiredSpace) {
            throw new Exception("Insufficient disk space. Available: " . round($freeSpace / 1024 / 1024, 2) . "MB, Required: " . round($requiredSpace / 1024 / 1024, 2) . "MB");
        }
    }
    
    /**
     * Check if processing already completed
     */
    private function isProcessingCompleted($processingPeriod) {
        $stmt = $this->db->prepare("
            SELECT id, status FROM income_processing_status 
            WHERE processing_type = 'weekly' AND processing_period = ?
        ");
        
        $stmt->execute([$processingPeriod]);
        $status = $stmt->fetch();
        
        return $status && $status['status'] === 'completed';
    }
    
    /**
     * Perform pre-processing validation
     */
    private function performPreProcessingValidation() {
        // Validate PV data consistency
        $pvValidation = $this->validator->validateDataIntegrity();
        if (!$pvValidation['healthy']) {
            $this->logger['warning']("PV data inconsistencies detected", $pvValidation['issues']);
        }
        
        // Check for pending PV transactions
        $stmt = $this->db->prepare("SELECT COUNT(*) as pending_count FROM pv_transactions WHERE processing_status = 'pending'");
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['pending_count'] > 0) {
            $this->logger['info']("Found {$result['pending_count']} pending PV transactions that will be processed");
        }
    }
    
    /**
     * Perform post-processing validation
     */
    private function performPostProcessingValidation($processingPeriod) {
        // Validate income distribution
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as processed_users, SUM(income_amount) as total_income
            FROM weekly_income_logs 
            WHERE week_start_date = ?
        ");
        
        $stmt->execute([$processingPeriod]);
        $result = $stmt->fetch();
        
        $this->logger['info']("Post-processing validation", [
            'processed_users' => $result['processed_users'],
            'total_income_distributed' => $result['total_income']
        ]);
        
        // Check for data integrity issues after processing
        $integrityCheck = $this->validator->validateDataIntegrity();
        if (!$integrityCheck['healthy']) {
            $this->logger['error']("Data integrity issues detected after processing", $integrityCheck['issues']);
        }
    }
    
    /**
     * Generate completion report
     */
    private function generateCompletionReport($result) {
        $reportData = [
            'process_id' => $this->processId,
            'processing_date' => date('Y-m-d H:i:s'),
            'processing_period' => $result['processing_period'],
            'total_users' => $result['total_users'],
            'processed_users' => $result['processed_users'],
            'users_with_income' => $result['users_with_income'],
            'total_income_distributed' => $result['total_income_distributed'],
            'total_capping_applied' => $result['total_capping_applied'],
            'processing_time_seconds' => $result['processing_time_seconds'],
            'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'errors_count' => $result['errors_count'] ?? 0
        ];
        
        // Save report to file
        $reportFile = __DIR__ . '/../logs/weekly_income_reports/report_' . $result['processing_period'] . '.json';
        $reportDir = dirname($reportFile);
        
        if (!is_dir($reportDir)) {
            mkdir($reportDir, 0755, true);
        }
        
        file_put_contents($reportFile, json_encode($reportData, JSON_PRETTY_PRINT));
        
        $this->logger['info']("Completion report generated", [
            'report_file' => $reportFile,
            'report_data' => $reportData
        ]);
    }
    
    /**
     * Handle processing errors
     */
    private function handleProcessingError($e) {
        $errorData = [
            'process_id' => $this->processId,
            'error_message' => $e->getMessage(),
            'error_trace' => $e->getTraceAsString(),
            'memory_usage' => memory_get_usage(true),
            'processing_time' => microtime(true) - $this->startTime
        ];
        
        $this->logger['error']("Weekly income processing failed", $errorData);
        
        // Save error report
        $errorFile = __DIR__ . '/../logs/weekly_income_errors/error_' . date('Y-m-d_H-i-s') . '.json';
        $errorDir = dirname($errorFile);
        
        if (!is_dir($errorDir)) {
            mkdir($errorDir, 0755, true);
        }
        
        file_put_contents($errorFile, json_encode($errorData, JSON_PRETTY_PRINT));
    }
    
    /**
     * Assess critical issues that would prevent processing
     */
    private function assessCriticalIssues($issues) {
        $criticalIssues = [];

        foreach ($issues as $category => $categoryIssues) {
            foreach ($categoryIssues as $issue) {
                // Define critical patterns
                if (strpos($issue, 'negative') !== false ||
                    strpos($issue, 'inconsistent') !== false ||
                    strpos($issue, 'orphaned') !== false) {
                    $criticalIssues[] = "{$category}: {$issue}";
                }
            }
        }

        return $criticalIssues;
    }

    /**
     * Check for active processing locks
     */
    private function checkActiveProcessingLocks() {
        $stmt = $this->db->prepare("
            SELECT lock_type, lock_key, locked_by, locked_at
            FROM pv_processing_locks
            WHERE status = 'active' AND expires_at > NOW()
        ");

        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Generate success response
     */
    private function generateSuccessResponse($processingPeriod, $totalUsers, $processedUsers, $totalIncome, $alreadyCompleted = false) {
        return [
            'status' => $alreadyCompleted ? 'already_completed' : 'completed',
            'processing_period' => $processingPeriod,
            'total_users' => $totalUsers,
            'processed_users' => $processedUsers,
            'total_income_distributed' => $totalIncome,
            'processing_time_seconds' => microtime(true) - $this->startTime
        ];
    }

    /**
     * Log messages to system
     */
    private function logMessage($level, $message, $context = []) {
        $context['process_id'] = $this->processId;

        // Console output
        $timestamp = date('Y-m-d H:i:s');
        echo "[{$timestamp}] [{$level}] {$message}\n";

        // Database logging
        try {
            // Try new schema first
            $stmt = $this->db->prepare("
                INSERT INTO system_logs (log_type, category, message, context, processing_period)
                VALUES (?, 'weekly_income_processing', ?, ?, ?)
            ");

            $stmt->execute([
                $level,
                $message,
                json_encode($context),
                $context['processing_period'] ?? null
            ]);
        } catch (Exception $e) {
            // Fall back to old schema
            try {
                $stmt = $this->db->prepare("
                    INSERT INTO system_logs (log_type, message)
                    VALUES (?, ?)
                ");

                $fullMessage = "[weekly_income_processing] {$message}";
                if (!empty($context)) {
                    $fullMessage .= " - Context: " . json_encode($context);
                }

                $stmt->execute([$level, $fullMessage]);
            } catch (Exception $e2) {
                // Fallback to file logging if database fails
                error_log("[{$level}] {$message} - Context: " . json_encode($context));
            }
        }
    }
}

// Main execution
try {
    // Create log directories if they don't exist
    $logDirs = [
        __DIR__ . '/../logs/weekly_income_reports',
        __DIR__ . '/../logs/weekly_income_errors'
    ];

    foreach ($logDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }

    // Execute the processor
    $processor = new EnhancedWeeklyIncomeProcessor();
    $result = $processor->process();

    // Output final result
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "WEEKLY INCOME PROCESSING COMPLETED\n";
    echo str_repeat("=", 50) . "\n";
    echo "Status: " . $result['status'] . "\n";
    echo "Processing Period: " . $result['processing_period'] . "\n";
    echo "Total Users: " . $result['total_users'] . "\n";
    echo "Processed Users: " . $result['processed_users'] . "\n";
    echo "Users with Income: " . ($result['users_with_income'] ?? 0) . "\n";
    echo "Total Income Distributed: ₹" . number_format($result['total_income_distributed'] ?? 0, 2) . "\n";
    echo "Processing Time: " . $result['processing_time_seconds'] . " seconds\n";
    echo str_repeat("=", 50) . "\n";

    exit(0);

} catch (Exception $e) {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "WEEKLY INCOME PROCESSING FAILED\n";
    echo str_repeat("=", 50) . "\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Time: " . date('Y-m-d H:i:s') . "\n";
    echo str_repeat("=", 50) . "\n";

    // Log to error file
    error_log("Weekly income processing failed: " . $e->getMessage() . "\n" . $e->getTraceAsString());

    exit(1);
}
