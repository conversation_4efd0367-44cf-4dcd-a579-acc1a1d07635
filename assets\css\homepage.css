/**
 * Homepage Styles - Modern E-commerce Design
 * Responsive design inspired by modern tech e-commerce sites
 */

:root {
    /* RootPure Color Palette */
    --primary-green: #3bb77e;
    --primary-light: #ffffff;
    --primary-dark: #2d8f5f;
    --secondary-blue: #29a9dc;
    --accent-orange: #f97316;
    --text-dark: #1f2937;
    --text-gray-light: #6b7280;
    --text-gray-dark: #374151;
    --background-light: #f8fafc;
    --background-card: #ffffff;
    --border-color: #e5e7eb;
    --border-light: #f0e9ff;

    /* RootPure Gradients */
    --gradient-primary: linear-gradient(135deg, #3bb77e 0%, #2d8f5f 100%);
    --gradient-hero: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);

    /* Spacing */
    --container-max-width: 1400px;
    --section-padding: 80px 0;
    --card-border-radius: 16px;
    --button-border-radius: 12px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--background-light);
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles - RootPure Design */
.top-header {
    background: var(--primary-green);
    border-bottom: 1px solid var(--border-color);
    padding: 8px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.top-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-header-left {
    font-size: 13px;
    color: white;
    font-weight: 500;
}

.top-header-right {
    display: flex;
    gap: 25px;
    align-items: center;
}

.top-header-right a {
    color: white;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.top-header-right a:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* Main Header */
.main-header {
    background: var(--primary-light);
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
}

.logo {
    font-size: 28px;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
}

.logo1{
    height: 50px;
    width: auto;
}

.search-bar {
    flex: 1;
    max-width: 500px;
}

.search-bar form {
    width: 100%;
}

.search-bar input {
    width: 100%;
    padding: 14px 50px 14px 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--button-border-radius);
    background: var(--background-card);
    color: var(--text-dark);
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(59, 183, 126, 0.1);
}

.search-bar input::placeholder {
    color: var(--text-gray-light);
}

.search-bar button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--gradient-primary);
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-bar button:hover {
    transform: translateY(-50%) scale(1.05);
}

.header-actions {
    display: flex;
    gap: 20px;
    align-items: center;
}

.header-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-gray-light);
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 8px;
}

.header-btn:hover {
    color: var(--text-dark);
    background: rgba(0, 0, 0, 0.05);
}

.header-btn i {
    font-size: 20px;
    margin-bottom: 4px;
}

.header-btn span {
    font-size: 12px;
    font-weight: 500;
}

/* Navigation - RootPure Style */
.navigation {
    background: var(--primary-light);
    padding: 0;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-content {
    display: flex;
    align-items: center;
    gap: 40px;
}

.categories-btn {
    background: var(--primary-green);
    color: white;
    border: none;
    padding: 16px 24px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 14px;
}

.categories-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-menu a {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    padding: 16px 0;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-green);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-green);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Hero Banner Section - RootPure Style */
.hero-banner-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 60px 0;
    position: relative;
    overflow: hidden;
}

.hero-slider {
    position: relative;
}

.hero-slide {
    display: none;
}

.hero-slide.active {
    display: block;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
    color: var(--text-dark);
}

.hero-text .text-green {
    color: var(--primary-green);
}

.hero-text p {
    font-size: 18px;
    color: var(--text-gray-light);
    margin-bottom: 30px;
    line-height: 1.6;
}

.btn-shop-now {
    background: var(--primary-green);
    color: white;
    padding: 14px 28px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    display: inline-block;
    text-transform: uppercase;
}

.btn-shop-now:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 183, 126, 0.3);
}

.btn-primary-hero {
    background: var(--gradient-primary);
    color: white;
    padding: 16px 32px;
    border-radius: var(--button-border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary-hero:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(30, 64, 175, 0.4);
    color: white;
}

.btn-secondary-hero {
    background: transparent;
    color: var(--text-white);
    padding: 16px 32px;
    border: 2px solid var(--border-color);
    border-radius: var(--button-border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
}

.btn-secondary-hero:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--text-white);
    color: var(--text-white);
    transform: translateY(-3px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

/* Carousel Navigation */
.carousel-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
    z-index: 10;
}

.carousel-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: all;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.carousel-btn:hover {
    background: var(--primary-green);
    color: white;
    transform: scale(1.1);
}

.carousel-btn i {
    font-size: 18px;
}

/* Carousel Indicators */
.carousel-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: var(--primary-green);
    transform: scale(1.2);
}

.indicator:hover {
    background: rgba(255, 255, 255, 0.8);
}

/* Slide Transitions */
.hero-slide {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

/* Fade in animation for page load */
.hero-content,
.category-card,
.product-card {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Categories Section - RootPure Style */
.categories-section {
    padding: 60px 0;
    background: var(--background-light);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.category-card {
    background: var(--primary-light);
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-green);
}

.category-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 20px;
    background: var(--primary-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: white;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    background: var(--primary-dark);
    transform: scale(1.1);
}

.category-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.category-btn {
    background: var(--primary-green);
    color: white;
    padding: 8px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    display: inline-block;
}

.category-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Section Headers - RootPure Style */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.section-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.show-all-btn {
    background: var(--primary-green);
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.show-all-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Products Section - RootPure Style */
.top-products-section {
    padding: 60px 0;
    background: var(--primary-light);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    min-height: 200px; /* Ensure minimum height */
}

/* Ensure products display properly */
.products-grid .product-card {
    display: block;
    opacity: 1;
    visibility: visible;
}

.product-card {
    background: var(--primary-light);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    position: relative;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-green);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 200px;
    background: var(--background-card);
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-dark);
    line-height: 1.4;
}

.product-price {
    margin-bottom: 15px;
}

.current-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-green);
}

.pv-value {
    font-size: 14px;
    color: var(--accent-orange);
    font-weight: 600;
    background: rgba(249, 115, 22, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
}

.original-price {
    font-size: 16px;
    color: var(--text-gray-light);
    text-decoration: line-through;
}

.product-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn-add-cart {
    background: var(--primary-green);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-add-cart:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-wishlist {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-gray-light);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-wishlist:hover {
    border-color: var(--primary-green);
    color: var(--primary-green);
}

/* Popular Products Section */
.popular-products-section {
    padding: 60px 0;
    background: var(--background-light);
}

/* Product Tabs Section */
.product-tabs-section {
    padding: 60px 0;
    background: var(--primary-light);
}

.tab-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 30px;
    text-align: center;
}

.no-products {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-gray-light);
}

.no-products i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-products h4 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-dark);
}

/* Footer - RootPure Style */
.footer {
    background: var(--text-dark);
    color: white;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 15px;
}

.footer-contact p {
    margin-bottom: 8px;
    font-size: 14px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 14px;
}

.footer-section ul li a:hover {
    color: var(--primary-green);
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--background-card);
    border-radius: 50%;
    color: var(--text-gray-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-green);
    color: white;
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 30px;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 15px;
    }

    .hero-content {
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .top-header-right {
        gap: 15px;
    }

    .top-header-right a {
        font-size: 12px;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
    }

    .search-bar {
        order: 3;
        max-width: 100%;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .nav-content {
        flex-direction: column;
        gap: 0;
    }

    .categories-btn {
        width: 100%;
        justify-content: center;
        padding: 12px 20px;
        font-size: 13px;
    }

    .nav-menu {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
        padding: 15px 0;
    }

    .nav-menu a {
        padding: 12px 0;
        font-size: 14px;
    }

    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .category-card {
        padding: 25px 15px;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .section-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .section-title {
        font-size: 24px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .top-header {
        padding: 6px 0;
    }

    .top-header-left {
        font-size: 12px;
    }

    .top-header-right {
        gap: 10px;
    }

    .top-header-right a {
        font-size: 11px;
    }

    .hero-banner-section {
        padding: 40px 0;
    }

    .hero-text h1 {
        font-size: 1.8rem;
    }

    .categories-section,
    .top-products-section,
    .popular-products-section,
    .product-tabs-section {
        padding: 40px 0;
    }

    .categories-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .category-card {
        padding: 20px 15px;
    }

    .category-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .product-info {
        padding: 15px;
    }

    .product-title {
        font-size: 14px;
    }

    .current-price {
        font-size: 16px;
    }

    .header-actions {
        gap: 8px;
    }

    .header-btn {
        padding: 6px;
    }

    .header-btn i {
        font-size: 16px;
    }

    .header-btn span {
        font-size: 10px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .footer-section h3 {
        font-size: 16px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--text-white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Additional RootPure Styles */
.text-green {
    color: var(--primary-green) !important;
}

.bg-green {
    background-color: var(--primary-green) !important;
}

.border-green {
    border-color: var(--primary-green) !important;
}

/* Hover Effects */
.hover-lift:hover {
    transform: translateY(-3px);
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }
.mb-5 { margin-bottom: 40px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }
.mt-5 { margin-top: 40px; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
a:focus {
    outline: 2px solid var(--primary-green);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .top-header,
    .navigation,
    .hero-section,
    .footer {
        display: none;
    }

    body {
        background: white;
        color: black;
    }
}
