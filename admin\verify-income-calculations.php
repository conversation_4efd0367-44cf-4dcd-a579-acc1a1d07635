<?php
/**
 * Verify Income Calculations
 * Test script to verify that income calculations are working correctly
 */

require_once '../config/Connection.php';
require_once '../includes/PVSystem.php';
require_once '../config/config.php';

// Initialize classes
$db = Database::getInstance();
$pvSystem = new PVSystem();
$config = Config::getInstance();

echo "<h2>Income Calculation Verification</h2>";

// Get configuration values
$pvRate = $config->getPVRate();
$weeklyCapping = $config->get('weekly_capping', 130000);
$serviceChargeRate = 0.10; // 10%
$tdsRate = 0.05; // 5%

echo "<h3>Configuration:</h3>";
echo "<ul>";
echo "<li>PV Rate: ₹{$pvRate} per PV</li>";
echo "<li>Weekly Capping: ₹" . number_format($weeklyCapping) . "</li>";
echo "<li>Service Charge: " . ($serviceChargeRate * 100) . "%</li>";
echo "<li>TDS Rate: " . ($tdsRate * 100) . "%</li>";
echo "</ul>";

// Test calculation scenarios
echo "<h3>Test Scenarios:</h3>";

$testScenarios = [
    ['matched_pv' => 100, 'description' => 'Small PV amount'],
    ['matched_pv' => 1000, 'description' => 'Medium PV amount'],
    ['matched_pv' => 5000, 'description' => 'Large PV amount'],
    ['matched_pv' => 10000, 'description' => 'Very large PV amount (may hit capping)']
];

foreach ($testScenarios as $scenario) {
    $matchedPV = $scenario['matched_pv'];
    $description = $scenario['description'];
    
    echo "<h4>{$description} ({$matchedPV} PV):</h4>";
    
    // Calculate gross income
    $grossIncome = $matchedPV * $pvRate;
    echo "<p>Gross Income: {$matchedPV} PV × ₹{$pvRate} = ₹" . number_format($grossIncome, 2) . "</p>";
    
    // Apply capping
    $cappingApplied = 0;
    if ($grossIncome > $weeklyCapping) {
        $cappingApplied = $grossIncome - $weeklyCapping;
        $grossIncome = $weeklyCapping;
        echo "<p style='color: orange;'>Capping Applied: ₹" . number_format($cappingApplied, 2) . "</p>";
        echo "<p>Gross Income after capping: ₹" . number_format($grossIncome, 2) . "</p>";
    }
    
    // Calculate deductions
    $serviceCharge = $grossIncome * $serviceChargeRate;
    $tdsAmount = $grossIncome * $tdsRate;
    $netIncome = $grossIncome - $serviceCharge - $tdsAmount;
    
    echo "<p>Service Charge (10%): ₹" . number_format($serviceCharge, 2) . "</p>";
    echo "<p>TDS (5%): ₹" . number_format($tdsAmount, 2) . "</p>";
    echo "<p><strong>Net Income: ₹" . number_format($netIncome, 2) . "</strong></p>";
    
    // Calculate effective rate
    $effectiveRate = $netIncome / $matchedPV;
    echo "<p>Effective Rate per PV: ₹" . number_format($effectiveRate, 2) . "</p>";
    
    echo "<hr>";
}

// Check recent income logs
echo "<h3>Recent Income Logs Sample:</h3>";
try {
    $recentLogsStmt = $db->query("
        SELECT wil.*, u.full_name 
        FROM weekly_income_logs wil 
        JOIN users u ON wil.user_id = u.user_id 
        WHERE wil.income_amount > 0 
        ORDER BY wil.created_at DESC 
        LIMIT 5
    ");
    $recentLogs = $recentLogsStmt->fetchAll();
    
    if (!empty($recentLogs)) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr>";
        echo "<th>User</th>";
        echo "<th>Week</th>";
        echo "<th>Matched PV</th>";
        echo "<th>Gross Income</th>";
        echo "<th>Service Charge</th>";
        echo "<th>TDS</th>";
        echo "<th>Net Income</th>";
        echo "<th>Capping Applied</th>";
        echo "</tr>";
        
        foreach ($recentLogs as $log) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($log['full_name']) . "</td>";
            echo "<td>" . date('M d, Y', strtotime($log['week_start_date'])) . "</td>";
            echo "<td>" . number_format($log['matched_pv'], 2) . "</td>";
            echo "<td>₹" . number_format($log['gross_income_amount'], 2) . "</td>";
            echo "<td>₹" . number_format($log['service_charge'], 2) . "</td>";
            echo "<td>₹" . number_format($log['tds_amount'], 2) . "</td>";
            echo "<td>₹" . number_format($log['income_amount'], 2) . "</td>";
            echo "<td>₹" . number_format($log['weekly_capping_applied'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No recent income logs found.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error fetching recent logs: " . $e->getMessage() . "</p>";
}

// Check wallet transactions
echo "<h3>Recent Wallet Credits Sample:</h3>";
try {
    $walletStmt = $db->query("
        SELECT wt.*, u.full_name 
        FROM wallet_transactions wt 
        JOIN users u ON wt.user_id = u.user_id 
        WHERE wt.transaction_type = 'credit' 
        AND wt.description LIKE '%Weekly PV Matching%' 
        ORDER BY wt.created_at DESC 
        LIMIT 5
    ");
    $walletTransactions = $walletStmt->fetchAll();
    
    if (!empty($walletTransactions)) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr>";
        echo "<th>User</th>";
        echo "<th>Amount</th>";
        echo "<th>Description</th>";
        echo "<th>Date</th>";
        echo "</tr>";
        
        foreach ($walletTransactions as $transaction) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($transaction['full_name']) . "</td>";
            echo "<td>₹" . number_format($transaction['amount'], 2) . "</td>";
            echo "<td>" . htmlspecialchars($transaction['description']) . "</td>";
            echo "<td>" . date('M d, Y H:i', strtotime($transaction['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No recent wallet transactions found.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error fetching wallet transactions: " . $e->getMessage() . "</p>";
}

echo "<h3>Verification Complete</h3>";
?>
