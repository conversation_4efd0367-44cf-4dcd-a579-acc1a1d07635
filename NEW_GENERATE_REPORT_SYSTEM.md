# New Generate Report System

## Overview

The new generate report system has been completely rebuilt from scratch to provide a clean, predictable PV processing system where each PV contributes to income calculation exactly once, and admins have full control over when reports are generated.

## Key Features

### ✅ One-Time PV Usage
- **No Double Counting**: Each PV transaction is used exactly once across all reports
- **FIFO Processing**: PV transactions are consumed in First-In-First-Out order
- **Comprehensive Tracking**: Every PV usage is tracked in the `pv_usage_tracking` table

### ✅ On-Demand Report Generation
- **Admin Control**: Reports can be generated at any time by admin
- **Flexible Date Ranges**: Choose any start and end date for reports
- **No Time Restrictions**: Not limited to specific weekly intervals

### ✅ Complete PV Processing
- **All Users Processed**: Every active user in the system is included
- **Unprocessed PV Only**: Only unused PV transactions are included in calculations
- **Carry Forward Logic**: Unmatched PV is carried forward to future reports

### ✅ Transparent Income Calculation
- **Clear Formula**: Income = Matched PV × PV Rate (₹0.20 per PV)
- **Proper Deductions**: 10% service charge + 5% TDS automatically calculated
- **Weekly Capping**: Configurable weekly income limit (₹130,000 default)

## How It Works

### 1. PV Collection
When generating a report, the system:
- Scans all PV transactions with `processing_status = 'pending'`
- Includes only PV that hasn't been marked as used in previous reports
- Adds carry-forward PV from the user's last report

### 2. Income Calculation
For each user:
```
Left PV = Unprocessed Left PV + Carry Forward Left PV
Right PV = Unprocessed Right PV + Carry Forward Right PV
Matched PV = MIN(Left PV, Right PV)
Gross Income = Matched PV × PV Rate (₹0.20)
```

### 3. Deductions & Capping
```
If Gross Income > Weekly Capping:
    Capping Applied = Gross Income - Weekly Capping
    Gross Income = Weekly Capping

Service Charge = Gross Income × 10%
TDS Amount = Gross Income × 5%
Net Income = Gross Income - Service Charge - TDS Amount
```

### 4. PV Consumption
- Used PV transactions are marked in `pv_usage_tracking` table
- Remaining PV becomes carry-forward for next report
- Fully used transactions get `processing_status = 'processed'`

### 5. Wallet Credit
- Net income is credited to user's wallet
- Transaction recorded in `wallet_transactions` table
- Wallet balance updated automatically

## Database Changes

### New PV Usage Tracking
The system uses the `pv_usage_tracking` table to prevent double-counting:

```sql
CREATE TABLE pv_usage_tracking (
    id INT PRIMARY KEY AUTO_INCREMENT,
    pv_transaction_id INT NOT NULL,
    user_id VARCHAR(20) NOT NULL,
    side ENUM('left', 'right', 'self') NOT NULL,
    original_amount DECIMAL(10,2) NOT NULL,
    used_amount DECIMAL(10,2) DEFAULT 0.00,
    remaining_amount DECIMAL(10,2) NOT NULL,
    week_used DATE NULL,
    processing_period VARCHAR(20) NULL,
    status ENUM('available', 'partially_used', 'fully_used') DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Enhanced Report Tables
- `weekly_income_reports`: Summary of each generated report
- `weekly_income_logs`: Individual user income details per report

## Installation Steps

### 1. Run Database Fix
First, ensure the database structure is correct:
```
http://yoursite.com/admin/fix-pv-usage-tracking.php
```

### 2. Test the System
Generate a test report from the admin panel:
```
Admin Panel → Weekly Income Reports → Generate Report
```

### 3. Verify Results
Check that:
- Users receive appropriate income based on their PV
- PV transactions are marked as processed
- Wallet balances are updated correctly
- No PV is double-counted

## Configuration

### PV Rate
Default: ₹0.20 per PV
Location: Admin Settings or `config` table

### Weekly Capping
Default: ₹130,000 per user per report
Location: Admin Settings or `config` table

### Deduction Rates
- Service Charge: 10% (hardcoded)
- TDS: 5% (hardcoded)

## Benefits

### For Admins
- **Full Control**: Generate reports whenever needed
- **Transparency**: Clear audit trail of all PV usage
- **Reliability**: No risk of double-counting or missed PV
- **Flexibility**: Choose any date range for reports

### For Users
- **Fair Processing**: All PV is eventually processed
- **Predictable Income**: Clear calculation formula
- **No Loss**: Unmatched PV carries forward to next report
- **Immediate Credit**: Income credited to wallet instantly

### For System
- **Data Integrity**: Comprehensive tracking prevents errors
- **Performance**: Efficient FIFO processing
- **Scalability**: Handles large numbers of users and transactions
- **Maintainability**: Clean, well-documented code

## Troubleshooting

### Common Issues

1. **"Table pv_usage_tracking doesn't exist"**
   - Run: `admin/fix-pv-usage-tracking.php`

2. **"No PV found for processing"**
   - Check if PV transactions exist with `processing_status = 'pending'`
   - Verify PV usage tracking is initialized

3. **"Report already exists for this week"**
   - Choose a different date range
   - Each report must have unique start date

### Verification Queries

Check PV usage tracking:
```sql
SELECT COUNT(*) FROM pv_usage_tracking WHERE status = 'available';
```

Check pending PV transactions:
```sql
SELECT COUNT(*) FROM pv_transactions WHERE processing_status = 'pending';
```

Check recent reports:
```sql
SELECT * FROM weekly_income_reports ORDER BY week_start_date DESC LIMIT 5;
```

## Support

For technical issues:
1. Check the troubleshooting section above
2. Verify database structure with fix script
3. Review system logs for detailed error messages
4. Contact system administrator if issues persist

---

**Version**: 2.0.0  
**Last Updated**: 2025-08-10  
**Compatibility**: PHP 7.4+, MySQL 5.7+
